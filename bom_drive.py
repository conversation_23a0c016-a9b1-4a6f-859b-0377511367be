#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import re
import os
import pandas as pd

ERPS = [37130, 37100]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    return value.split('-')[1] if match else ''


# 读取并物料信息Csv文件
def read_and_clean_motor_detail(file_path, df=None):
    if df is None:
        df = pd.read_csv(file_path)
    df['物料描述'] = (
        df['motorModel']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .replace(r'-XD.*', '', regex=True)
    )
    df = df.drop_duplicates(subset=['物料描述'], keep='first')
    df['invertekDriveModel'] = (
        df['invertekDriveModel']
        .fillna('')
        .apply(lambda x: x.split('/')[0] if '/' in x else x)
    )
    # df = df.explode('invertekDriveModel')
    # 清理物料描述并提取部件编号

    df['驱动器物料描述'] = (
        df['invertekDriveModel']
        .str.replace(r'-XD.*', '', regex=True)
        .str.replace(r'-FS.*', '', regex=True)
        .replace(r'MIT\.', '', regex=True)
    )
    df = df[['物料描述', '驱动器物料描述']].dropna()

    return df.groupby('物料描述').agg(set).reset_index()


def merge_bom_and_motor_detail(df_bom, df_motor_detail, erp, output_path):
    df = df_bom.merge(df_motor_detail, on='物料描述', how='left')
    with open(os.path.join(output_path, 'drive_motor_missing.txt'), 'w') as f:
        f.write(f'{erp}: ')
        f.write(','.join(df[df['驱动器物料描述'].isna()]['物料描述'].unique()))
        f.write('\n')
    df = df.explode('驱动器物料描述')
    return df


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
    df_bom = df[
        (
            (df['物料类型'] == '外购件')
            & (df['物料描述'].str.contains(TOULIAO_PREFIX, na=False))
        )
        | (
            (df['物料类型'] == '外购件')
            & (df['物料描述'].str.contains('步进驱动器', na=False))
        )
    ]

    # 清理物料描述并提取部件编号
    df_bom['物料描述'] = (
        df_bom['物料描述']
        .str.replace(f'{TOULIAO_PREFIX}_?', '', regex=True)
        .replace(r'-XD.*', '', regex=True)
        .replace(r'-FS.*', '', regex=True)
    )

    return df_bom


# 对BOM数据按物料描述进行汇总
def summarize_bom_data(df_bom):
    # 分组汇总
    df_bom = (
        df_bom.groupby('物料描述')
        .apply(lambda x: pd.Series({'总数量': x['总数量'].sum()}))
        .reset_index()
    )

    return df_bom


# 读取机电沟通Excel文件
def read_and_clean_db_file(
    file_path, mapping_df=None, output_path='tests', code_mapping=None
):
    df = pd.read_excel(file_path)
    df['部件编号'] = df['部件编号'].fillna('')
    df2 = read_and_clean_motor_detail(
        'tests/mech_motor_details.csv', mapping_df
    )

    df = df[~df['EM中文'].str.contains('蛇形', na=False)]
    df_db = df[df['类别'] == '伺服电机'][['型号', '部件编号']].rename(
        columns={'型号': '物料描述'}
    )

    # 清理物料描述
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .replace(r'-XD.*', '', regex=True)
    )
    df_db = merge_bom_and_motor_detail(
        df_db, df2, file_path[6:11], output_path
    )
    df_db['物料描述备注'] = df_db['物料描述']
    df_db['物料描述'] = df_db['驱动器物料描述'].str.strip()
    df_db['部件编号'] = df_db['部件编号'].astype(str)
    df_db['部件编号'] = df_db['部件编号'].apply(lambda x: x.zfill(2))
    # 使用映射替换两位编码为三位编码
    if code_mapping is not None:
        # 只替换存在于映射中的编码
        df_db['部件编号'] = df_db['部件编号'].apply(
            lambda x: code_mapping.get(x, x)  # 如果不存在，返回原值
        )
    return df_db


# 对数据库文件按物料描述进行汇总
def summarize_db_data(df_db):
    # 统计总数量
    df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')

    # 统计部件号数量
    df_db_2 = (
        df_db.groupby('物料描述')['部件编号']
        .apply(
            lambda x: ', '.join(
                x.value_counts().index
                + ' ('
                + x.value_counts().astype(str)
                + ')'
            )
        )
        .reset_index(name='部件号统计')
    )

    # 保留所有不同的物料描述备注
    df_db_3 = (
        df_db.groupby('物料描述')['物料描述备注']
        .apply(lambda x: ', '.join(x.drop_duplicates().astype(str)))
        .reset_index(name='物料描述备注')
    )

    # 合并所有结果
    df_merged = pd.merge(df_db_1, df_db_2, on='物料描述', how='left')
    df_result = pd.merge(df_merged, df_db_3, on='物料描述', how='left')

    return df_result


# 格式化部件号为两位数
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 解析部件号统计字符串，并返回字典
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


# 计算数量差异并添加对比结果状态列
def calculate_differences(df_bom, df_db):
    # 合并两个文件的数据
    comparison_df = pd.merge(
        df_bom[['物料描述', '总数量', '原总数量']],
        df_db[['物料描述', '总数量', '原总数量', '部件号统计', '物料描述备注']],
        on='物料描述',
        how='outer',
        suffixes=('_df_bom', '_df_db'),
    )

    # 用 NaN 值填充未匹配的数据
    comparison_df.fillna(0, inplace=True)

    # 计算数量差异
    comparison_df['数量差异'] = (
        comparison_df['总数量_df_bom'] - comparison_df['总数量_df_db']
    )

    # 添加对比结果状态列
    comparison_df['对比结果'] = comparison_df.apply(
        lambda row: 'df_bom数量更多'
        if row['数量差异'] > 0
        else 'df_db数量更多'
        if row['数量差异'] < 0
        else '数量相等',
        axis=1,
    )

    # 格式化BOM总数量，显示原始值
    comparison_df['总数量_df_bom'] = comparison_df.apply(
        lambda row: f"{int(row['总数量_df_bom'])} ({int(row['原总数量_df_bom'])})",
        axis=1,
    )

    comparison_df['总数量_df_db'] = comparison_df.apply(
        lambda row: f"{int(row['总数量_df_db'])} ({int(row['原总数量_df_db'])})",
        axis=1,
    )

    # 删除不需要的列
    comparison_df = comparison_df.drop('原总数量_df_db', axis=1)
    comparison_df = comparison_df.drop('原总数量_df_bom', axis=1)

    return comparison_df


# 生成汇总行并合并到结果DataFrame
def add_summary_row(comparison_df):
    # 需要针对总数量_df_bom列进行特殊处理，因为它包含带括号的原始值
    total_bom = 0
    for val in comparison_df['总数量_df_bom']:
        if isinstance(val, str) and '(' in val:
            # 从格式如 "100 (50)" 中提取 100
            total_bom += float(val.split('(')[0].strip())
        else:
            total_bom += float(val) if pd.notna(val) else 0

    # 获取原始BOM总数
    total_orig_bom = 0
    for val in comparison_df['总数量_df_bom']:
        if isinstance(val, str) and '(' in val:
            # 从格式如 "100 (50)" 中提取 50
            orig_val = val.split('(')[1].split(')')[0].strip()
            total_orig_bom += float(orig_val)

    total_db = 0
    for val in comparison_df['总数量_df_db']:
        if isinstance(val, str) and '(' in val:
            # 从格式如 "100 (50)" 中提取 100
            total_db += float(val.split('(')[0].strip())
        else:
            total_db += float(val) if pd.notna(val) else 0

    # 获取原始BOM总数
    total_orig_db = 0
    for val in comparison_df['总数量_df_db']:
        if isinstance(val, str) and '(' in val:
            # 从格式如 "100 (50)" 中提取 50
            orig_val = val.split('(')[1].split(')')[0].strip()
            total_orig_db += float(orig_val)
    total_diff = comparison_df['数量差异'].sum()

    # 格式化总和
    total_bom_display = f'{int(total_bom)} ({int(total_orig_bom)})'
    total_db_display = f'{int(total_db)} ({int(total_orig_db)})'

    # 创建汇总行
    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '总数量_df_bom': [total_bom_display],
            '总数量_df_db': [total_db_display],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
            '部件号统计': [''],
            '物料描述备注': [''],
        }
    )

    return pd.concat([comparison_df, summary_row], ignore_index=True)


# 高亮差异较大的物料描述
def highlight_differences(s):
    if s['物料描述'] == '总数':
        return ['background-color: lightblue'] * len(s)  # 对汇总行进行不同的高亮
    elif abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# 创建驱动器与单轴电机的匹配规则
def create_matching_rules():
    # 基于表格创建匹配规则
    matching_rules = {
        'SV630NDS2R8I': ['SV625NS1R6I', 'SV625NS2R8I'],
        'SV630NDS5R5I': ['SV625NS5R5I', 'SV625NS1R6I', 'SV625NS2R8I'],
        'SV660NDS2R8I': ['SV635NS1R6I', 'SV635NS2R8I'],
        'SV660NDS5R5I': ['SV635NS5R5I', 'SV635NS1R6I', 'SV635NS2R8I'],
        'SV670NS1R6I': ['SV635NS1R6I', 'SV660NS1R6I'],
        'SV670NS2R8I': ['SV635NS2R8I', 'SV660NS2R8I'],
        'SV670NS5R5I': ['SV635NS5R5I', 'SV660NS5R5I'],
        'SV670NT012I': ['SV635NT012I', 'SV660NT012I'],
        'SV670NT017I': ['SV635NT017I', 'SV660NT017I'],
        'SV670NT3R5I': ['SV635NT3R5I', 'SV660NT3R5I'],
        'SV670NT5R4I': ['SV635NT5R4I', 'SV660NT5R4I'],
        'SV670NT5R6I': ['SV635NT5R6I', 'SV660NT5R6I'],
        'SV670NT8R4I': ['SV635NT8R4I', 'SV660NT8R4I'],
        'SV670NT026I': ['SV635NT026I', 'SV660NT026I'],
        'SV670NT021I': ['SV635NT021I', 'SV660NT021I'],
        'SV725WBN-CCCC': [
            'SV-X4FB040A-A',
            'SV-X4FB020A-A',
            'SV-X4FB010A-A',
            'HN-Y7EB040A-S',
            'HN-Y7FB040A-S',
        ],
        'SV725WBN-FFFF': [
            'SV-X4FB075A-A',
            'SV-X4FB040A-A',
            'SV-X4FB020A-A',
            'SV-X4FB010A-A',
            'HN-Y7EB075A-S',
            'HN-Y7EB040A-S',
            'HN-Y7FB075A-S',
            'HN-Y7FB040A-S',
        ],
    }

    # 定义优先级 (数字越小，优先级越高)
    priority = {
        'SV630NDS5R5I': 3,  # 优先级
        'SV660NDS5R5I': 2,  # 优先级2
        'SV725WBN-CCCC': 2,  # 优先级2
        'SV725WBN-FFFF': 1,  # 优先级1
        'SV670NS1R6I': 1,  # 优先级1
        'SV670NS2R8I': 1,  # 优先级1
        'SV670NS5R5I': 1,  # 优先级1
        'SV670NT012I': 1,  # 优先级1
        'SV670NT017I': 1,  # 优先级1
        'SV670NT3R5I': 1,  # 优先级1
        'SV670NT5R4I': 1,  # 优先级1
        'SV670NT5R6I': 1,  # 优先级1
        'SV670NT8R4I': 1,  # 优先级1
        'SV670NT026I': 1,  # 优先级1
        'SV670NT021I': 1,  # 优先级1
    }

    # 定义双轴型号
    dual_axis = [
        'SV630NDS2R8I',
        'SV630NDS5R5I',
        'SV660NDS2R8I',
        'SV660NDS5R5I',
    ]

    # 定义四轴型号
    four_axis = [
        'SV725WBN-CCCC',
        'SV725WBN-FFFF',
    ]

    return matching_rules, priority, dual_axis, four_axis


# 重新分配数据根据匹配规则和优先级
def redistribute_data(df_bom, df_db):
    # 创建匹配规则
    matching_rules, priority, dual_axis, four_axis = create_matching_rules()

    # 保存原始BOM数量，并将双轴数量乘以2用于计算
    df_bom['原总数量'] = df_bom['总数量'].copy()
    df_db['原总数量'] = df_db['总数量'].copy()
    for drive in dual_axis:
        if drive in df_bom['物料描述'].values:
            mask = df_bom['物料描述'] == drive
            df_bom.loc[mask, '总数量'] = df_bom.loc[mask, '总数量'] * 2

    for drive in four_axis:
        if drive in df_bom['物料描述'].values:
            mask = df_bom['物料描述'] == drive
            df_bom.loc[mask, '总数量'] = df_bom.loc[mask, '总数量'] * 4

    # 按优先级排序驱动器
    drive_priority_order = sorted(
        [
            (drive, priority.get(drive, 999))
            for drive in matching_rules.keys()
            if drive in df_bom['物料描述'].values
        ],
        key=lambda x: x[1],  # 按优先级排序
    )
    # 按优先级依次分配电机
    for drive, _ in drive_priority_order:
        if drive not in df_bom['物料描述'].values:
            continue

        drive_qty = df_bom.loc[df_bom['物料描述'] == drive, '总数量'].iloc[0]
        for motor in matching_rules[drive]:
            if motor not in df_db['物料描述'].values:
                continue

            motor_qty = df_db.loc[df_db['物料描述'] == motor, '总数量'].iloc[0]
            if motor not in df_bom['物料描述'].values:
                motor_qty_bom = 0
            else:
                motor_qty_bom = df_bom.loc[
                    df_bom['物料描述'] == motor, '总数量'
                ].iloc[0]
            if drive not in df_db['物料描述'].values:
                new_row = pd.DataFrame(
                    {
                        '物料描述': [drive],
                        '总数量': [0],
                        '原总数量': [0],
                        '部件号统计': [''],
                        '物料描述备注': [''],
                    }
                )
                df_db = pd.concat([df_db, new_row], ignore_index=True)
            else:
                if (
                    df_db.loc[df_db['物料描述'] == drive, '总数量'].iloc[0]
                    >= drive_qty
                ):
                    break
            drive_qty_db = df_db.loc[df_db['物料描述'] == drive, '总数量'].iloc[0]
            if drive_qty_db >= drive_qty:
                break
            else:
                if (motor_qty - motor_qty_bom) >= (drive_qty - drive_qty_db):
                    df_db.loc[df_db['物料描述'] == motor, '总数量'] -= (
                        drive_qty - drive_qty_db
                    )
                    df_db.loc[df_db['物料描述'] == drive, '总数量'] += (
                        drive_qty - drive_qty_db
                    )
                    break
                else:
                    df_db.loc[df_db['物料描述'] == motor, '总数量'] -= (
                        motor_qty - motor_qty_bom
                    )
                    df_db.loc[df_db['物料描述'] == drive, '总数量'] += (
                        motor_qty - motor_qty_bom
                    )

    return df_bom, df_db


# 主流程
def main():
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(
        '/home/<USER>/code/bom-analysis/tests/37807/37807-BOM.xlsx'
    )
    df_bom = summarize_bom_data(df_bom)

    # 读取并处理第二个文件
    df_db = read_and_clean_db_file(
        '/home/<USER>/code/bom-analysis/tests/37807/37807机电沟通表.xlsx'
    )
    df_db_2 = df_db[['物料描述', '物料描述备注']]

    drives_need_to_add = []
    drives_in_bom = set(df_bom['物料描述'])
    for i in df_db_2['物料描述备注'].unique():
        drives_in_db = df_db_2[df_db_2['物料描述备注'] == i]['物料描述'].unique()
        if pd.isna(drives_in_db).all():
            continue
        is_in_bom = False
        for j in drives_in_db:
            if j in drives_in_bom:
                is_in_bom = True
                break
        if not is_in_bom:
            drives_need_to_add.extend(list(drives_in_db))

    regex_pattern = '|'.join(df_bom['物料描述'])
    df_db = df_db[
        df_db['物料描述'].str.contains(regex_pattern, regex=True)
        | df_db['物料描述'].isin(drives_need_to_add)
    ]

    df_db = summarize_db_data(df_db)

    # 在merge后重新分配数据
    df_bom, df_db = redistribute_data(df_bom, df_db)

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_bom, df_db)

    # 添加汇总行
    comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        'tests/comparison_result_of_drive_motor.xlsx', index=False
    )


def run(bom_file, db_file, mapping_df, output_path, code_mapping=None):
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(bom_file)
    df_bom = summarize_bom_data(df_bom)

    # 读取并处理第二个文件
    df_db = read_and_clean_db_file(
        db_file, mapping_df, output_path, code_mapping
    )
    df_db_2 = df_db[['物料描述', '物料描述备注']]

    drives_need_to_add = []
    drives_in_bom = set(df_bom['物料描述'])
    for i in df_db_2['物料描述备注'].unique():
        drives_in_db = df_db_2[df_db_2['物料描述备注'] == i]['物料描述'].unique()
        if pd.isna(drives_in_db).all():
            continue
        is_in_bom = False
        for j in drives_in_db:
            if j in drives_in_bom:
                is_in_bom = True
                break
        if not is_in_bom:
            drives_need_to_add.extend(list(drives_in_db))

    regex_pattern = '|'.join(df_bom['物料描述'])
    df_db = df_db[
        df_db['物料描述'].str.contains(regex_pattern, regex=True)
        | df_db['物料描述'].isin(drives_need_to_add)
    ]

    df_db = summarize_db_data(df_db)

    # 在merge后重新分配数据
    df_bom, df_db = redistribute_data(df_bom, df_db)

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_bom, df_db)

    # 添加汇总行
    comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        f'{output_path}/2驱动器对比_BOM_机电沟通表.xlsx', index=False
    )

    return (
        {
            'type': 'drive',
            'path': f'{output_path}/2驱动器对比_BOM_机电沟通表.xlsx',
        },
    )


if __name__ == '__main__':
    try:
        os.remove('tests/drive_motor_missing.txt')
    except FileNotFoundError:
        print('file not found')
    # for ERP in ERPS:
    #     main(ERP)
    main()

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import re
import os
import pandas as pd

ERPS = [37074]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    return value.split('-')[1] if match else ''


# 读取并清理投料信息Excel文件
def read_and_clean_cabinet_file(file_path):
    df = pd.read_excel(file_path)
    df = df[['物料编号', '型号', '总数量']]
    df = df.dropna(subset=['物料编号'])
    df = df.rename(columns={'物料编号': '物料编码', '型号': '物料描述', '总数量': '物料清单数量'})
    try:
        df['物料编码'] = df['物料编码'].astype(int).astype(str)
    except:
        df['物料编码'] = df['物料编码'].astype(str)
    df['物料清单数量'] = pd.to_numeric(df['物料清单数量'], errors='coerce').fillna(0)

    return (
        df.groupby('物料编码')
        .agg(
            {
                '物料清单数量': 'sum',
                '物料描述': 'first',
            }
        )
        .reset_index()
    )


# 对BOM数据按物料描述进行汇总
def summarize_bom_data(df_bom):
    # 分组汇总
    df_bom = (
        df_bom.groupby('物料描述')
        .apply(lambda x: pd.Series({'总数量': x['总数量'].sum()}))
        .reset_index()
    )

    return df_bom


# 对数据库文件按物料描述进行汇总
def summarize_db_data(df_db):
    df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')
    # 合并结果
    # 统计部件号数量
    df_db_2 = (
        df_db.groupby('物料描述')['部件编号']
        .apply(
            lambda x: ', '.join(
                x.value_counts().index
                + ' ('
                + x.value_counts().astype(str)
                + ')'
            )
        )
        .reset_index(name='部件号统计')
    )

    # 合并结果
    return pd.merge(df_db_1, df_db_2, on='物料描述', how='left')


# 格式化部件号为两位数
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 解析部件号统计字符串，并返回字典
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


# 计算数量差异并添加对比结果状态列
def calculate_differences(df_bom, df_db):
    # 合并两个文件的数据
    comparison_df = pd.merge(
        df_bom,
        df_db,
        on='物料编码',
        how='left',
    )

    # 用 NaN 值填充未匹配的数据
    comparison_df.fillna(0, inplace=True)

    # 计算数量差异
    comparison_df['数量差异'] = comparison_df['物料清单数量'] - comparison_df['投料总数量']

    # 添加对比结果状态列
    comparison_df['对比结果'] = comparison_df.apply(
        lambda row: '物料清单数量更多'
        if row['数量差异'] > 0
        else '投料数量更多'
        if row['数量差异'] < 0
        else '数量相等',
        axis=1,
    )

    return comparison_df


# 生成汇总行并合并到结果DataFrame
def add_summary_row(comparison_df):
    total_bom = comparison_df['物料清单数量'].sum()
    total_db = comparison_df['投料总数量'].sum()
    total_diff = comparison_df['数量差异'].sum()

    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '物料清单数量': [total_bom],
            '投料总数量': [total_db],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
        }
    )

    return pd.concat([comparison_df, summary_row], ignore_index=True)


# 高亮差异较大的物料描述
def highlight_differences(s):
    if abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df = df[
        ~(
            df['所属部件'].str.contains('-800-', na=False)
            | df['所属部件'].str.contains('-801-', na=False)
        )
    ]
    df['投料物料描述'] = df['物料描述']
    df['投料总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
    df_bom = df[['投料物料描述', '投料总数量', '物料编码']]
    try:
        df_bom['物料编码'] = df_bom['物料编码'].astype(int).astype(str)
    except:
        df_bom['物料编码'] = df_bom['物料编码'].astype(str)

    return (
        df_bom.groupby('物料编码')
        .agg(
            {
                '投料总数量': 'sum',
                '投料物料描述': 'first',
            }
        )
        .reset_index()
    )


# 主流程
def main(ERP):
    # 读取并处理第一个文件
    df_boom = read_and_clean_cabinet_file(
        '/home/<USER>/code/bom-analysis/tests/38147/38147-柜外清单.xlsx'
    )
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(
        '/home/<USER>/code/bom-analysis/tests/38147/38147-b.xlsx'
    )

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_boom, df_bom)

    # 添加汇总行
    # comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(f'tests/{ERP}柜外物料对比结果.xlsx', index=False)


def run(bom_file, db_file, mapping_df, output_path):
    # 读取并处理第一个文件
    df_boom = read_and_clean_cabinet_file(bom_file)
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(db_file)

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_boom, df_bom)

    # 添加汇总行
    # comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        f'{output_path}/7柜外物料对比_BOM_柜外清单.xlsx', index=False
    )
    return (
        {'type': 'cabinet', 'path': f'{output_path}/7柜外物料对比_BOM_柜外清单.xlsx'},
    )


if __name__ == '__main__':
    try:
        os.remove('tests/drive_motor_missing.txt')
    except FileNotFoundError:
        print('file not found')
    # for ERP in ERPS:
    main('37956')

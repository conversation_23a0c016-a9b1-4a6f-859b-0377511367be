from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from core.config import settings
from models import *  # noqa

# 创建数据库引擎，增加连接池和超时配置
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=settings.DB_POOL_SIZE,  # 连接池大小
    max_overflow=settings.DB_MAX_OVERFLOW,  # 最大溢出连接数
    pool_timeout=settings.DB_POOL_TIMEOUT,  # 获取连接的超时时间
    pool_recycle=settings.DB_POOL_RECYCLE,  # 连接回收时间
    pool_pre_ping=True,  # 连接前ping测试
    echo=False,  # 设置为True可以看到SQL语句
)
SessionLocal = sessionmaker(bind=engine)


def get_db():
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()


def init_db():

    Base.metadata.create_all(bind=engine)  # noqa

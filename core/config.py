from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # DATABASE_URL: str = 'mysql+pymysql://root:leadchina@localhost:3306/IO'
    DATABASE_URL: str = 'mysql+pymysql://root:leadchina@*************:3306/IO'

    # 任务管理配置
    TASK_TIMEOUT: int = 600  # 任务超时时间（秒）
    HEALTH_CHECK_INTERVAL: int = 60  # 健康检查间隔（秒）
    DB_RETRY_ATTEMPTS: int = 3  # 数据库重试次数
    DB_RETRY_DELAY: int = 5  # 数据库重试延迟（秒）

    # 数据库连接池配置
    DB_POOL_SIZE: int = 10  # 连接池大小
    DB_MAX_OVERFLOW: int = 20  # 最大溢出连接数
    DB_POOL_TIMEOUT: int = 30  # 获取连接超时时间
    DB_POOL_RECYCLE: int = 3600  # 连接回收时间（秒）

    # 其他配置项...

    class Config:
        env_file = '.env'


settings = Settings()

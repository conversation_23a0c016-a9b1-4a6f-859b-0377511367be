#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import re
import os
import pandas as pd

ERPS = [37130]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    return value.split('-')[1] if match else ''


# 读取并清理投料信息Excel文件
def read_and_clean_boom_file(file_path):
    df = pd.read_excel(file_path)

    # 定义一个函数来处理每个单元格
    def remove_leading_single_quote(value):
        if isinstance(value, str) and value.startswith("'"):
            return value.lstrip("'")
        return value

    df['*物料编码'] = df['*物料编码'].apply(remove_leading_single_quote)

    df['物料编码'] = df['*物料编码'].astype(str)
    # 预投料名称
    # M4螺钉,终端紧固件,终端固定器,双层端子（灰色）,跨接件,多芯柔性电缆,多芯普通屏蔽电缆,多芯普通电缆,端子附件,端子,电气开关附件,插拔式桥接件,接线端子
    strings_to_exclude = [
        'M4螺钉',
        '终端紧固件',
        '终端固定器',
        '双层端子（灰色）',
        '跨接件',
        '多芯柔性电缆',
        '多芯普通屏蔽电缆',
        '多芯普通电缆',
        '端子附件',
        '端子',
        '端子台',
        '电气开关附件',
        '插拔式桥接件',
        '接线端子',
        '双层接线端子',
        '标定板',
        '视觉其他',
        '三层端子(灰色）',
        '菲林标定片',
        '直通式接地端子',
        '多芯电缆',
        '普通电缆',
        '单芯普通电缆',
    ]

    strings_to_include = ['T056（G）', 'T055']

    df['预投料型号'] = df['型号'].str.replace(r"^'+", '', regex=True)

    df['*数量'] = df['*数量'].apply(
        lambda x: re.sub(r"^'+", '', x)
        if pd.notna(x) and isinstance(x, str)
        else x
    )
    df['预投料名称'] = df['名称'].str.replace(r"^'+", '', regex=True)

    df_include = df[df['预投料型号'].isin(strings_to_include)]

    df = df[~df['预投料名称'].isin(strings_to_exclude)]

    df = pd.concat([df, df_include])

    df['预投料数量'] = pd.to_numeric(df['*数量'], errors='coerce')

    # 清理物料描述并提取部件编号
    df = df[['预投料名称', '预投料型号', '预投料数量', '物料编码']]
    return (
        df.groupby('物料编码')
        .agg({'预投料数量': 'sum', '预投料型号': 'first', '预投料名称': 'first'})
        .reset_index()
    )


# 对BOM数据按物料描述进行汇总
def summarize_bom_data(df_bom):
    # 分组汇总
    df_bom = (
        df_bom.groupby('物料描述')
        .apply(lambda x: pd.Series({'总数量': x['总数量'].sum()}))
        .reset_index()
    )

    return df_bom


# 对数据库文件按物料描述进行汇总
def summarize_db_data(df_db):
    df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')
    # 合并结果
    # 统计部件号数量
    df_db_2 = (
        df_db.groupby('物料描述')['部件编号']
        .apply(
            lambda x: ', '.join(
                x.value_counts().index
                + ' ('
                + x.value_counts().astype(str)
                + ')'
            )
        )
        .reset_index(name='部件号统计')
    )

    # 合并结果
    return pd.merge(df_db_1, df_db_2, on='物料描述', how='left')


# 格式化部件号为两位数
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 解析部件号统计字符串,并返回字典
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


# 计算数量差异并添加对比结果状态列
def calculate_differences(df_bom, df_db, df_db_all):
    # 合并两个文件的数据
    comparison_df = pd.merge(
        df_bom,
        df_db,
        on='物料编码',
        how='outer',
    )

    # 从 df_db_all 获取投料物料描述和数量信息来填充空值
    comparison_df = pd.merge(
        comparison_df,
        df_db_all[['物料编码', '投料物料描述', '投料总数量']],
        on='物料编码',
        how='left',
        suffixes=('', '_all'),
    )

    # 用 df_db_all 中的描述和数量填充空值
    comparison_df['投料物料描述'] = comparison_df['投料物料描述'].fillna(
        comparison_df['投料物料描述_all']
    )
    comparison_df['投料总数量'] = comparison_df['投料总数量'].fillna(
        comparison_df['投料总数量_all']
    )

    # 删除多余的列
    comparison_df.drop(['投料物料描述_all', '投料总数量_all'], axis=1, inplace=True)

    # # 用 0 填充其他未匹配的数据
    comparison_df['投料总数量'].fillna(0, inplace=True)
    comparison_df['预投料数量'].fillna(0, inplace=True)

    # 计算数量差异
    comparison_df['数量差异'] = comparison_df['预投料数量'] - comparison_df['投料总数量']

    # 添加对比结果状态列
    comparison_df['对比结果'] = comparison_df.apply(
        lambda row: '预投料数量更多'
        if row['数量差异'] > 0
        else '投料数量更多'
        if row['数量差异'] < 0
        else '数量相等',
        axis=1,
    )

    # 过滤掉物料编码以3开头的记录和虚拟件
    comparison_df = comparison_df[
        (~comparison_df['物料编码'].astype(str).str.startswith('3'))
        & (~comparison_df['投料物料描述'].astype(str).str.startswith('虚拟件-'))
    ]

    return comparison_df


# 生成汇总行并合并到结果DataFrame
def add_summary_row(comparison_df):
    total_bom = comparison_df['预投料数量'].sum()
    total_db = comparison_df['投料总数量'].sum()
    total_diff = comparison_df['数量差异'].sum()

    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '预投料数量': [total_bom],
            '投料总数量': [total_db],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
        }
    )

    return pd.concat([comparison_df, summary_row], ignore_index=True)


# 高亮差异较大的物料描述
def highlight_differences(s):
    if abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df['投料物料描述'] = df['物料描述']
    df['投料总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
    df_bom = df[['投料物料描述', '投料总数量', '父级', '物料编码']]
    df_bom_filter = df_bom[
        (df_bom['父级'].str.contains('-999', na=False))
        | (df_bom['父级'].str.contains('-800-', na=False))
        | (df_bom['父级'].str.contains('-801-', na=False))
        | (df_bom['父级'].str.contains('-901-', na=False))
        | (df_bom['父级'].str.contains('-910-', na=False))
    ]

    return (
        df_bom_filter.groupby('物料编码')
        .agg(
            {
                '投料总数量': 'sum',
                '投料物料描述': 'first',
            }
        )
        .reset_index()
    ), (
        df_bom.groupby('物料编码')
        .agg(
            {
                '投料总数量': 'sum',
                '投料物料描述': 'first',
            }
        )
        .reset_index()
    )


# 主流程
def main(ERP):
    # 读取并处理第一个文件
    df_boom = read_and_clean_boom_file(
        '/home/<USER>/code/bom-analysis/tests/37973/37973-EPLAN.xlsx'
    )
    # 读取并处理第一个文件
    df_bom_filter, df_bom = read_and_clean_bom_file(
        '/home/<USER>/code/bom-analysis/tests/37973/37973-BOM.xlsx'
    )

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_boom, df_bom_filter, df_bom)

    # 添加汇总行
    # comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(f'tests/{ERP}电气投料对比结果.xlsx', index=False)


def run(bom_file, db_file, mapping_df, output_path):
    # 读取并处理第一个文件
    df_boom = read_and_clean_boom_file(bom_file)
    # 读取并处理第一个文件
    df_bom_filter, df_bom = read_and_clean_bom_file(db_file)

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_boom, df_bom_filter, df_bom)

    # 添加汇总行
    # comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        f'{output_path}/5电气投料对比结果_图纸_BOM.xlsx', index=False
    )
    return (
        {'type': 'electric', 'path': f'{output_path}/5电气投料对比结果_图纸_BOM.xlsx'},
    )


if __name__ == '__main__':
    try:
        os.remove('tests/drive_motor_missing.txt')
    except FileNotFoundError:
        print('file not found')
    # for ERP in ERPS:
    #     main(ERP)
    main('37973')

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import re
import os
import itertools
import pandas as pd

ERPS = [37102]
# ERPS = [36615]
JIDIAN_PREFIX = '伺服电机'
CABLES_OR = []


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    return value.split('-')[1] if match else ''


# 读取并物料信息Csv文件
def read_and_clean_motor_detail(file_path, df=None):
    if df is None:
        df = pd.read_csv(file_path)
    # 清理物料描述并提取部件编号
    df['物料描述'] = (
        df['motorModel']
        .str.replace(f'{JIDIAN_PREFIX}_', '', regex=False)
        .str.replace(r'-XD.*', '', regex=True)
    )

    # 要处理的列
    columns_to_process = [
        'powerLineStandard',
        'powerLineFlexible',
        'encoderLineStandard',
        'encoderLineFlexible',
    ]
    # 处理每一列
    for col in columns_to_process:

        # 检查加号分隔，并扩展成刹车线列
        df[col + '（刹车线）'] = df[col].apply(
            lambda x: x.split('+')[1].strip()
            if pd.notna(x) and '+' in x
            else None
        )

        # 处理原字段，提取字符串中的字母数字三段组合（如 S6-L-B107）
        df[col] = df[col].apply(
            lambda x: x.split('-？')[0] if pd.notna(x) and '-？' in x else x
        )

        if col in ['powerLineFlexible', 'encoderLineFlexible']:
            df_or = df[df[col].str.contains('正向出线：', na=False)][col]
            for i in df_or.values:
                CABLES_OR.append(
                    [
                        j.replace('正向出线：', '').replace('向出线：', '')
                        for j in i.split('/反')
                    ]
                )
            df[col] = df[col].apply(
                lambda x: [
                    i.replace('正向出线：', '').replace('向出线：', '')
                    for i in x.split('/反')
                ]
                if pd.notna(x) and '/反' in x
                else x
            )

        # # 去除首尾空格
        # df[col] = df[col].str.strip()

    df = df.explode('powerLineFlexible').explode('encoderLineFlexible')
    for col in columns_to_process:
        df[col] = df[col].str.strip()

    # 重命名列
    renamed_columns = {
        'powerLineStandard': '动力线-普通物料描述',
        'powerLineFlexible': '动力线-柔性物料描述',
        'encoderLineStandard': '编码器线-普通物料描述',
        'encoderLineFlexible': '编码器线-柔性物料描述',
    }

    # 应用列重命名
    df.rename(columns=renamed_columns, inplace=True)

    # 根据加号生成的列名一起重命名
    for original_col in renamed_columns:
        brake_line_col = original_col + '（刹车线）'
        if brake_line_col in df.columns:
            df.rename(
                columns={
                    brake_line_col: renamed_columns[original_col] + '（刹车线）'
                },
                inplace=True,
            )
    # 移除全是 None 的列
    df = df.dropna(axis=1, how='all')
    df = df[
        [
            '物料描述',
            '动力线-普通物料描述',
            '动力线-柔性物料描述',
            '编码器线-普通物料描述',
            '编码器线-柔性物料描述',
            '动力线-普通物料描述（刹车线）',
        ]
    ]

    return df


def merge_bom_and_motor_detail(df_bom, df_motor_detail, erp, output_path):
    # 通过物料编码合并两个表
    df = pd.merge(df_bom, df_motor_detail, on='物料描述', how='left')

    # 逻辑处理
    def select_materials(row):
        # 如果是过拖链则选择普通物料描述字段，否则选择柔性物料描述字段
        if row['是否过拖链']:
            selected_materials = [row['动力线-柔性物料描述'], row['编码器线-柔性物料描述']]
        else:
            selected_materials = [
                row['动力线-普通物料描述'],
                row['编码器线-普通物料描述'],
                row['动力线-普通物料描述（刹车线）'],
            ]

        # 过滤掉 None 值并返回统计的物料描述
        return [
            material for material in selected_materials if material is not None
        ]

    # 新列：将处理后的物料描述字段整合在一起
    df['选择的物料描述'] = df.apply(select_materials, axis=1)

    # 使用 explode 将选择的物料描述展开
    df_exploded = df.explode('选择的物料描述')
    with open(os.path.join(output_path, 'cable_motor_missing.txt'), 'w') as f:
        f.write(f'{erp}: ')
        na_list = df_exploded[df_exploded['选择的物料描述'].isna()]['物料描述'].unique()
        notna_list = df_exploded[~df_exploded['选择的物料描述'].isna()][
            '物料描述'
        ].unique()
        difference_list = list(set(na_list) - set(notna_list))
        f.write(','.join(difference_list))
        f.write('\n')
    df_exploded['物料描述'] = df_exploded['选择的物料描述']
    return df_exploded


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df['型号'] = df['型号'].apply(lambda x: x.lstrip('\''))
    df['*数量'] = df['*数量'].apply(
        lambda x: x.lstrip('\'') if pd.notna(x) and isinstance(x, str) else x
    )
    df['名称'] = df['名称'].apply(lambda x: x.lstrip('\''))
    df['总数量'] = pd.to_numeric(df['*数量'], errors='coerce')
    pattern = r'伺服(动力|编码器)(柔性|标准|普通屏蔽|普通|柔性屏蔽)电缆'
    df = df[df['名称'].str.contains(pattern, regex=True)]

    # 正则表达式
    pattern = r'[A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+'

    # 使用正则表达式提取 DataFrame 中的数据
    df['物料描述'] = df['型号'].str.extract(f'({pattern})', expand=False)

    return df


# 对BOM数据按物料描述进行汇总
def summarize_bom_data(df_bom):
    def summarize_bom(group):
        bom_summary = group.groupby('部件编号')['总数量'].sum().reset_index()
        return ', '.join(
            [
                f'{row["部件编号"]} ({row["总数量"]})'
                for _, row in bom_summary.iterrows()
            ]
        )

    # 分组汇总
    df_bom = (
        df_bom.groupby('物料描述')
        .apply(lambda x: pd.Series({'总数量': x['总数量'].sum()}))
        .reset_index()
    )

    return df_bom


# 读取机电沟通Excel文件
def read_and_clean_db_file(file_path, mapping_df=None, output_path='tests'):
    df = pd.read_excel(file_path)
    df['部件编号'] = df['部件编号'].fillna('')
    df2 = read_and_clean_motor_detail(
        'tests/mech_motor_details.csv', mapping_df
    )

    df = df[~df['EM中文'].str.contains('蛇形', na=False)]
    df_db = df[df['类别'] == '伺服电机'][['型号', '部件编号', '是否过拖链']].rename(
        columns={'型号': '物料描述'}
    )

    # 转换数据类型并排序
    df_db = (
        df_db.astype({'部件编号': 'str'})
        .sort_values(by='部件编号')
        .reset_index(drop=True)
        .astype({'部件编号': 'str'})
    )

    # 清理物料描述
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .str.replace(r'-XD.*', '', regex=True)
    )
    df_db['是否过拖链'] = df_db['是否过拖链'].apply(
        lambda x: True if x == '是' else False
    )
    df_db['物料描述备注'] = df_db['物料描述']
    df_db = merge_bom_and_motor_detail(
        df_db, df2, file_path[6:11], output_path
    )
    return df_db


# 对数据库文件按物料描述进行汇总
def summarize_db_data(df_db):
    df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')

    # 统计部件号数量
    df_db_2 = (
        df_db.groupby('物料描述')['部件编号']
        .apply(
            lambda x: ', '.join(
                x.value_counts().index
                + ' ('
                + x.value_counts().astype(str)
                + ')'
            )
        )
        .reset_index(name='部件号统计')
    )

    # 合并结果
    return pd.merge(df_db_1, df_db_2, on='物料描述', how='left')


# 格式化部件号为两位数
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 解析部件号统计字符串，并返回字典
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{1,3})\s?\((\d+)\)', part_number_str)
    return {part.lstrip('0'): int(count) for part, count in parts}


# 比较两个部件号统计的差异
def compare_part_numbers(row):
    bom_parts = parse_part_numbers(row['部件号统计_df_bom'])
    db_parts = parse_part_numbers(row['部件号统计_df_db'])

    all_parts = sorted(set(bom_parts.keys()).union(db_parts.keys()))
    diff_results = []

    for part in all_parts:
        bom_count = bom_parts.get(part, 0)
        db_count = db_parts.get(part, 0)
        diff = bom_count - db_count
        if diff != 0:
            diff_results.append(f'{part} (差异: {diff})')

    return ', '.join(diff_results) if diff_results else '数量相等'


# 计算数量差异并添加对比结果状态列
def calculate_differences(df_bom, df_db):
    # 合并两个文件的数据
    comparison_df = pd.merge(
        df_bom[['物料描述', '总数量']],
        df_db[['物料描述', '总数量', '部件号统计']],
        on='物料描述',
        how='outer',
        suffixes=('_df_bom', '_df_db'),
    )

    # 用 NaN 值填充未匹配的数据
    comparison_df.fillna(0, inplace=True)

    # 计算数量差异
    comparison_df['数量差异'] = (
        comparison_df['总数量_df_bom'] - comparison_df['总数量_df_db']
    )

    # 添加对比结果状态列
    comparison_df['对比结果'] = comparison_df.apply(
        lambda row: 'df_bom数量更多'
        if row['数量差异'] > 0
        else 'df_db数量更多'
        if row['数量差异'] < 0
        else '数量相等',
        axis=1,
    )

    return comparison_df


# 生成汇总行并合并到结果DataFrame
def add_summary_row(comparison_df):
    total_bom = comparison_df['总数量_df_bom'].sum()
    total_db = comparison_df['总数量_df_db'].sum()
    total_diff = comparison_df['数量差异'].sum()

    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '总数量_df_bom': [total_bom],
            '总数量_df_db': [total_db],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
        }
    )

    return pd.concat([comparison_df, summary_row], ignore_index=True)


# 高亮差异较大的物料描述
def highlight_differences(s):
    if s['物料描述'] == '总数':
        return ['background-color: lightblue'] * len(s)  # 对汇总行进行不同的高亮
    elif abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# 主流程
def main(ERP):
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(f'tests/{ERP}投料BOOM.xlsx')
    df_bom = summarize_bom_data(df_bom)

    # 读取并处理第二个文件
    df_db = read_and_clean_db_file(f'tests/{ERP}-机电沟通表.xlsx')
    # df_db_2 = df_db[['物料描述', '物料描述备注']]
    df_db = summarize_db_data(df_db)
    cables_need_to_remove = []
    cables_in_bom = set(df_bom['物料描述'])
    for i in df_db['物料描述'].unique():
        if i not in list(itertools.chain(*CABLES_OR)):
            continue
        if i in cables_in_bom:
            for j in CABLES_OR:
                if i in j:
                    skip_idx = j.index(i)
                    for k in len(j):
                        if k != skip_idx:
                            cables_need_to_remove.append(j[1])
    df_db = df_db[~df_db['物料描述'].isin(cables_need_to_remove)]

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_bom, df_db)

    # 添加汇总行
    comparison_df = add_summary_row(comparison_df)

    # # 格式化部件号列
    # comparison_df['部件号统计_df_bom'] = comparison_df['部件号统计_df_bom'].apply(
    #     format_part_numbers
    # )
    # comparison_df['部件号统计_df_db'] = comparison_df['部件号统计_df_db'].apply(
    #     format_part_numbers
    # )

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        f'tests/{ERP}_comparison_result_of_cable_motor.xlsx', index=False
    )


if __name__ == '__main__':
    try:
        os.remove('tests/cable_motor_missing.txt')
    except FileNotFoundError:
        print('file not found')
    for ERP in ERPS:
        main(ERP)

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''=================================================
@Project -> File   ：pc_to_pi.py -> bom_cp_db
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2024/9/4 16:07
@Desc   ：
=================================================='''
import re
import pandas as pd

ERPS = [37074]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'
TYPES = ['安全门', '按钮', '按钮盒', '仪器', '传感器', '阀岛', '其它']


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    if match:
        return value.split('-')[1]  # 返回第二个部分
    return ''  # 如果不匹配，返回空字符串


# 读取并清理投料信息Excel文件
def read_and_clean_bom_motor(df, part_number):
    df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
    df_bom = df.loc[
        (df['物料类型'] == '外购件')
        & (~df['物料描述'].str.contains('虚拟件', na=False))
        & (
            df['物料描述'].str.contains('伺服电机', na=False)
            | df['物料描述'].str.contains('气缸_', na=False)
            | df['物料描述'].str.contains('传感器_', na=False)
        )
    ]

    # 修改 '物料描述' 列和提取 '部件号'
    df_bom.loc[:, '物料描述'] = (
        df_bom['物料描述']
        .str.replace('伺服电机_', '', regex=False)
        .str.replace(r'-XD.*', '', regex=True)
    )
    df_bom.loc[:, '部件编号'] = df_bom['所属部件'].apply(extract_second_part)
    df_bom = df_bom.sort_values(by='部件编号', ascending=True).reset_index(
        drop=True
    )
    df_bom = df_bom[df_bom['部件编号'] == f'{part_number}'.zfill(2)]

    # 现在处理BOM表中的重复值，通过部件编号和物料描述进行合并（sum总数量）
    df_bom_grouped = df_bom.groupby(['物料描述', '部件编号'], as_index=False).agg(
        {'总数量': 'sum'}
    )

    return df_bom_grouped[['物料描述', '部件编号', '总数量']]


# 读取机电沟通Excel文件
def read_and_clean_db_motor(df, mapping_df=None, output_path='tests'):
    df['部件编号'] = df['部件编号'].fillna('')

    df = df[~df['EM中文'].str.contains('蛇形', na=False)]
    df_db = df[
        (df['类别'] == '伺服电机') | (df['类别'] == '气缸') | (df['类别'] == '传感器')
    ][['类别', '型号', '部件编号', 'EM中文', '功能描述']].rename(columns={'型号': '物料描述'})

    # 转换数据类型并排序
    df_db['部件编号'] = df_db['部件编号'].apply(
        lambda x: str(int(x)) if isinstance(x, float) else str(x)
    )

    df_db['部件编号'] = df_db['部件编号'].apply(lambda x: x.zfill(2))

    # 清理物料描述
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .str.replace(r'-XD.*', '', regex=True)
    )
    return df_db


def merge_df_motor(df_bom, df_db):
    result_df = pd.DataFrame(
        columns=['物料描述', '部件编号', 'EM中文', '功能描述', 'BOM表物料描述']
    )

    # Step 1: 创建唯一键“物料描述-部件编号”用于匹配
    # df_bom['物料描述-部件编号'] = df_bom['物料描述'] + '-' + df_bom['部件编号'].astype(str)
    # df_db['物料描述-部件编号'] = df_db['物料描述'] + '-' + df_db['部件编号'].astype(str)

    # Step 2: 遍历数据库数据，根据唯一键检查 BOM 表
    unique_db_keys = df_db['物料描述'].unique()
    unique_bom_keys = df_bom['物料描述'].unique()

    for key in unique_db_keys:
        db_rows = df_db[df_db['物料描述'] == key]
        bom_rows = df_bom[df_bom['物料描述'] == key]

        if key not in unique_bom_keys:
            # 如果 BOM 中没有，直接添加 DB 表中的数据
            result_df = pd.concat(
                [result_df, db_rows[['物料描述', '部件编号', 'EM中文', '功能描述']]]
            )
        else:
            # 如果 BOM 中存在
            bom_count = bom_rows.iloc[0]['总数量']
            db_count = len(db_rows)

            if db_count == bom_count:
                # 情况1: DB 表中的行数等于 BOM 中的总数量
                db_rows['BOM表物料描述'] = db_rows['物料描述']
                result_df = pd.concat([result_df, db_rows])

            elif db_count > bom_count:
                # 情况2: DB 表中的行数多于 BOM 表的总数量
                data = [bom_rows.iloc[0]['物料描述']] * bom_count
                for i in range(db_count - bom_count):
                    data.append(None)
                db_rows['BOM表物料描述'] = data
                result_df = pd.concat([result_df, db_rows])
            else:
                # 情况3: DB 表中的行数少于 BOM 表的总数量
                db_rows['BOM表物料描述'] = bom_rows.iloc[0]['物料描述']
                result_df = pd.concat([result_df, db_rows])

                # 添加多余的 BOM 行
                missing_count = bom_count - db_count
                missing_rows = pd.DataFrame(
                    {
                        '物料描述': [None] * missing_count,
                        '部件编号': [bom_rows.iloc[0]['部件编号']] * missing_count,
                        'EM中文': [None] * missing_count,
                        '功能描述': [None] * missing_count,
                        'BOM表物料描述': [bom_rows.iloc[0]['物料描述']] * missing_count,
                    }
                )
                result_df = pd.concat([result_df, missing_rows])

    # Step 3: 遍历 BOM 表，添加 DB 表中不存在的 BOM 记录
    for key in unique_bom_keys:
        if key not in unique_db_keys:
            bom_row = df_bom[df_bom['物料描述'] == key].iloc[0]
            for _ in range(bom_row['总数量']):
                result_df = pd.concat(
                    [
                        result_df,
                        pd.DataFrame(
                            [
                                {
                                    '物料描述': None,
                                    '部件编号': bom_row['部件编号'],
                                    'EM中文': None,
                                    '功能描述': None,
                                    'BOM表物料描述': bom_row['物料描述'],
                                }
                            ]
                        ),
                    ]
                )

    # Reset index after concatenation
    result_df.reset_index(drop=True, inplace=True)
    if '物料描述-部件编号' in result_df.columns:
        result_df = result_df.drop('物料描述-部件编号', axis=1)
    result_df['异常'] = result_df.apply(
        lambda row: 1
        if pd.isna(row['物料描述']) or pd.isna(row['BOM表物料描述'])
        else 0,
        axis=1,
    )
    return result_df


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(df_bom, part_number):
    # df_bom = pd.read_excel(file_path)
    df_bom['总数量'] = pd.to_numeric(df_bom['总数量'], errors='coerce')
    # df_bom = df_bom.loc[
    #     (~df_bom['物料描述'].str.contains('虚拟件', na=False))
    # ]
    df_bom['所属部件'] = df_bom['所属部件'].fillna('')
    df_bom.loc[:, '部件编号'] = df_bom['所属部件'].apply(extract_second_part)
    df_bom = df_bom.sort_values(by='部件编号', ascending=True).reset_index(
        drop=True
    )
    df_bom = df_bom[df_bom['部件编号'] == f'{part_number}'.zfill(2)]

    # 现在处理BOM表中的重复值，通过部件编号和物料描述进行合并（sum总数量）
    df_bom_grouped = df_bom.groupby(['物料描述', '部件编号'], as_index=False).agg(
        {'总数量': 'sum'}
    )

    return df_bom_grouped[['物料描述', '部件编号', '总数量']]


# 读取机电沟通Excel文件
def read_and_clean_db_file(df, mapping_df=None, output_path='tests'):
    # df = pd.read_excel(file_path)
    df['部件编号'] = df['部件编号'].fillna('')

    # 获取电机数据作为过滤条件（不包含蛇形的伺服电机和气缸数据）
    df_no_snake = df[~df['EM中文'].str.contains('蛇形', na=False)]
    motor_condition = (
        (df_no_snake['类别'] == '伺服电机')
        | (df_no_snake['类别'] == '气缸')
        | (df_no_snake['类别'] == '传感器')
    )
    motor_ids = df_no_snake[motor_condition].index

    # 过滤掉电机数据和蛇形数据
    df = df[~df.index.isin(motor_ids)]

    df_db = df[['类别', '型号', '部件编号', 'EM中文', '功能描述']].rename(
        columns={'型号': '物料描述'}
    )

    # 转换数据类型并排序
    df_db['部件编号'] = df_db['部件编号'].apply(
        lambda x: str(int(x)) if isinstance(x, float) else str(x)
    )

    df_db['部件编号'] = df_db['部件编号'].apply(lambda x: x.zfill(2))

    return df_db


# 格式化部件号
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 比较两者的部件号和数量差异
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


# 高亮差异
def highlight_differences(s):
    if s['物料描述'] == '总数':
        return ['background-color: lightblue'] * len(s)
    elif abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# Step 3: 标记数量不匹配的行，如果BOM没有值标记为黄色，原表没有值标记为红色
def highlight_row(row):
    if pd.isnull(row['物料描述']) and not pd.isnull(row['BOM表物料描述']):
        return ['background-color: red'] * len(row)
    elif pd.isnull(row['BOM表物料描述']) and not pd.isnull(row['物料描述']):
        return ['background-color: yellow'] * len(row)
    return [''] * len(row)


def merge_df(df_bom, df_db):
    result_df = pd.DataFrame(
        columns=['物料描述', '部件编号', 'EM中文', '功能描述', 'BOM表物料描述']
    )

    # Step 1: 创建唯一键“物料描述-部件编号”用于匹配
    # df_db['物料描述-部件编号'] = df_db['物料描述'] + '-' + df_db['部件编号'].astype(str)

    # Step 2: 遍历数据库数据，根据唯一键检查 BOM 表
    unique_db_keys = [
        i for i in df_db['物料描述'].dropna().unique() if len(i.strip()) > 0
    ]

    for key in unique_db_keys:
        db_rows = df_db[df_db['物料描述'] == key]
        # part_num = db_rows['部件编号'].unique()[0]
        meterial_num = db_rows['物料描述'].unique()[0]

        is_contained = df_bom['物料描述'].str.contains(
            db_rows['物料描述'].unique()[0], regex=False
        )
        bom_rows = df_bom[is_contained]

        if not is_contained.any():
            # 如果 BOM 中没有，直接添加 DB 表中的数据
            result_df = pd.concat(
                [result_df, db_rows[['类别', '物料描述', '部件编号', 'EM中文', '功能描述']]]
            )
        else:
            bom_rows = bom_rows.groupby(['物料描述', '部件编号'], as_index=False).agg(
                {'总数量': 'sum'}
            )
            for _, bom_row in bom_rows.iterrows():
                if meterial_num not in bom_row['物料描述']:
                    missing_count = bom_row['总数量']
                    missing_rows = pd.DataFrame(
                        {
                            '类别': [db_rows['类别'].unique()[0]] * missing_count,
                            '物料描述': [None] * missing_count,
                            '部件编号': [bom_row['部件编号']] * missing_count,
                            'EM中文': [None] * missing_count,
                            '功能描述': [None] * missing_count,
                            'BOM表物料描述': [bom_row['物料描述']] * missing_count,
                        }
                    )
                    result_df = pd.concat([result_df, missing_rows])
                else:
                    if bom_row['总数量'] == len(db_rows):
                        db_rows['BOM表物料描述'] = bom_row['物料描述']
                        result_df = pd.concat([result_df, db_rows])
                    elif bom_row['总数量'] > len(db_rows):
                        db_rows['BOM表物料描述'] = bom_row['物料描述']
                        result_df = pd.concat([result_df, db_rows])

                        missing_count = bom_row['总数量'] - len(db_rows)
                        missing_rows = pd.DataFrame(
                            {
                                '类别': [db_rows['类别'].unique()[0]]
                                * missing_count,
                                '物料描述': [None] * missing_count,
                                '部件编号': [bom_row['部件编号']] * missing_count,
                                'EM中文': [None] * missing_count,
                                '功能描述': [None] * missing_count,
                                'BOM表物料描述': [bom_row['物料描述']] * missing_count,
                            }
                        )
                        result_df = pd.concat([result_df, missing_rows])
                    else:
                        data = [bom_row['物料描述']] * bom_row['总数量']
                        for i in range(len(db_rows) - bom_row['总数量']):
                            data.append(None)
                        db_rows['BOM表物料描述'] = data
                        result_df = pd.concat([result_df, db_rows])
    # 处理空值并筛选空描述
    empty_desc_mask = df_db['物料描述'].fillna('').str.strip().str.len() == 0
    empty_desc_rows = df_db.loc[
        empty_desc_mask, ['类别', '物料描述', '部件编号', 'EM中文', '功能描述']
    ]

    # 合并数据框
    result_df = pd.concat([result_df, empty_desc_rows])

    # Reset index after concatenation
    result_df.reset_index(drop=True, inplace=True)
    if '物料描述-部件编号' in result_df.columns:
        result_df = result_df.drop('物料描述-部件编号', axis=1)
    result_df['异常'] = result_df.apply(
        lambda row: 1
        if pd.isna(row['物料描述']) or pd.isna(row['BOM表物料描述'])
        else 0,
        axis=1,
    )

    return result_df


# 主函数，执行所有流程
def main(ERP):
    # 读取和处理 BOM 文件
    df_bom = read_and_clean_bom_file(f'tests/{ERP}投料信息.xlsx')

    # 读取和处理机电沟通表
    df_db = read_and_clean_db_file(f'tests/{ERP}-机电沟通表.xlsx')

    styled_df = merge_df(df_bom, df_db)

    styled_df.to_excel(f'tests/{ERP}物料投料信息汇总.xlsx', index=False)


def run(bom_df, db_df, parts_number, output_path):
    # 读取和处理 BOM 文件
    bom_df1 = read_and_clean_bom_motor(bom_df, parts_number)

    # 读取和处理机电沟通表
    db_df1 = read_and_clean_db_motor(db_df)
    if not db_df1.empty:
        result_df1 = merge_df_motor(bom_df1, db_df1)
    else:
        result_df1 = None

    # 读取和处理 BOM 文件
    bom_df2 = read_and_clean_bom_file(bom_df, parts_number)
    # 读取和处理机电沟通表
    db_df2 = read_and_clean_db_file(db_df)

    if not db_df2.empty:
        result_df2 = merge_df(bom_df2, db_df2)
    else:
        result_df2 = None
    if result_df1 is None:
        result_df = result_df2
    elif result_df2 is None:
        result_df = result_df1
    else:
        result_df = pd.concat([result_df1, result_df2]).reset_index(drop=True)
    result_df['异常原因'] = ''
    result_df['负责人'] = ''

    styled_df = result_df.style.apply(highlight_row, axis=1)

    # styled_df.to_excel(f'{output_path}/6机械投料校验_BOM_机电沟通表.xlsx', index=False)
    with pd.ExcelWriter(
        f'{output_path}/部件投料校验.xlsx', engine='openpyxl', mode='w'
    ) as writer:
        styled_df.to_excel(writer, index=False, encoding='utf-8')

    return (
        f'{output_path}/部件投料校验.xlsx',
        result_df['异常'].fillna(0).sum(),
        len(result_df),
    )


if __name__ == '__main__':
    # for ERP in ERPS:
    #     main(ERP)
    df = pd.read_excel('tests/32211/32211-机电沟通表20250117.xlsx')
    df = df[df['部件编号'] == '0H']
    bom_df = pd.read_excel('tests/32211/32211投料BOM.xlsx')
    run(bom_df, df, '115', 'tests/32211')

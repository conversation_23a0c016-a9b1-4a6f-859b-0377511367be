import pandas as pd
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from typing import Tuple
import os
from db_operations import get_all_airline_connectors
from core.config import settings


def highlight_differences(row):
    if row['数量差异'] != 0:
        return ['background-color: red'] * len(row)
    return [''] * len(row)


def compare_bom_with_connectors(
    bom_df: pd.DataFrame, db_session: Session, output_path: str
) -> Tuple[str, int, int]:
    """
    比较BOM表与航插数据库表，检查公母头数量是否匹配

    Args:
        bom_df (pd.DataFrame): BOM表数据
        db_session (Session): 数据库会话
        part_number (str): 部件编号
        output_path (str): 输出文件路径

    Returns:
        Tuple[str, int, int]: (输出文件路径, 不匹配数量, 总行数)
    """
    # 获取所有航插数据
    connector_records = get_all_airline_connectors(
        db_session, skip=0, limit=10000  # 获取所有记录
    )

    # 转换为DataFrame便于处理
    connector_df = pd.DataFrame([item.to_dict() for item in connector_records])

    # 处理BOM数据
    def clean_bom_data(df):
        # 提取部件编号
        df['部件编号'] = df['所属部件'].apply(
            lambda x: str(x).split('-')[1]
            if isinstance(x, str) and '-' in str(x)
            else ''
        )
        # 将总数量转换为数值
        df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')

        return df.sort_values(['物料描述']).reset_index(drop=True)

    # 清洗BOM数据
    bom_cleaned = clean_bom_data(bom_df)

    # 创建结果DataFrame，使用图片中的列名
    result_df = pd.DataFrame(
        columns=['公头编码', '公头名称', '公头数量', '母头编码', '母头名称', '母头数量', '数量差异']
    )

    # 处理每个连接器对
    for idx, connector in connector_df.iterrows():
        male_code = connector['male_connector_code']
        female_code = connector['female_connector_code']

        # 在BOM中查找公头
        male_in_bom = bom_cleaned[bom_cleaned['物料编码'] == male_code]
        male_qty = male_in_bom['总数量'].sum() if not male_in_bom.empty else 0

        # 在BOM中查找母头
        female_in_bom = bom_cleaned[bom_cleaned['物料编码'] == female_code]
        female_qty = (
            female_in_bom['总数量'].sum() if not female_in_bom.empty else 0
        )

        # 检查数量是否匹配
        if male_qty == 0 and female_qty == 0:
            continue

        # 创建新行
        new_row = {
            '公头编码': male_code,
            '公头名称': connector['male_connector_name'],
            '公头数量': male_qty,
            '母头编码': female_code,
            '母头名称': connector['female_connector_name'],
            '母头数量': female_qty,
            '数量差异': male_qty - female_qty,
        }

        result_df = pd.concat(
            [result_df, pd.DataFrame([new_row])], ignore_index=True
        )

    # 计算统计信息
    total_rows = len(result_df)
    mismatch_count = len(result_df[result_df['数量差异'] != 0])

    # 应用样式
    styled_df = result_df.style.apply(highlight_differences, axis=1)

    # 保存结果
    output_file = f'{output_path}/9航插公母头匹配检查表.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
        styled_df.to_excel(writer, index=False, encoding='utf-8')

    return output_file, mismatch_count, total_rows


def run(bom_file_path: str, output_path: str) -> Tuple[str, int, int]:
    """
    运行BOM与航插表匹配检查

    Args:
        bom_file_path (str): BOM文件路径
        db_connection_string (str): 数据库连接字符串
        part_number (str): 部件编号
        output_path (str): 输出路径

    Returns:
        Tuple[str, int, int]: (输出文件路径, 不匹配数量, 总行数)
    """
    # 加载BOM表
    bom_df = pd.read_excel(bom_file_path)

    # 创建数据库连接 - 使用MySQL
    engine = create_engine(settings.DATABASE_URL)

    # 创建会话
    from sqlalchemy.orm import sessionmaker

    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # 执行比较
        output_file, mismatch_count, total_rows = compare_bom_with_connectors(
            bom_df, session, output_path
        )

        return (
            {'type': 'airline', 'path': f'{output_path}/9航插公母头匹配检查表.xlsx'},
        )
    finally:
        # 关闭会话
        session.close()


def run_connector_comparison(
    bom_file_path: str, db_connection_string: str, output_path: str
) -> Tuple[str, int, int]:
    """
    运行BOM与航插表匹配检查

    Args:
        bom_file_path (str): BOM文件路径
        db_connection_string (str): 数据库连接字符串
        part_number (str): 部件编号
        output_path (str): 输出路径

    Returns:
        Tuple[str, int, int]: (输出文件路径, 不匹配数量, 总行数)
    """
    # 加载BOM表
    bom_df = pd.read_excel(bom_file_path)

    # 创建数据库连接 - 使用MySQL
    engine = create_engine(db_connection_string)

    # 创建会话
    from sqlalchemy.orm import sessionmaker

    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # 执行比较
        output_file, mismatch_count, total_rows = compare_bom_with_connectors(
            bom_df, session, output_path
        )

        return output_file, mismatch_count, total_rows
    finally:
        # 关闭会话
        session.close()


if __name__ == '__main__':
    # 示例用法 - MySQL连接字符串格式

    bom_file = 'tests/37555/37555正机BOM20250317.xlsx'
    db_connection = settings.DATABASE_URL
    output_dir = './tests'

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 运行比较
    output_file, mismatch_count, total_rows = run_connector_comparison(
        bom_file, db_connection, output_dir
    )

    print(f'总行数: {total_rows}')
    print(f'不匹配行数: {mismatch_count}')
    print(f'结果保存在: {output_file}')

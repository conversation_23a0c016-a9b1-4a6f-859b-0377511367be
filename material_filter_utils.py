#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
物料过滤规则工具函数
用于在BOM分析中应用数据库中的过滤规则
"""

from sqlalchemy.orm import Session
from core.database import SessionLocal
from db_operations import get_all_material_filter_rules
import pandas as pd
from typing import List, <PERSON><PERSON>


def get_material_filter_conditions(
    db: Session = None,
) -> <PERSON><PERSON>[List[str], List[str], List[str]]:
    """
    从数据库获取物料过滤条件

    Returns:
        Tuple[List[str], List[str], List[str]]: (包含规则, 排除规则, 排除物料编码)
    """
    if db is None:
        db = SessionLocal()
        should_close = True
    else:
        should_close = False

    try:
        # 获取所有活跃的过滤规则
        all_rules = get_all_material_filter_rules(db)

        include_rules = []
        exclude_rules = []
        exclude_codes = []

        for rule in all_rules:
            if rule.description.startswith('物料编码:'):
                # 特殊处理物料编码
                code = rule.description.replace('物料编码:', '')
                exclude_codes.append(code)
            elif rule.is_included:
                include_rules.append(rule.description)
            else:
                exclude_rules.append(rule.description)

        return include_rules, exclude_rules, exclude_codes

    finally:
        if should_close:
            db.close()


def apply_material_filters(
    df: pd.DataFrame, db: Session = None
) -> pd.DataFrame:
    """
    应用物料过滤规则到DataFrame

    Args:
        df: 包含物料信息的DataFrame，必须有 '物料类型', '物料描述', '物料编码' 列
        db: 数据库会话（可选）

    Returns:
        pd.DataFrame: 过滤后的DataFrame
    """

    # 获取过滤条件
    (
        include_rules,
        exclude_rules,
        exclude_codes,
    ) = get_material_filter_conditions(db)

    # 基础过滤：只保留外购件
    filtered_df = df[df['物料类型'] == '外购件'].copy()

    # 应用排除规则
    for exclude_rule in exclude_rules:
        filtered_df = filtered_df[
            ~filtered_df['物料描述'].str.contains(exclude_rule, na=False)
        ]

    # 应用包含规则（OR逻辑）
    if include_rules:
        include_condition = pd.Series(
            [False] * len(filtered_df), index=filtered_df.index
        )

        for include_rule in include_rules:
            include_condition |= filtered_df['物料描述'].str.contains(
                include_rule, na=False
            )

        filtered_df = filtered_df[include_condition]

    # 排除特定物料编码
    if exclude_codes:
        filtered_df = filtered_df[~filtered_df['物料编码'].isin(exclude_codes)]

    return filtered_df


def get_filter_summary(db: Session = None) -> dict:
    """
    获取当前过滤规则的摘要信息

    Returns:
        dict: 包含过滤规则统计信息的字典
    """
    (
        include_rules,
        exclude_rules,
        exclude_codes,
    ) = get_material_filter_conditions(db)

    return {
        'include_rules_count': len(include_rules),
        'exclude_rules_count': len(exclude_rules),
        'exclude_codes_count': len(exclude_codes),
        'total_rules': len(include_rules)
        + len(exclude_rules)
        + len(exclude_codes),
        'include_rules': include_rules,
        'exclude_rules': exclude_rules,
        'exclude_codes': exclude_codes,
    }


def validate_material_description(
    description: str, db: Session = None
) -> dict:
    """
    验证物料描述是否会被当前过滤规则包含或排除

    Args:
        description: 物料描述
        db: 数据库会话（可选）

    Returns:
        dict: 验证结果
    """
    (
        include_rules,
        exclude_rules,
        exclude_codes,
    ) = get_material_filter_conditions(db)

    result = {
        'description': description,
        'would_be_included': False,
        'matched_include_rules': [],
        'matched_exclude_rules': [],
        'final_decision': 'excluded',
    }

    # 检查排除规则
    for exclude_rule in exclude_rules:
        if exclude_rule in description:
            result['matched_exclude_rules'].append(exclude_rule)

    # 如果匹配到排除规则，直接排除
    if result['matched_exclude_rules']:
        result['final_decision'] = 'excluded_by_exclude_rules'
        return result

    # 检查包含规则
    for include_rule in include_rules:
        if include_rule in description:
            result['matched_include_rules'].append(include_rule)

    # 如果匹配到包含规则，则包含
    if result['matched_include_rules']:
        result['would_be_included'] = True
        result['final_decision'] = 'included'
    else:
        result['final_decision'] = 'excluded_no_include_match'

    return result


def test_filter_rules():
    """测试过滤规则功能"""

    print('🧪 测试物料过滤规则功能')
    print('=' * 60)

    # 获取过滤规则摘要
    summary = get_filter_summary()
    print('📊 过滤规则摘要:')
    print(f"  包含规则: {summary['include_rules_count']} 条")
    print(f"  排除规则: {summary['exclude_rules_count']} 条")
    print(f"  排除编码: {summary['exclude_codes_count']} 条")
    print(f"  总计: {summary['total_rules']} 条")

    print('\n📋 详细规则:')
    print('✅ 包含规则:')
    for rule in summary['include_rules']:
        print(f'  - {rule}')

    print('\n❌ 排除规则:')
    for rule in summary['exclude_rules']:
        print(f'  - {rule}')

    print('\n🚫 排除编码:')
    for code in summary['exclude_codes']:
        print(f'  - {code}')

    # 测试一些物料描述
    test_descriptions = [
        '伺服电机_ABC123',
        '虚拟件_TEST',
        '气缸_XYZ789',
        '读码器传感器_DEF456',
        '传感器_GHI789',
        '普通螺丝_M6',
        '光纤放大器_AMP001',
    ]

    print('\n🔍 测试物料描述:')
    for desc in test_descriptions:
        result = validate_material_description(desc)
        status = '✅ 包含' if result['would_be_included'] else '❌ 排除'
        print(f'  {status} {desc}')
        if result['matched_include_rules']:
            print(f"    匹配包含规则: {', '.join(result['matched_include_rules'])}")
        if result['matched_exclude_rules']:
            print(f"    匹配排除规则: {', '.join(result['matched_exclude_rules'])}")


if __name__ == '__main__':
    test_filter_rules()

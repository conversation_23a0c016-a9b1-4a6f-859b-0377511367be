from fastapi import APIRouter
from fastapi import (
    File,
    UploadFile,
    Query,
    Depends,
    HTTPException,
)
from fastapi.responses import FileResponse, StreamingResponse
import os
import copy
import time
from datetime import datetime
from openpyxl import load_workbook, Workbook
import zipfile
from zoneinfo import ZoneInfo
from openpyxl.utils import get_column_letter

import pandas as pd
from io import BytesIO
from sqlalchemy.orm import Session
from db_operations import (
    get_session_by_id,
    get_calculation_results_by_erp,
    add_error_count,
    # update_error_count,
    # get_error_count,
    get_latest_calculation_results,
    get_session_by_result_id,
)

from core.dependencies import get_db_dependency
from core.logger import logger

from routers.calculation import filter_part_numbers

router = APIRouter()


def copy_first_sheet_with_formatting(
    source_workbook, target_workbook, sheet_name, src_sheet_name=None
):
    if src_sheet_name:
        source_sheet = source_workbook[src_sheet_name]
    else:
        source_sheet = source_workbook.active  # Get the first (active) sheet
    target_sheet = target_workbook.create_sheet(title=sheet_name)
    # First check if columns already exist in the first row
    max_column = source_sheet.max_column
    header_row = list(source_sheet.iter_rows(min_row=1, max_row=1))[0]
    header_values = [cell.value for cell in header_row]

    # Check if all columns already exist
    abnormal_reason_exists = '异常原因' in header_values
    responsible_person_exists = '负责人' in header_values
    problem_cls_person_exists = '问题类别' in header_values
    step_person_exists = '措施' in header_values
    step_responsible_person_exists = '措施执行负责人' in header_values
    progress_person_exists = '进度' in header_values

    # If all columns exist, just copy everything as is
    if (
        abnormal_reason_exists
        and responsible_person_exists
        and problem_cls_person_exists
        and step_person_exists
        and step_responsible_person_exists
        and progress_person_exists
    ):
        # Copy all rows including formats
        for row in source_sheet.iter_rows():
            for cell in row:
                new_cell = target_sheet.cell(
                    row=cell.row, column=cell.column, value=cell.value
                )
                if cell.has_style:
                    new_cell._style = copy.copy(cell._style)
                    new_cell.font = copy.copy(cell.font)
                    new_cell.border = copy.copy(cell.border)
                    new_cell.fill = copy.copy(cell.fill)
                    new_cell.number_format = cell.number_format
                    new_cell.protection = copy.copy(cell.protection)
                    new_cell.alignment = copy.copy(cell.alignment)
    else:
        # Copy existing content with formats
        for row in source_sheet.iter_rows():
            for cell in row:
                new_cell = target_sheet.cell(
                    row=cell.row, column=cell.column, value=cell.value
                )
                if cell.has_style:
                    new_cell._style = copy.copy(cell._style)
                    new_cell.font = copy.copy(cell.font)
                    new_cell.border = copy.copy(cell.border)
                    new_cell.fill = copy.copy(cell.fill)
                    new_cell.number_format = cell.number_format
                    new_cell.protection = copy.copy(cell.protection)
                    new_cell.alignment = copy.copy(cell.alignment)

        # Add new columns only if they don't exist
        next_col = max_column + 1

        # Get style reference from the last column header
        ref_cell = source_sheet.cell(row=1, column=max_column)

        # Create a white fill
        from openpyxl.styles import PatternFill

        white_fill = PatternFill(
            start_color='FFFFFF', end_color='FFFFFF', fill_type='solid'
        )

        # Add headers for new columns
        if not abnormal_reason_exists:
            header_cell = target_sheet.cell(
                row=1, column=next_col, value='异常原因'
            )
            if ref_cell.has_style:
                header_cell.font = copy.copy(ref_cell.font)
                header_cell.border = copy.copy(ref_cell.border)
                header_cell.fill = white_fill
                header_cell.number_format = ref_cell.number_format
                header_cell.protection = copy.copy(ref_cell.protection)
                header_cell.alignment = copy.copy(ref_cell.alignment)
            next_col += 1

        if not responsible_person_exists:
            header_cell = target_sheet.cell(
                row=1, column=next_col, value='负责人'
            )
            if ref_cell.has_style:
                header_cell.font = copy.copy(ref_cell.font)
                header_cell.border = copy.copy(ref_cell.border)
                header_cell.fill = white_fill
                header_cell.number_format = ref_cell.number_format
                header_cell.protection = copy.copy(ref_cell.protection)
                header_cell.alignment = copy.copy(ref_cell.alignment)
            next_col += 1

        if not problem_cls_person_exists:
            header_cell = target_sheet.cell(
                row=1, column=next_col, value='问题类别'
            )
            if ref_cell.has_style:
                header_cell.font = copy.copy(ref_cell.font)
                header_cell.border = copy.copy(ref_cell.border)
                header_cell.fill = white_fill
                header_cell.number_format = ref_cell.number_format
                header_cell.protection = copy.copy(ref_cell.protection)
                header_cell.alignment = copy.copy(ref_cell.alignment)
            next_col += 1

        if not step_person_exists:
            header_cell = target_sheet.cell(row=1, column=next_col, value='措施')
            if ref_cell.has_style:
                header_cell.font = copy.copy(ref_cell.font)
                header_cell.border = copy.copy(ref_cell.border)
                header_cell.fill = white_fill
                header_cell.number_format = ref_cell.number_format
                header_cell.protection = copy.copy(ref_cell.protection)
                header_cell.alignment = copy.copy(ref_cell.alignment)
            next_col += 1

        if not step_responsible_person_exists:
            header_cell = target_sheet.cell(
                row=1, column=next_col, value='措施执行负责人'
            )
            if ref_cell.has_style:
                header_cell.font = copy.copy(ref_cell.font)
                header_cell.border = copy.copy(ref_cell.border)
                header_cell.fill = white_fill
                header_cell.number_format = ref_cell.number_format
                header_cell.protection = copy.copy(ref_cell.protection)
                header_cell.alignment = copy.copy(ref_cell.alignment)
            next_col += 1

        if not progress_person_exists:
            header_cell = target_sheet.cell(row=1, column=next_col, value='进度')
            if ref_cell.has_style:
                header_cell.font = copy.copy(ref_cell.font)
                header_cell.border = copy.copy(ref_cell.border)
                header_cell.fill = white_fill
                header_cell.number_format = ref_cell.number_format
                header_cell.protection = copy.copy(ref_cell.protection)
                header_cell.alignment = copy.copy(ref_cell.alignment)
            next_col += 1

        # Copy format for all rows in new columns
        if not (
            abnormal_reason_exists
            and responsible_person_exists
            and problem_cls_person_exists
            and step_person_exists
            and step_responsible_person_exists
            and progress_person_exists
        ):

            start_col = max_column + 1
            end_col = next_col  # Use the next_col value which was incremented properly above

            # Get reference cell from first data row
            ref_data_cell = (
                source_sheet.cell(row=2, column=max_column)
                if source_sheet.max_row > 1
                else None
            )

            for row in range(2, source_sheet.max_row + 1):
                for col in range(start_col, end_col):
                    new_cell = target_sheet.cell(row=row, column=col, value='')
                    if ref_data_cell and ref_data_cell.has_style:
                        new_cell.font = copy.copy(ref_data_cell.font)
                        new_cell.border = copy.copy(ref_data_cell.border)
                        new_cell.fill = white_fill
                        new_cell.number_format = ref_data_cell.number_format
                        new_cell.protection = copy.copy(
                            ref_data_cell.protection
                        )
                        new_cell.alignment = copy.copy(ref_data_cell.alignment)

    # Copy merged cells
    for merged_range in source_sheet.merged_cells.ranges:
        target_sheet.merge_cells(str(merged_range))

    # Copy column dimensions
    for column in source_sheet.column_dimensions:
        target_sheet.column_dimensions[column] = copy.copy(
            source_sheet.column_dimensions[column]
        )

    # Set width for new columns if they were added
    if not (
        abnormal_reason_exists
        and responsible_person_exists
        and problem_cls_person_exists
        and step_person_exists
        and step_responsible_person_exists
        and progress_person_exists
    ):

        ref_col_letter = get_column_letter(max_column)
        if ref_col_letter in source_sheet.column_dimensions:
            ref_width = source_sheet.column_dimensions[ref_col_letter].width
            start_col = max_column + 1
            end_col = next_col  # Use the calculated next_col value

            for col in range(start_col, end_col):
                col_letter = get_column_letter(col)
                target_sheet.column_dimensions[col_letter].width = ref_width

    # Copy row dimensions
    for row in source_sheet.row_dimensions:
        target_sheet.row_dimensions[row] = copy.copy(
            source_sheet.row_dimensions[row]
        )


@router.get('/result')
async def get_result(
    session_id: str = Query(..., description='用户会话ID'),
    db: Session = Depends(get_db_dependency),
):
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    calculation_results = get_calculation_results_by_erp(
        db, session.erp_number
    )
    if not calculation_results:
        raise HTTPException(
            status_code=400, detail='Calculation has not been performed'
        )

    user_result_dir = os.path.join(
        'results', session.erp_number, session.user_id, session.id
    )
    os.makedirs(user_result_dir, exist_ok=True)

    zip_filename = (
        f'results_{session.erp_number}_{session.user_id}_{session.id}.zip'
    )
    zip_path = os.path.join('results', zip_filename)
    with zipfile.ZipFile(zip_path, 'w') as zipf:
        for result in calculation_results:
            zipf.write(
                result.result_file_path,
                os.path.basename(result.result_file_path),
            )

    return FileResponse(
        zip_path,
        media_type='application/zip',
        filename=zip_filename,
        headers={
            'Content-Disposition': f'attachment; filename="{zip_filename}"'
        },
    )


@router.get('/combined_result')
async def get_combined_result(
    session_id: str = Query(..., description='用户会话ID'),
    db: Session = Depends(get_db_dependency),
):
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    erp_num = session.erp_number
    download_time = datetime.now(ZoneInfo('Asia/Shanghai')).strftime(
        '%Y%m%d%H%M%S'
    )

    calculation_results = get_calculation_results_by_erp(db, erp_num)
    if not calculation_results:
        raise HTTPException(
            status_code=400, detail='Calculation has not been performed'
        )

    # 先检查是否有有效的Excel文件
    valid_files = [
        result
        for result in calculation_results
        if result.result_file_path.endswith('.xlsx')
        and os.path.exists(result.result_file_path)
    ]

    if not valid_files:
        raise HTTPException(status_code=404, detail='No Excel files found')

    # 如果有有效文件，再创建Excel writer
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        for index, result in enumerate(valid_files):
            original_wb = load_workbook(result.result_file_path)
            sheet_name = os.path.splitext(
                os.path.basename(result.result_file_path)
            )[0]

            if sheet_name in writer.book.sheetnames:
                sheet_name = f'{sheet_name}_{index}'

            copy_first_sheet_with_formatting(
                original_wb, writer.book, sheet_name
            )

        # Remove default sheet
        if 'Sheet' in writer.book.sheetnames:
            del writer.book['Sheet']

    output.seek(0)
    return StreamingResponse(
        output,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={
            'Content-Disposition': f'attachment; filename={erp_num}_{download_time}_combined_results_{session_id}.xlsx'
        },
    )


@router.post('/update_results/{erp_number}')
async def update_results(
    erp_number: str,
    current_user_id: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db_dependency),
):
    # latest_session = get_latest_calculation_session(db, erp_number)
    # if not latest_session:
    #     raise HTTPException(
    #         status_code=404,
    #         detail='No calculation results found for this ERP number',
    #     )

    calculation_results = get_latest_calculation_results(db, erp_number)
    if not calculation_results:
        raise HTTPException(
            status_code=400, detail='Calculation has not been performed'
        )

    # 保存上传的文件内容到临时文件
    temp_file = BytesIO(await file.read())

    # 读取DataFrame
    df = pd.read_excel(temp_file, sheet_name=None)

    # 重新设置文件指针位置以重新读取
    temp_file.seek(0)

    # 读取带格式的Excel文件
    source_wb = load_workbook(temp_file)

    # Update each sheet in the original files
    for sheet_name, sheet_data in df.items():
        result = next(
            (
                r
                for r in calculation_results
                if os.path.splitext(os.path.basename(r.result_file_path))[0]
                == sheet_name
            ),
            None,
        )
        if result:
            if '异常原因' in sheet_data.columns:
                if '负责人' not in sheet_data.columns:
                    sheet_data['负责人'] = ''
                sheet_data.loc[
                    sheet_data['异常原因'].notna()
                    & (sheet_data['异常原因'] != '')
                    & ((sheet_data['负责人'] == '') | (sheet_data['负责人'].isna())),
                    '负责人',
                ] = current_user_id

            result_session = get_session_by_result_id(db, result.id)
            if not result_session:
                raise HTTPException(
                    status_code=404,
                    detail=f'Session not found for result {result.id}',
                )

            try:
                # 如果文件存在，尝试删除
                if os.path.exists(result.result_file_path):
                    try:
                        os.remove(result.result_file_path)
                    except (PermissionError, OSError):
                        time.sleep(1)
                        try:
                            os.remove(result.result_file_path)
                        except Exception as e:
                            raise HTTPException(
                                status_code=500,
                                detail=f'Failed to delete existing file: {str(e)}',
                            )

                os.makedirs(
                    os.path.dirname(result.result_file_path), exist_ok=True
                )

                # 获取源文件中对应sheet的格式信息
                if sheet_name not in source_wb.sheetnames:
                    raise HTTPException(
                        status_code=400,
                        detail=f'Sheet {sheet_name} not found in uploaded file',
                    )
                source_ws = source_wb[sheet_name]

                # 创建新的工作簿（只包含一个sheet）
                target_wb = Workbook()
                # 获取默认的sheet
                target_ws = target_wb.active
                # 设置sheet名称
                target_ws.title = sheet_name

                def update_worksheet_with_format(ws, df, source_ws):
                    # 写入表头
                    for col_idx, column in enumerate(df.columns, 1):
                        cell = ws.cell(row=1, column=col_idx, value=column)
                        source_cell = source_ws.cell(row=1, column=col_idx)
                        copy_cell_format(source_cell, cell)

                    # 写入数据并保持格式
                    for row_idx, row in enumerate(df.values, 2):
                        for col_idx, value in enumerate(row, 1):
                            cell = ws.cell(
                                row=row_idx, column=col_idx, value=value
                            )
                            source_cell = source_ws.cell(
                                row=row_idx, column=col_idx
                            )
                            copy_cell_format(source_cell, cell)

                    # 调整列宽为与源文件相同
                    for col_idx in range(1, ws.max_column + 1):
                        col_letter = get_column_letter(col_idx)
                        ws.column_dimensions[col_letter].width = (
                            source_ws.column_dimensions[col_letter].width
                            if col_letter in source_ws.column_dimensions
                            else 10
                        )

                def copy_cell_format(source_cell, target_cell):
                    """复制单元格格式"""
                    # 复制字体
                    if source_cell.font:
                        target_cell.font = copy.copy(source_cell.font)

                    # 复制填充
                    if source_cell.fill:
                        target_cell.fill = copy.copy(source_cell.fill)

                    # 复制边框
                    if source_cell.border:
                        target_cell.border = copy.copy(source_cell.border)

                    # 复制对齐方式
                    if source_cell.alignment:
                        target_cell.alignment = copy.copy(
                            source_cell.alignment
                        )

                    # 复制数字格式
                    target_cell.number_format = source_cell.number_format

                # 更新数据和格式
                update_worksheet_with_format(target_ws, sheet_data, source_ws)

                # 保存更新后的文件
                try:
                    target_wb.save(result.result_file_path)
                finally:
                    target_wb.close()

            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f'Failed to update file {sheet_name}: {str(e)}',
                )

            if result.calculation_type in [
                'drive',
                'motor',
                'cable',
                'bomhistorycheck',
                'electric',
                'cabinet',
                'mechcheck',
                'iotable',
                'airline',
            ]:
                if result.calculation_type == 'drive':
                    if (
                        '异常原因' in sheet_data.columns
                        and pd.notna(sheet_data['异常原因'].iloc[-1])
                        and str(sheet_data['异常原因'].iloc[-1]).strip() != ''
                    ):
                        error_count = 0
                    else:
                        error_count = abs(sheet_data['数量差异'].iloc[-1])

                    value = sheet_data['总数量_df_db'].iloc[-1]
                    if isinstance(value, str):
                        # If it's a string, apply split and strip
                        total_count = int(value.split('(')[0].strip())
                    else:
                        # If it's already a float, use it directly
                        total_count = int(value)
                    # total_count = int(
                    #     sheet_data['总数量_df_db'].iloc[-1].split('(')[0].strip()
                    # )
                elif result.calculation_type == 'cabinet':
                    # if (
                    #     '异常原因' in sheet_data.columns
                    #     and pd.notna(sheet_data['异常原因'].iloc[-1])
                    #     and str(sheet_data['异常原因'].iloc[-1]).strip() != ''
                    # ):
                    #     error_count = 0
                    # else:
                    #     error_count = abs(
                    #         sheet_data['数量差异'].iloc[-1]
                    #         if pd.notna(sheet_data['数量差异'].iloc[-1])
                    #         else 0
                    #     )
                    error_count = (
                        sheet_data.loc[
                            (sheet_data['异常原因'].isna())
                            | (sheet_data['异常原因'] == ''),
                            '数量差异',
                        ]
                        .fillna(0)
                        .abs()
                        .sum()
                    )
                    # if pd.isna(sheet_data['投料总数量'].iloc[-1]):
                    #     total_count = (
                    #         0
                    #         if pd.isna(sheet_data['物料清单数量'].iloc[-1])
                    #         else sheet_data['物料清单数量'].iloc[-1]
                    #     )
                    # else:
                    total_count = sheet_data['投料总数量'].sum()
                elif result.calculation_type == 'electric':
                    # if (
                    #     '异常原因' in sheet_data.columns
                    #     and pd.notna(sheet_data['异常原因'].iloc[-1])
                    #     and str(sheet_data['异常原因'].iloc[-1]).strip() != ''
                    # ):
                    #     error_count = 0
                    # else:
                    #     error_count = abs(
                    #         sheet_data['数量差异'].iloc[-1]
                    #         if pd.notna(sheet_data['数量差异'].iloc[-1])
                    #         else 0
                    #     )
                    error_count = (
                        sheet_data.loc[
                            (sheet_data['异常原因'].isna())
                            | (sheet_data['异常原因'] == ''),
                            '数量差异',
                        ]
                        .fillna(0)
                        .abs()
                        .sum()
                    )
                    # if pd.isna(sheet_data['投料总数量'].iloc[-1]):
                    #     total_count = (
                    #         0
                    #         if pd.isna(sheet_data['预投料数量'].iloc[-1])
                    #         else sheet_data['预投料数量'].iloc[-1]
                    #     )
                    # else:

                    total_count = sheet_data['投料总数量'].sum()

                elif result.calculation_type == 'airline':
                    error_count = (
                        sheet_data.loc[
                            (sheet_data['异常原因'].isna())
                            | (sheet_data['异常原因'] == ''),
                            '数量差异',
                        ]
                        .fillna(0)
                        .abs()
                        .sum()
                    )
                    total_count = sheet_data['公头数量'].fillna(0).sum()
                elif result.calculation_type == 'bomhistorycheck':
                    error_count = len(
                        sheet_data[
                            (
                                sheet_data['异常原因'].isna()
                                | (sheet_data['异常原因'] == '')
                            )
                            & (
                                sheet_data['物料差异'].notna()
                                & (sheet_data['物料差异'] != '')
                                & (sheet_data['物料差异'] != 0)
                            )
                        ]
                    )
                    total_count = len(sheet_data)
                else:
                    sheet_data['异常'] = pd.to_numeric(
                        sheet_data['异常'], errors='coerce'
                    )
                    error_count = (
                        sheet_data.loc[
                            (sheet_data['异常原因'].isna())
                            | (sheet_data['异常原因'] == ''),
                            '异常',
                        ]
                        .fillna(0)
                        .sum()
                    )

                    total_count = len(sheet_data)

                    if result.calculation_type in [
                        'motor',
                        'cable',
                        'mechcheck',
                    ]:
                        df_filtered = filter_part_numbers(sheet_data)
                        error_count_filtered = (
                            df_filtered.loc[
                                (sheet_data['异常原因'].isna())
                                | (sheet_data['异常原因'] == ''),
                                '异常',
                            ]
                            .fillna(0)
                            .sum()
                        )
                        total_count_filtered = len(df_filtered)
                        add_error_count(
                            db,
                            result_session.id,
                            f'{result.calculation_type}_om',
                            error_count_filtered,
                            total_count_filtered,
                        )
                error_count = 0 if pd.isna(error_count) else error_count
                add_error_count(
                    db,
                    result_session.id,
                    result.calculation_type,
                    error_count,
                    total_count,
                )

    return {'success': True, 'message': 'Results updated successfully'}


@router.get('/latest_erp_results/{erp_number}')
async def get_latest_erp_results(
    erp_number: str,
    db: Session = Depends(get_db_dependency),
):
    """
    获取当前用户选定项目的最新计算结果文件，以合并的Excel形式返回

    :param erp_number: ERP编号
    :return: 合并后的Excel文件
    """
    try:

        # latest_session = get_latest_calculation_session(db, erp_number)

        # if not latest_session:
        #     raise HTTPException(
        #         status_code=404,
        #         detail='No calculation results found for this ERP number',
        #     )

        calculation_results = get_latest_calculation_results(db, erp_number)
        if not calculation_results:
            raise HTTPException(
                status_code=400, detail='Calculation has not been performed'
            )

        # 创建一个内存中的Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            for index, result in enumerate(calculation_results):
                if result.result_file_path.endswith('.xlsx'):
                    # 读取原始Excel文件，保留格式
                    original_wb = load_workbook(result.result_file_path)

                    # 使用文件名（不包括扩展名）作为sheet名
                    sheet_name = os.path.splitext(
                        os.path.basename(result.result_file_path)
                    )[0]

                    # 如果sheet名重复，添加序号
                    if sheet_name in writer.book.sheetnames:
                        sheet_name = f'{sheet_name}_{index}'

                    # 复制第一个sheet到新的workbook，保留格式
                    copy_first_sheet_with_formatting(
                        original_wb, writer.book, sheet_name
                    )

            # 删除默认创建的Sheet
            if 'Sheet' in writer.book.sheetnames:
                del writer.book['Sheet']

        # 将结果写入到新的Excel文件
        output.seek(0)

        return StreamingResponse(
            output,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename=latest_results_{erp_number}.xlsx'
            },
        )

    except Exception as e:
        logger.error(f'Error getting latest ERP results: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving latest ERP results: {str(e)}',
        )

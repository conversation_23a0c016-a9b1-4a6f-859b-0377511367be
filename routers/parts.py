from fastapi import APIRouter
from fastapi import (
    File,
    UploadFile,
    Query,
    Depends,
    HTTPException,
)
from fastapi.responses import FileResponse
import os
from typing import Optional

from datetime import datetime


import pandas as pd
from sqlalchemy import text
from bom_parts_count_by_line import run as run_bom_parts_count_by_line
from bom_parts_count_by_line import highlight_row
from bom_two_compare import run_bom_comparison, highlight_differences
from sqlalchemy.orm import Session

from db_operations import (
    add_parts_calculation_result,
    add_parts_error_count,
    add_parts_uploaded_file,
    get_latest_parts_calculation,
    get_latest_parts_error_count,
    get_parts_error_summary,
)
from openpyxl import load_workbook
from .results import copy_first_sheet_with_formatting

from core.dependencies import get_db_dependency
from core.logger import logger

router = APIRouter()


@router.post('/api/v1/bom-analysis/parts-comparison')
async def parts_comparison(
    erp_number: str,
    part_number: str,
    bom_part_number: Optional[str] = Query(None, description='BOM部件编号'),
    user_id: str = Query(..., description='用户ID'),
    file: UploadFile = File(...),
    file_reverse: Optional[UploadFile] = File(default=None),
    db: Session = Depends(get_db_dependency),
):
    try:

        user_upload_dir = os.path.join(
            'uploads', erp_number, part_number, user_id
        )
        os.makedirs(user_upload_dir, exist_ok=True)
        file_path = os.path.join(user_upload_dir, file.filename)

        # 写入文件
        with open(file_path, 'wb') as buffer:
            content = await file.read()
            buffer.write(content)

        bom_result_file_path = None
        if file_reverse:
            user_upload_dir = os.path.join(
                'uploads', erp_number, part_number, user_id
            )
            os.makedirs(user_upload_dir, exist_ok=True)
            file_reverse_path = os.path.join(
                user_upload_dir, f'{file.filename}_reverse'
            )

            # 写入文件
            with open(file_reverse_path, 'wb') as buffer:
                content = await file_reverse.read()
                buffer.write(content)
            user_result_dir = os.path.join(
                'results', erp_number, part_number, user_id
            )
            os.makedirs(user_result_dir, exist_ok=True)
            df_bom1 = pd.read_excel(file.file)
            df_bom2 = pd.read_excel(file_reverse_path)
            (
                bom_result_file_path,
                reverse_error_count,
                reverse_total_count,
            ) = run_bom_comparison(
                df_bom1,
                df_bom2,
                bom_part_number if bom_part_number else part_number,
                user_result_dir,
            )
            add_parts_error_count(
                db=db,
                erp_number=erp_number,
                part_id=part_number,
                error_count=reverse_error_count,
                total_count=reverse_total_count,
                cal_type='reverse',
            )

        query = text(
            """
            SELECT
                id AS `序号`,
                componentEngineer AS `部件工程师`,
                revisionDate AS `修订日期`,
                partNumber AS `部件编号`,
                emCN AS `EM中文`,
                emName AS `EM名称`,
                deviceIdentifier AS `设备标识符`,
                functionDescription AS `功能描述`,
                category AS `类别`,
                brand AS `品牌`,
                model AS `型号`,
                airInterface AS `气接口`,
                initialPosition AS `初始位`,
                valveModel AS `阀型号`,
                valveIslandOrSingleValve AS `阀岛/单片阀`,
                singleOrDoubleControl AS `单/双控`,
                extensionDetection AS `伸位检测`,
                retractionDetection AS `缩位检测`,
                note1 AS `备注1`,
                note2 AS `备注2`,
                positiveLimit AS `正.极限`,
                negativeLimit AS `负.极限`,
                origin AS `原点`,
                passDragChain AS `是否过拖链`,
                reductionRatio AS `减速比`,
                `lead` AS `导程`,
                rollerDiameter AS `辊径`,
                motorRotationDirection AS `电机旋转方向`,
                inertiaRatio AS `惯量比`
            FROM inner_erp_mech_info WHERE ERP = :erp_number and is_delete = 0 and partNumber = :part_number
        """
        )
        result = db.execute(
            query, {'erp_number': erp_number, 'part_number': part_number}
        )
        data = result.fetchall()

        # 将查询结果转换为DataFrame
        df = pd.DataFrame(data)
        if df.empty:
            query = text(
                """
                SELECT
                    id AS `序号`,
                    componentEngineer AS `部件工程师`,
                    revisionDate AS `修订日期`,
                    partNumber AS `部件编号`,
                    emCN AS `EM中文`,
                    emName AS `EM名称`,
                    deviceIdentifier AS `设备标识符`,
                    functionDescription AS `功能描述`,
                    category AS `类别`,
                    brand AS `品牌`,
                    model AS `型号`,
                    airInterface AS `气接口`,
                    initialPosition AS `初始位`,
                    valveModel AS `阀型号`,
                    valveIslandOrSingleValve AS `阀岛/单片阀`,
                    singleOrDoubleControl AS `单/双控`,
                    extensionDetection AS `伸位检测`,
                    retractionDetection AS `缩位检测`,
                    note1 AS `备注1`,
                    note2 AS `备注2`,
                    positiveLimit AS `正.极限`,
                    negativeLimit AS `负.极限`,
                    origin AS `原点`,
                    passDragChain AS `是否过拖链`,
                    reductionRatio AS `减速比`,
                    `lead` AS `导程`,
                    rollerDiameter AS `辊径`,
                    motorRotationDirection AS `电机旋转方向`,
                    inertiaRatio AS `惯量比`
                FROM erp_mech_info WHERE ERP = :erp_number and partNumber = :part_number
            """
            )
            result = db.execute(
                query, {'erp_number': erp_number, 'part_number': part_number}
            )
            data = result.fetchall()

            # 将查询结果转换为DataFrame
            df = pd.DataFrame(data)
        if not df.empty:
            df_bom = pd.read_excel(file.file)
            user_result_dir = os.path.join(
                'results', erp_number, part_number, user_id
            )
            os.makedirs(user_result_dir, exist_ok=True)
            (
                result_file,
                error_count,
                total_count,
            ) = run_bom_parts_count_by_line(
                df_bom,
                df,
                part_number if bom_part_number is None else bom_part_number,
                user_result_dir,
            )

            # 指定输出文件路径
            output_path = os.path.join(user_result_dir, 'result.xlsx')

            # 创建新的Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for index, result_file_path in enumerate(
                    [bom_result_file_path, result_file]
                ):
                    if result_file_path and result_file_path.endswith('.xlsx'):
                        # 读取原始Excel文件，保留格式
                        original_wb = load_workbook(result_file_path)

                        # 使用文件名（不包括扩展名）作为sheet名
                        sheet_name = os.path.splitext(
                            os.path.basename(result_file_path)
                        )[0]

                        # 如果sheet名重复，添加序号
                        if sheet_name in writer.book.sheetnames:
                            sheet_name = f'{sheet_name}_{index}'

                        # 复制第一个sheet到新的workbook，保留格式
                        copy_first_sheet_with_formatting(
                            original_wb, writer.book, sheet_name
                        )

                # 删除默认创建的Sheet
                if 'Sheet' in writer.book.sheetnames:
                    del writer.book['Sheet']

            add_parts_calculation_result(
                db=db,
                erp_number=erp_number,
                part_id=part_number,
                result_file_path=output_path,
            )
            add_parts_error_count(
                db=db,
                erp_number=erp_number,
                part_id=part_number,
                error_count=error_count,
                total_count=total_count,
            )

            # 检查文件是否存在
            if not os.path.exists(output_path):
                raise HTTPException(
                    status_code=404, detail='Result file not found'
                )

            # 返回文件
            filename = os.path.basename(output_path)
            headers = {
                'Content-Disposition': f'attachment; filename="{erp_number}_{part_number}_{filename}"'
            }

            return FileResponse(
                path=output_path,
                filename=f'{erp_number}_{part_number}_{filename}',
                headers=headers,
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            )

        else:
            raise HTTPException(status_code=404, detail='Data not found')

    except Exception as e:
        logger.error(f'Error processing ERP data: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error processing ERP data: {str(e)}'
        )


@router.get(
    '/api/v1/bom-analysis/parts/{erp_number}/{part_number}/latest-result'
)
async def get_latest_result(
    erp_number: str,
    part_number: str,
    db: Session = Depends(get_db_dependency),
):
    """
    获取指定ERP和零件的最新计算结果文件
    """
    try:
        latest_result = get_latest_parts_calculation(
            db, erp_number, part_number
        )
        if not latest_result:
            raise HTTPException(
                status_code=404, detail='No calculation result found'
            )

        if not os.path.exists(latest_result.result_file_path):
            raise HTTPException(
                status_code=404, detail='Result file not found'
            )

        filename = os.path.basename(latest_result.result_file_path)
        headers = {
            'Content-Disposition': f'attachment; filename="{erp_number}_{part_number}_{filename}"'
        }

        return FileResponse(
            path=latest_result.result_file_path,
            filename=f'{erp_number}_{part_number}_{filename}',
            headers=headers,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
    except Exception as e:
        logger.error(f'Error retrieving latest result: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error retrieving latest result: {str(e)}'
        )


@router.get(
    '/api/v1/bom-analysis/parts/{erp_number}/{part_number}/latest-stats'
)
async def get_latest_stats(
    erp_number: str,
    part_number: str,
    is_reverse: Optional[bool] = Query(False),
    db: Session = Depends(get_db_dependency),
):
    """
    获取指定ERP和零件的最新错误统计
    """
    try:
        error_count = get_latest_parts_error_count(
            db, erp_number, part_number, is_reverse
        )
        if not error_count:
            raise HTTPException(
                status_code=404, detail='No error statistics found'
            )

        return error_count.to_dict()
    except Exception as e:
        logger.error(f'Error retrieving latest statistics: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving latest statistics: {str(e)}',
        )


@router.get('/api/v1/bom-analysis/parts/{erp_number}/all-stats')
async def get_all_parts_stats(
    erp_number: str,
    is_reverse: Optional[bool] = Query(False),
    db: Session = Depends(get_db_dependency),
):
    """
    获取指定ERP下所有零件的最新错误统计
    """
    try:
        # 首先从inner_erp_mech_info获取所有有效的ERP和部件
        base_query = text(
            """
            SELECT DISTINCT
                erp,
                partNumber
            FROM inner_erp_framework_list
            WHERE erp = :erp_number AND is_delete = 0
        """
        )

        base_data = db.execute(
            base_query, {'erp_number': erp_number}
        ).fetchall()
        part_ids = [row[1] for row in base_data]
        if not base_data:
            return []
        stats = get_parts_error_summary(db, erp_number, is_reverse)
        if not stats:
            raise Exception('项目未进行比对，无统计数据')

        return [stat for stat in stats if stat['part_id'] in part_ids]
    except Exception as e:
        logger.error(f'{str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'{str(e)}',
        )


@router.get('/api/v1/bom-analysis/parts/erp-stats/summary')
async def get_all_erp_stats(
    is_reverse: Optional[bool] = Query(False),
    db: Session = Depends(get_db_dependency),
):
    """
    获取所有ERP的比对统计汇总信息，以inner_erp_mech_info表为基准
    """
    try:
        # 首先从inner_erp_mech_info获取所有有效的ERP和部件
        base_query = text(
            """
            SELECT DISTINCT
                erp,
                partNumber
            FROM inner_erp_framework_list
            WHERE is_delete = 0
            ORDER BY erp, partNumber
        """
        )

        base_data = db.execute(base_query).fetchall()
        if not base_data:
            return {
                'total_erps': 0,
                'erp_stats': [],
                'overall_summary': {
                    'total_parts': 0,
                    'total_errors': 0,
                    'error_rate': 0,
                    'last_update': datetime.now().isoformat(),
                },
            }

        # 获取错误统计数据
        error_stats = get_parts_error_summary(db, is_reverse=is_reverse)
        error_lookup = {
            f"{stat['erp_number']}_{stat['part_id']}": stat
            for stat in error_stats
        }

        # 按ERP分组统计数据
        erp_stats = {}
        total_parts = 0

        for row in base_data:
            erp = row.erp
            part = row.partNumber

            if erp not in erp_stats:
                erp_stats[erp] = {
                    'erp_number': erp,
                    'total_parts': 0,
                    'error_counts': 0,
                    'checked_parts': 0,  # 新增：已检查的零件数
                    'total_counts': 0,
                    'last_update': None,
                }

            erp_stats[erp]['total_parts'] += 1
            total_parts += 1

            # 查找对应的错误统计
            error_key = f'{erp}_{part}'
            if error_key in error_lookup:
                error_info = error_lookup[error_key]
                erp_stats[erp]['checked_parts'] += 1
                erp_stats[erp]['error_counts'] += error_info['error_count']
                erp_stats[erp]['total_counts'] += error_info['total_count']

                # 更新最后更新时间
                current_time = (
                    datetime.fromisoformat(error_info['create_time'])
                    if error_info['create_time']
                    else None
                )
                if current_time and (
                    not erp_stats[erp]['last_update']
                    or current_time
                    > datetime.fromisoformat(erp_stats[erp]['last_update'])
                ):
                    erp_stats[erp]['last_update'] = error_info['create_time']

        return {
            'total_erps': len(erp_stats),
            'erp_stats': erp_stats,
        }

    except Exception as e:
        logger.error(f'Failed to get ERP stats summary: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Failed to get ERP stats summary: {str(e)}',
        )


@router.post(
    '/api/v1/bom-analysis/parts/{erp_number}/{part_number}/update-stats'
)
async def update_parts_stats(
    erp_number: str,
    part_number: str,
    file: UploadFile = File(...),
    user_id: str = Query(..., description='用户ID'),
    db: Session = Depends(get_db_dependency),
):
    """
    通过上传新文件更新统计值
    """
    try:
        # 1. 保存上传文件
        user_upload_dir = os.path.join(
            'uploads', erp_number, part_number, user_id
        )
        os.makedirs(user_upload_dir, exist_ok=True)
        file_path = os.path.join(user_upload_dir, file.filename)

        # 写入文件
        with open(file_path, 'wb') as buffer:
            content = await file.read()
            buffer.write(content)
            file_size = len(content)

        # # 2. 获取当前最新的计算结果
        # latest_result = get_latest_parts_calculation(
        #     db, erp_number, part_number
        # )
        # if not latest_result:
        #     raise HTTPException(
        #         status_code=404, detail='No calculation result found'
        #     )

        # 3. 读取结果文件进行统计
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404, detail='Result file not found'
            )

        result_dfs = pd.read_excel(file_path, sheet_name=None)
        for sheet_name, result_df in result_dfs.items():
            # 更新负责人字段：当异常原因不为空时，填写user_id
            result_df.loc[
                (result_df['异常原因'].notna()) & (result_df['异常原因'] != ''), '负责人'
            ] = user_id
            if sheet_name == '正反机物料一致性对比':
                error_count = len(
                    (
                        result_df.loc[
                            (result_df['差异类型'] != '完全相同')
                            & (result_df['异常原因'].isna())
                            | (result_df['异常原因'] == ''),
                            '异常',
                        ]
                    )
                )
            else:
                error_count = (
                    result_df.loc[
                        (result_df['异常原因'].isna()) | (result_df['异常原因'] == ''),
                        '异常',
                    ]
                    .fillna(0)
                    .sum()
                )
            total_count = len(result_df)
            if sheet_name == '正反机物料一致性对比':
                styled_df = result_df.style.apply(
                    highlight_differences, axis=1
                )
            else:
                styled_df = result_df.style.apply(highlight_row, axis=1)

            user_result_dir = os.path.join(
                'results', erp_number, part_number, user_id
            )

            # styled_df.to_excel(f'{output_path}/6机械投料校验_BOM_机电沟通表.xlsx', index=False)
            with pd.ExcelWriter(
                f'{user_result_dir}/{sheet_name}.xlsx',
                engine='openpyxl',
                mode='w',
            ) as writer:
                styled_df.to_excel(writer, index=False, encoding='utf-8')

            # 4. 更新错误统计
            updated_stats = add_parts_error_count(
                db=db,
                erp_number=erp_number,
                part_id=part_number,
                error_count=error_count,
                total_count=total_count,
                cal_type='reverse' if sheet_name == '正反机物料一致性对比' else '',
            )

        # 指定输出文件路径
        output_path = os.path.join(user_result_dir, 'result.xlsx')

        # 创建新的Excel文件
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for index, result_file_path in enumerate(
                [
                    f'{user_result_dir}/正反机物料一致性对比.xlsx',
                    f'{user_result_dir}/部件投料校验.xlsx',
                ]
            ):
                if result_file_path.endswith('.xlsx') and os.path.exists(
                    result_file_path
                ):
                    # 读取原始Excel文件，保留格式
                    original_wb = load_workbook(result_file_path)

                    # 遍历原始工作簿中的所有sheet
                    for sheet in original_wb.sheetnames:
                        if sheet == '部件投料校验':
                            continue
                        if sheet.startswith('Sheet'):
                            base_sheet_name = os.path.splitext(
                                os.path.basename(result_file_path)
                            )[0]
                        else:
                            base_sheet_name = sheet

                        # 如果sheet名重复，添加文件索引
                        if base_sheet_name in writer.book.sheetnames:
                            base_sheet_name = f'{sheet}_{index}'

                        # 复制每个sheet到新的workbook，保留格式
                        copy_first_sheet_with_formatting(
                            original_wb, writer.book, base_sheet_name, sheet
                        )

            # 删除默认创建的Sheet
            if 'Sheet' in writer.book.sheetnames:
                del writer.book['Sheet']

        # 记录上传文件
        add_parts_uploaded_file(
            db=db,
            erp_number=erp_number,
            part_id=part_number,
            file_path=output_path,
            file_name=os.path.basename(output_path),
            file_size=file_size,
        )

        return updated_stats.to_dict()

    except Exception as e:
        logger.error(f'Error updating statistics: {str(e)}')
        # 发生错误时删除已上传的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as del_e:
                logger.error(f'Error deleting uploaded file: {str(del_e)}')
        raise HTTPException(
            status_code=500, detail=f'Error updating statistics: {str(e)}'
        )

# routes/airline_connector.py

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from pydantic import BaseModel
from core.dependencies import get_db_dependency
from core.logger import logger
from db_operations import (
    get_all_airline_connectors,
    get_total_connector_count,
    get_airline_connector_by_id,
    create_airline_connector,
    update_airline_connector,
    delete_airline_connector,
)

router = APIRouter()


# 请求和响应模型
class AirlineConnectorCreate(BaseModel):
    male_connector_code: str
    male_connector_name: str
    female_connector_code: str
    female_connector_name: str
    remark: Optional[str] = None


class AirlineConnectorUpdate(BaseModel):
    male_connector_code: Optional[str] = None
    male_connector_name: Optional[str] = None
    female_connector_code: Optional[str] = None
    female_connector_name: Optional[str] = None
    remark: Optional[str] = None


# 路由定义
@router.get('/airline_connectors')
async def list_airline_connectors(
    skip: int = Query(0, description='跳过的记录数'),
    limit: int = Query(10, description='返回的记录数'),
    search: Optional[str] = Query(None, description='搜索关键词'),
    sort_field: str = Query('id', description='排序字段'),
    sort_order: str = Query('asc', description='排序方向 (asc/desc)'),
    db: Session = Depends(get_db_dependency),
):
    """获取航插信息列表"""
    try:
        connectors = get_all_airline_connectors(
            db, skip, limit, search, sort_field, sort_order
        )
        total = get_total_connector_count(db, search)

        return {
            'success': True,
            'data': [item.to_dict() for item in connectors],
            'total': total,
            'page': skip // limit + 1,
            'page_size': limit,
        }
    except Exception as e:
        logger.error(f'Error getting airline connectors: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving airline connectors: {str(e)}',
        )


@router.get('/airline_connectors/{connector_id}')
async def get_airline_connector(
    connector_id: int, db: Session = Depends(get_db_dependency)
):
    """获取特定航插信息"""
    connector = get_airline_connector_by_id(db, connector_id)
    if not connector:
        raise HTTPException(status_code=404, detail='Connector not found')
    return {'success': True, 'data': connector.to_dict()}


@router.post('/airline_connectors')
async def add_airline_connector(
    connector: AirlineConnectorCreate,
    db: Session = Depends(get_db_dependency),
):
    """创建新的航插记录"""
    try:
        new_connector = create_airline_connector(
            db,
            connector.male_connector_code,
            connector.male_connector_name,
            connector.female_connector_code,
            connector.female_connector_name,
            connector.remark,
        )
        return {'success': True, 'data': new_connector.to_dict()}
    except Exception as e:
        logger.error(f'Error creating airline connector: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error creating airline connector: {str(e)}',
        )


@router.put('/airline_connectors/{connector_id}')
async def update_connector(
    connector_id: int,
    connector: AirlineConnectorUpdate,
    db: Session = Depends(get_db_dependency),
):
    """更新航插信息"""
    updated_connector = update_airline_connector(
        db,
        connector_id,
        connector.male_connector_code,
        connector.male_connector_name,
        connector.female_connector_code,
        connector.female_connector_name,
        connector.remark,
    )

    if not updated_connector:
        raise HTTPException(status_code=404, detail='Connector not found')

    return {'success': True, 'data': updated_connector.to_dict()}


@router.delete('/airline_connectors/{connector_id}')
async def delete_connector(
    connector_id: int, db: Session = Depends(get_db_dependency)
):
    """删除航插记录"""
    success = delete_airline_connector(db, connector_id)
    if not success:
        raise HTTPException(status_code=404, detail='Connector not found')

    return {'success': True, 'message': 'Connector deleted successfully'}

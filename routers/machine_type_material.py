# routes/machine_type_material.py

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from pydantic import BaseModel
from core.dependencies import get_db_dependency
from core.logger import logger
from db_operations import (
    get_all_machine_type_materials,
    get_total_count,
    get_machine_type_material_by_id,
    create_machine_type_material,
    update_machine_type_material,
    delete_machine_type_material,
)

router = APIRouter()


# 请求和响应模型
class MachineTypeMaterialCreate(BaseModel):
    machine_type: str
    material_detail: str
    anotation: Optional[str] = None


class MachineTypeMaterialUpdate(BaseModel):
    machine_type: Optional[str] = None
    material_detail: Optional[str] = None
    anotation: Optional[str] = None


# 路由定义
@router.get('/machine_type_materials')
async def list_machine_type_materials(
    skip: int = Query(0, description='跳过的记录数'),
    limit: int = Query(10, description='返回的记录数'),
    search: Optional[str] = Query(None, description='搜索关键词'),
    sort_field: str = Query('id', description='排序字段'),
    sort_order: str = Query('asc', description='排序方向 (asc/desc)'),
    db: Session = Depends(get_db_dependency),
):
    """获取机器类型材料列表"""
    try:
        materials = get_all_machine_type_materials(
            db, skip, limit, search, sort_field, sort_order
        )
        total = get_total_count(db, search)

        return {
            'success': True,
            'data': [item.to_dict() for item in materials],
            'total': total,
            'page': skip // limit + 1,
            'page_size': limit,
        }
    except Exception as e:
        logger.error(f'Error getting machine type materials: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving machine type materials: {str(e)}',
        )


@router.get('/machine_type_materials/{material_id}')
async def get_machine_type_material(
    material_id: int, db: Session = Depends(get_db_dependency)
):
    """获取特定机器类型材料信息"""
    material = get_machine_type_material_by_id(db, material_id)
    if not material:
        raise HTTPException(status_code=404, detail='Material not found')
    return {'success': True, 'data': material.to_dict()}


@router.post('/machine_type_materials')
async def add_machine_type_material(
    material: MachineTypeMaterialCreate,
    db: Session = Depends(get_db_dependency),
):
    """创建新的机器类型材料记录"""
    try:
        new_material = create_machine_type_material(
            db,
            material.machine_type,
            material.material_detail,
            material.anotation,
        )
        return {'success': True, 'data': new_material.to_dict()}
    except Exception as e:
        logger.error(f'Error creating machine type material: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error creating machine type material: {str(e)}',
        )


@router.put('/machine_type_materials/{material_id}')
async def update_material(
    material_id: int,
    material: MachineTypeMaterialUpdate,
    db: Session = Depends(get_db_dependency),
):
    """更新机器类型材料信息"""
    updated_material = update_machine_type_material(
        db,
        material_id,
        material.machine_type,
        material.material_detail,
        material.anotation,
    )

    if not updated_material:
        raise HTTPException(status_code=404, detail='Material not found')

    return {'success': True, 'data': updated_material.to_dict()}


@router.delete('/machine_type_materials/{material_id}')
async def delete_material(
    material_id: int, db: Session = Depends(get_db_dependency)
):
    """删除机器类型材料记录"""
    success = delete_machine_type_material(db, material_id)
    if not success:
        raise HTTPException(status_code=404, detail='Material not found')

    return {'success': True, 'message': 'Material deleted successfully'}

from fastapi import APIRouter
from fastapi import (
    Query,
    Depends,
    HTTPException,
)

from typing import List
from pydantic import BaseModel

import pandas as pd
from sqlalchemy import text
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from db_operations import (
    get_session_by_id,
    get_uploaded_files_by_session,
    get_all_erp_numbers,
    get_erp_with_file_type_2,
)

from core.dependencies import get_db_dependency
from core.logger import logger

router = APIRouter()


class PartNumberResponse(BaseModel):
    success: bool
    part_numbers: List[str]
    total: int


@router.get('/get_part_numbers')
async def get_part_numbers(
    session_id: str = Query(..., description='用户会话ID'),
    db: Session = Depends(get_db_dependency),
) -> PartNumberResponse:
    """
    获取机电沟通表中的所有部件编号
    """
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    # 获取机电沟通表文件
    uploaded_files = get_uploaded_files_by_session(db, session_id)
    mech_comm_file = next(
        (f for f in uploaded_files if f.file_type == '1'), None
    )

    if not mech_comm_file:
        raise HTTPException(
            status_code=400, detail='Mechanical communication file not found'
        )

    try:
        # 读取机电沟通表
        df = pd.read_excel(mech_comm_file.file_path)

        df['部件编号'] = df['部件编号'].fillna('')

        # 格式化部件编号
        df['部件编号'] = df['部件编号'].apply(
            lambda x: str(int(x)) if isinstance(x, float) else str(x)
        )
        df['部件编号'] = df['部件编号'].apply(lambda x: x.zfill(2))
        df = df.dropna(subset=['部件编号'])

        return PartNumberResponse(
            success=True,
            part_numbers=df['部件编号'].unique().tolist(),
            total=len(df['部件编号'].unique().tolist()),
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f'Error processing file: {str(e)}'
        )


@router.get('/get_machine_types')
async def get_machine_types(db: Session = Depends(get_db_dependency)):
    """
    获取所有可用的机型列表

    Args:
        session_id: 用户会话ID
        db: 数据库会话

    Returns:
        包含所有唯一机型的列表
    """
    try:

        # 执行SQL查询获取唯一机型列表
        query = text(
            'SELECT DISTINCT machine_type FROM bom_machine_type_material_info ORDER BY machine_type'
        )
        result = db.execute(query)

        # 提取机型列表
        machine_types = [row[0] for row in result if row[0]]  # 过滤掉可能的空值

        return {
            'success': True,
            'machine_types': machine_types,
            'total': len(machine_types),
        }

    except SQLAlchemyError as e:
        logger.error(f'Database error when fetching machine types: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail='Error accessing database while fetching machine types',
        )
    except Exception as e:
        logger.error(f'Unexpected error when fetching machine types: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Unexpected error: {str(e)}'
        )


@router.get('/erp-numbers')
async def get_erp_numbers(db: Session = Depends(get_db_dependency)):
    erp_numbers = await get_all_erp_numbers(db)
    return {'erp_numbers': erp_numbers}


@router.get(
    '/erp-numbers-with-file-type-2',
    response_model=dict,
    summary='获取包含特定文件类型的ERP编号',
    description='返回所有包含 file_type=2 的上传文件的ERP编号列表',
)
async def get_erp_numbers_with_file_type(
    db: Session = Depends(get_db_dependency),
):
    try:
        erp_numbers = await get_erp_with_file_type_2(db)
        return {'success': True, 'erp_numbers': erp_numbers}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'获取ERP编号失败: {str(e)}')


@router.get('/')
async def serve_frontend():
    from fastapi.responses import FileResponse

    logger.debug('Serving frontend')
    return FileResponse('dist/index.html')

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Text,
    case,
    desc,
    asc,
    text,
)
from datetime import datetime, timedelta
from typing import List, Optional
import time
import uuid
from enum import Enum
from pydantic import BaseModel
import threading
import queue
import json
from sqlalchemy.exc import OperationalError, DisconnectionError

from core.dependencies import get_db_dependency
from core.logger import logger
from core.config import settings
from models import Base
from scheduled_tasks import scheduler_manager

# 创建任务管理路由
task_router = APIRouter()


# 任务状态枚举
class TaskStatus(str, Enum):
    PENDING = 'pending'  # 等待中
    RUNNING = 'running'  # 运行中
    COMPLETED = 'completed'  # 已完成
    FAILED = 'failed'  # 失败


# 任务优先级枚举
class TaskPriority(int, Enum):
    HIGH = 1  # 高优先级（手动插入的紧急任务）
    NORMAL = 2  # 普通优先级（手动创建的任务）
    LOW = 3  # 低优先级（定时任务批量创建的任务）


# 数据库模型
class ComparisonTask(Base):
    __tablename__ = 'bom_comparison_tasks'

    id = Column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    erp_number = Column(String(50), nullable=False)  # 改为单个ERP
    status = Column(String(20), default=TaskStatus.PENDING)
    priority = Column(String(10), default='2')  # 任务优先级
    created_at = Column(DateTime, default=datetime.now)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    result_summary = Column(Text, nullable=True)  # JSON格式存储结果摘要
    created_by = Column(String(100), default='system')


# Pydantic模型
class TaskCreate(BaseModel):
    erp_numbers: List[str]
    created_by: Optional[str] = 'system'
    priority: Optional[TaskPriority] = TaskPriority.NORMAL  # 默认普通优先级


class TaskResponse(BaseModel):
    id: str
    erp_numbers: List[str]  # 现在每个任务只包含一个ERP
    status: TaskStatus
    priority: TaskPriority  # 任务优先级
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    result_summary: Optional[dict]
    created_by: str
    duration: Optional[int]  # 运行时长（秒）


class TaskCreateResponse(BaseModel):
    created_tasks: List[TaskResponse]
    skipped_erps: List[str]  # 因为重复而跳过的ERP
    message: str


class ScheduleConfig(BaseModel):
    scheduled_times: List[str]  # 时间格式: ["08:00", "12:00", "18:00", "20:00"]


class ScheduleStatus(BaseModel):
    is_running: bool
    scheduled_times: List[str]
    next_runs: List[dict]
    scheduler_alive: bool


class PaginatedTaskResponse(BaseModel):
    tasks: List[TaskResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# 优先级任务项
class PriorityTaskItem:
    def __init__(
        self,
        priority: int,
        task_id: str,
        erp_number: str,
        created_at: datetime,
    ):
        self.priority = priority
        self.task_id = task_id
        self.erp_number = erp_number
        self.created_at = created_at

    def __lt__(self, other):
        # 优先级数字越小越优先，如果优先级相同则按创建时间排序
        if self.priority == other.priority:
            return self.created_at < other.created_at
        return self.priority < other.priority


# 全局任务队列和工作线程
task_priority_queue = queue.PriorityQueue()  # 使用优先级队列
worker_thread = None
current_task_id = None
health_check_thread = None

# 任务超时配置（从配置文件读取）
TASK_TIMEOUT = settings.TASK_TIMEOUT  # 任务超时时间
HEALTH_CHECK_INTERVAL = settings.HEALTH_CHECK_INTERVAL  # 健康检查间隔
DB_RETRY_ATTEMPTS = settings.DB_RETRY_ATTEMPTS  # 数据库重试次数
DB_RETRY_DELAY = settings.DB_RETRY_DELAY  # 重试延迟（秒）


def get_db_session_with_retry(max_retries=DB_RETRY_ATTEMPTS):
    """获取数据库会话，带重试机制"""
    from core.database import SessionLocal

    for attempt in range(max_retries):
        try:
            session = SessionLocal()
            # 测试连接
            session.execute(text('SELECT 1'))
            return session
        except (OperationalError, DisconnectionError) as e:
            logger.warning(
                f'数据库连接失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}'
            )
            if session:
                try:
                    session.close()
                except:
                    pass
            if attempt < max_retries - 1:
                time.sleep(DB_RETRY_DELAY)
            else:
                logger.error(f'数据库连接失败，已达到最大重试次数 {max_retries}')
                raise
        except Exception as e:
            logger.error(f'获取数据库会话时发生未知错误: {str(e)}')
            if session:
                try:
                    session.close()
                except:
                    pass
            raise


def update_task_status_with_retry(
    task_id: str,
    status: TaskStatus,
    error_message: str = None,
    result_summary: dict = None,
):
    """更新任务状态，带重试机制"""
    for attempt in range(DB_RETRY_ATTEMPTS):
        db_session = None
        try:
            db_session = get_db_session_with_retry()

            task = (
                db_session.query(ComparisonTask)
                .filter(ComparisonTask.id == task_id)
                .first()
            )
            if task:
                task.status = status
                if status == TaskStatus.RUNNING:
                    task.started_at = datetime.now()
                elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    task.completed_at = datetime.now()

                if error_message:
                    task.error_message = error_message
                if result_summary:
                    task.result_summary = json.dumps(result_summary)

                db_session.commit()
                logger.info(f'任务 {task_id} 状态已更新为 {status}')
                return True
            else:
                logger.warning(f'任务 {task_id} 不存在')
                return False

        except (OperationalError, DisconnectionError) as e:
            logger.warning(
                f'更新任务状态失败 (尝试 {attempt + 1}/{DB_RETRY_ATTEMPTS}): {str(e)}'
            )
            if attempt < DB_RETRY_ATTEMPTS - 1:
                time.sleep(DB_RETRY_DELAY)
            else:
                logger.error(f'更新任务状态失败，已达到最大重试次数: {task_id}')
                return False
        except Exception as e:
            logger.error(f'更新任务状态时发生未知错误: {str(e)}')
            return False
        finally:
            if db_session:
                try:
                    db_session.close()
                except:
                    pass

    return False


# Mock的比对函数（用于测试）
def mock_run_daily_comparison(erp_number: str) -> dict:
    """Mock版本的比对函数，用于测试"""
    logger.info(f'开始处理ERP {erp_number} 的比对任务')

    # 模拟30秒的处理时间
    time.sleep(30)

    # 模拟返回结果
    result = {
        'erp_number': erp_number,
        'processed_parts': 15,
        'successful_parts': 12,
        'failed_parts': 3,
        'accuracy_rate': 80.0,
    }

    logger.info(f'ERP {erp_number} 比对任务完成')
    return result


# 实际的比对函数调用（生产环境使用）
def run_comparison_task(erp_number: str) -> dict:
    """调用实际的比对函数"""
    try:
        # 这里调用实际的比对函数
        # from daily_bom_comparison import run_daily_comparison
        # run_daily_comparison(erp_number)

        # 目前使用mock版本
        return mock_run_daily_comparison(erp_number)
    except Exception as e:
        logger.error(f'ERP {erp_number} 比对失败: {str(e)}')
        raise e


def detect_and_handle_zombie_tasks():
    """检测并处理僵尸任务"""
    try:
        db_session = get_db_session_with_retry()

        # 查找超时的运行中任务
        timeout_threshold = datetime.now() - timedelta(seconds=TASK_TIMEOUT)
        zombie_tasks = (
            db_session.query(ComparisonTask)
            .filter(
                ComparisonTask.status == TaskStatus.RUNNING,
                ComparisonTask.started_at < timeout_threshold,
            )
            .all()
        )

        if zombie_tasks:
            logger.warning(f'发现 {len(zombie_tasks)} 个僵尸任务')

            for task in zombie_tasks:
                logger.warning(f'处理僵尸任务: {task.id} (ERP: {task.erp_number})')
                task.status = TaskStatus.FAILED
                task.error_message = f'任务超时 ({TASK_TIMEOUT}秒)，已自动标记为失败'
                task.completed_at = datetime.now()

            db_session.commit()
            logger.info(f'已处理 {len(zombie_tasks)} 个僵尸任务')

        db_session.close()

    except Exception as e:
        logger.error(f'检测僵尸任务时出错: {str(e)}')


def health_check_worker():
    """健康检查工作线程"""
    logger.info(
        f'健康检查线程已启动，检查间隔: {HEALTH_CHECK_INTERVAL}秒，任务超时: {TASK_TIMEOUT}秒'
    )

    check_count = 0

    while True:
        try:
            check_count += 1
            logger.debug(f'执行第 {check_count} 次健康检查')

            # 检测僵尸任务
            detect_and_handle_zombie_tasks()

            # 检查工作线程状态
            global worker_thread
            if worker_thread and not worker_thread.is_alive():
                logger.error('发现工作线程已停止，尝试重启')
                # 重新启动工作线程（不包括健康检查线程）
                worker_thread = threading.Thread(
                    target=task_worker, daemon=True
                )
                worker_thread.start()
                logger.info('工作线程已重启')

            # 记录系统状态（每10次检查记录一次，避免日志过多）
            if check_count % 10 == 0:
                queue_size = task_priority_queue.qsize()
                worker_alive = (
                    worker_thread.is_alive() if worker_thread else False
                )
                logger.info(
                    f'系统状态检查 - 队列大小: {queue_size}, 工作线程: {"运行" if worker_alive else "停止"}, 当前任务: {current_task_id or "无"}'
                )

            # 等待下次检查
            time.sleep(HEALTH_CHECK_INTERVAL)

        except Exception as e:
            logger.error(f'健康检查出错: {str(e)}')
            time.sleep(HEALTH_CHECK_INTERVAL)


# 任务工作线程
def task_worker():
    """后台任务工作线程"""
    global current_task_id

    while True:
        try:
            # 从优先级队列获取任务
            priority_item = task_priority_queue.get(timeout=1)
            if priority_item is None:  # 停止信号
                break

            task_id = priority_item.task_id
            erp_number = priority_item.erp_number
            current_task_id = task_id

            logger.info(f'开始执行任务 {task_id}, ERP: {erp_number}')

            # 更新任务状态为运行中
            if not update_task_status_with_retry(task_id, TaskStatus.RUNNING):
                logger.error(f'无法更新任务 {task_id} 状态为运行中，跳过执行')
                continue

            # 执行比对任务
            try:
                result = run_comparison_task(erp_number)

                # 更新任务状态为完成
                if update_task_status_with_retry(
                    task_id, TaskStatus.COMPLETED, result_summary=result
                ):
                    logger.info(f'任务 {task_id} (ERP: {erp_number}) 执行完成')
                else:
                    logger.error(f'任务 {task_id} 执行完成但无法更新状态')

            except Exception as e:
                logger.error(f'ERP {erp_number} 处理失败: {str(e)}')

                # 更新任务状态为失败
                if not update_task_status_with_retry(
                    task_id, TaskStatus.FAILED, error_message=str(e)
                ):
                    logger.error(f'任务 {task_id} 失败但无法更新状态')

        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f'任务执行出错: {str(e)}')
            # 更新任务状态为失败
            if current_task_id:
                if not update_task_status_with_retry(
                    current_task_id, TaskStatus.FAILED, error_message=str(e)
                ):
                    logger.error(f'无法更新任务 {current_task_id} 的失败状态')
        finally:
            current_task_id = None


# 自动重启任务（处理重启后的运行中和等待中任务）
def auto_restart_tasks():
    """系统启动时自动重新开始运行中和等待中的任务"""
    try:
        db_session = get_db_session_with_retry()

        # 查找所有运行中的任务，重置为等待状态并重新排队
        running_tasks = (
            db_session.query(ComparisonTask)
            .filter(ComparisonTask.status == TaskStatus.RUNNING)
            .all()
        )

        if running_tasks:
            logger.info(f'发现 {len(running_tasks)} 个运行中的任务，将重置为等待状态并重新开始')

            for task in running_tasks:
                task.status = TaskStatus.PENDING
                task.started_at = None  # 清除开始时间
                task.error_message = None  # 清除错误信息
                logger.info(
                    f'重置运行中任务: {task.id} (ERP: {task.erp_number}) -> PENDING'
                )

            db_session.commit()
            logger.info('运行中任务重置完成')

        # 重新排队所有等待中的任务（包括刚重置的和原本等待的）
        pending_tasks = (
            db_session.query(ComparisonTask)
            .filter(ComparisonTask.status == TaskStatus.PENDING)
            .order_by(ComparisonTask.created_at)
            .all()
        )

        if pending_tasks:
            logger.info(f'重新排队 {len(pending_tasks)} 个等待中任务（包括重置的运行中任务）')

            for task in pending_tasks:
                priority_item = PriorityTaskItem(
                    priority=int(task.priority)
                    if task.priority
                    else TaskPriority.NORMAL,
                    task_id=task.id,
                    erp_number=task.erp_number,
                    created_at=task.created_at,
                )
                task_priority_queue.put(priority_item)
                logger.info(
                    f'重新排队任务: {task.id} (ERP: {task.erp_number}, 优先级: {task.priority})'
                )

            logger.info('所有等待中任务重新排队完成')
        else:
            logger.info('没有等待中的任务需要重新排队')

        db_session.close()

        total_restarted = len(running_tasks) + len(
            [t for t in pending_tasks if t not in running_tasks]
        )
        logger.info(f'任务自动重启完成，共处理 {total_restarted} 个任务')

    except Exception as e:
        logger.error(f'自动重启任务失败: {str(e)}')


# 启动工作线程
def start_worker():
    global worker_thread, health_check_thread

    # 启动任务工作线程
    if worker_thread is None or not worker_thread.is_alive():
        # 自动重启任务
        auto_restart_tasks()

        worker_thread = threading.Thread(target=task_worker, daemon=True)
        worker_thread.start()
        logger.info('任务工作线程已启动')

    # 启动健康检查线程
    if health_check_thread is None or not health_check_thread.is_alive():
        health_check_thread = threading.Thread(
            target=health_check_worker, daemon=True
        )
        health_check_thread.start()
        logger.info('健康检查线程已启动')


# 停止工作线程
def stop_worker():
    global worker_thread, health_check_thread

    # 停止任务工作线程
    if worker_thread and worker_thread.is_alive():
        task_priority_queue.put(None)  # 发送停止信号
        worker_thread.join(timeout=5)
        logger.info('任务工作线程已停止')

    # 健康检查线程会自动停止（daemon线程）
    logger.info('健康检查线程将自动停止')


# 启动时自动启动工作线程
start_worker()


@task_router.post(
    '/api/v1/bom-analysis/daily/tasks/comparison',
    response_model=TaskCreateResponse,
)
async def create_comparison_task(
    task_data: TaskCreate, db: Session = Depends(get_db_dependency)
):
    """创建比对任务 - 每个ERP创建一个独立任务"""
    try:
        created_tasks = []
        skipped_erps = []

        for erp_number in task_data.erp_numbers:
            erp_number = erp_number.strip()
            if not erp_number:
                continue

            # 检查该ERP是否已有待处理或运行中的任务
            existing_task = (
                db.query(ComparisonTask)
                .filter(
                    ComparisonTask.erp_number == erp_number,
                    ComparisonTask.status.in_(
                        [TaskStatus.PENDING, TaskStatus.RUNNING]
                    ),
                )
                .first()
            )

            if existing_task:
                logger.info(
                    f'ERP {erp_number} 已有任务在处理中，跳过: {existing_task.id}'
                )
                skipped_erps.append(erp_number)
                continue

            # 创建新任务
            task = ComparisonTask(
                erp_number=erp_number,
                created_by=task_data.created_by,
                status=TaskStatus.PENDING,
                priority=str(int(task_data.priority)),  # 转换为字符串
            )

            db.add(task)
            db.commit()
            db.refresh(task)

            # 添加到优先级任务队列
            priority_item = PriorityTaskItem(
                priority=int(task_data.priority),
                task_id=task.id,
                erp_number=erp_number,
                created_at=task.created_at,
            )
            task_priority_queue.put(priority_item)

            created_tasks.append(convert_task_to_response(task))
            logger.info(f'创建比对任务: {task.id}, ERP: {erp_number}')

        # 构建响应消息
        message_parts = []
        if created_tasks:
            message_parts.append(f'成功创建 {len(created_tasks)} 个任务')
        if skipped_erps:
            message_parts.append(
                f"跳过 {len(skipped_erps)} 个重复ERP: {', '.join(skipped_erps)}"
            )

        message = '; '.join(message_parts) if message_parts else '没有创建任何任务'

        return TaskCreateResponse(
            created_tasks=created_tasks,
            skipped_erps=skipped_erps,
            message=message,
        )

    except Exception as e:
        logger.error(f'创建任务失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建任务失败: {str(e)}')


@task_router.post(
    '/api/v1/bom-analysis/daily/tasks/comparison/urgent',
    response_model=TaskCreateResponse,
)
async def create_urgent_comparison_task(
    task_data: TaskCreate, db: Session = Depends(get_db_dependency)
):
    """创建高优先级比对任务 - 用于紧急插队处理"""
    try:
        # 强制设置为高优先级
        task_data.priority = TaskPriority.HIGH

        created_tasks = []
        skipped_erps = []

        for erp_number in task_data.erp_numbers:
            erp_number = erp_number.strip()
            if not erp_number:
                continue

            # 检查该ERP是否已有待处理或运行中的任务
            existing_task = (
                db.query(ComparisonTask)
                .filter(
                    ComparisonTask.erp_number == erp_number,
                    ComparisonTask.status.in_(
                        [TaskStatus.PENDING, TaskStatus.RUNNING]
                    ),
                )
                .first()
            )

            if existing_task:
                logger.info(
                    f'ERP {erp_number} 已有任务在处理中，跳过: {existing_task.id}'
                )
                skipped_erps.append(erp_number)
                continue

            # 创建新的高优先级任务
            task = ComparisonTask(
                erp_number=erp_number,
                created_by=task_data.created_by,
                status=TaskStatus.PENDING,
                priority=str(int(TaskPriority.HIGH)),  # 转换为字符串
            )

            db.add(task)
            db.commit()
            db.refresh(task)

            # 添加到优先级任务队列（高优先级）
            priority_item = PriorityTaskItem(
                priority=int(TaskPriority.HIGH),
                task_id=task.id,
                erp_number=erp_number,
                created_at=task.created_at,
            )
            task_priority_queue.put(priority_item)

            created_tasks.append(convert_task_to_response(task))
            logger.info(f'创建高优先级比对任务: {task.id}, ERP: {erp_number}')

        # 构建响应消息
        message_parts = []
        if created_tasks:
            message_parts.append(f'成功创建 {len(created_tasks)} 个高优先级任务')
        if skipped_erps:
            message_parts.append(
                f"跳过 {len(skipped_erps)} 个重复ERP: {', '.join(skipped_erps)}"
            )

        message = '; '.join(message_parts) if message_parts else '没有创建任何任务'

        return TaskCreateResponse(
            created_tasks=created_tasks,
            skipped_erps=skipped_erps,
            message=message,
        )

    except Exception as e:
        logger.error(f'创建高优先级任务失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建高优先级任务失败: {str(e)}')


class TaskPriorityUpdate(BaseModel):
    task_id: str
    new_priority: TaskPriority


@task_router.put(
    '/api/v1/bom-analysis/daily/tasks/priority',
    response_model=TaskResponse,
)
async def update_task_priority(
    priority_update: TaskPriorityUpdate,
    db: Session = Depends(get_db_dependency),
):
    """更新任务优先级 - 仅对等待中的任务有效"""
    try:
        # 查找任务
        task = (
            db.query(ComparisonTask)
            .filter(ComparisonTask.id == priority_update.task_id)
            .first()
        )

        if not task:
            raise HTTPException(status_code=404, detail='任务不存在')

        # 只能调整等待中的任务优先级
        if task.status != TaskStatus.PENDING:
            raise HTTPException(
                status_code=400, detail=f'只能调整等待中的任务优先级，当前任务状态: {task.status}'
            )

        old_priority = task.priority
        task.priority = str(int(priority_update.new_priority))  # 转换为字符串
        db.commit()
        db.refresh(task)

        # 需要重新排队任务
        # 先从队列中移除旧的任务项（这里简化处理，实际可能需要更复杂的队列管理）
        # 然后添加新的优先级任务项
        priority_item = PriorityTaskItem(
            priority=int(priority_update.new_priority),
            task_id=task.id,
            erp_number=task.erp_number,
            created_at=task.created_at,
        )
        task_priority_queue.put(priority_item)

        logger.info(
            f'任务 {task.id} (ERP: {task.erp_number}) 优先级已从 {old_priority} 调整为 {priority_update.new_priority}'
        )

        return convert_task_to_response(task)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'更新任务优先级失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'更新任务优先级失败: {str(e)}')


@task_router.delete(
    '/api/v1/bom-analysis/daily/tasks/{task_id}',
    response_model=dict,
)
async def cancel_task(task_id: str, db: Session = Depends(get_db_dependency)):
    """取消任务 - 仅对等待中的任务有效"""
    try:
        # 查找任务
        task = (
            db.query(ComparisonTask)
            .filter(ComparisonTask.id == task_id)
            .first()
        )

        if not task:
            raise HTTPException(status_code=404, detail='任务不存在')

        # 只能取消等待中的任务
        if task.status != TaskStatus.PENDING:
            raise HTTPException(
                status_code=400, detail=f'只能取消等待中的任务，当前任务状态: {task.status}'
            )

        # 标记任务为已取消
        task.status = TaskStatus.FAILED
        task.error_message = '用户手动取消'
        task.completed_at = datetime.now()
        db.commit()

        logger.info(f'任务 {task.id} (ERP: {task.erp_number}) 已被用户取消')

        return {'message': f'任务 {task.erp_number} 已取消', 'task_id': task_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'取消任务失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'取消任务失败: {str(e)}')


def get_status_priority(status: str) -> int:
    """获取状态优先级"""
    priority_map = {
        TaskStatus.RUNNING: 1,
        TaskStatus.PENDING: 2,
        TaskStatus.COMPLETED: 3,
        TaskStatus.FAILED: 4,
    }
    return priority_map.get(status, 5)


def convert_task_to_response(task: ComparisonTask) -> TaskResponse:
    """转换数据库任务对象为响应对象"""
    duration = None
    if task.started_at and task.completed_at:
        duration = int((task.completed_at - task.started_at).total_seconds())

    result_summary = None
    if task.result_summary:
        try:
            result_summary = json.loads(task.result_summary)
        except:
            pass

    return TaskResponse(
        id=task.id,
        erp_numbers=[task.erp_number],  # 现在是单个ERP的列表
        status=task.status,
        priority=TaskPriority(int(task.priority))
        if task.priority
        else TaskPriority.NORMAL,
        created_at=task.created_at,
        started_at=task.started_at,
        completed_at=task.completed_at,
        error_message=task.error_message,
        result_summary=result_summary,
        created_by=task.created_by,
        duration=duration,
    )


@task_router.get(
    '/api/v1/bom-analysis/daily/tasks/comparison',
    response_model=PaginatedTaskResponse,
)
async def get_all_tasks(
    status: Optional[TaskStatus] = None,
    page: int = 1,
    page_size: int = 10,
    db: Session = Depends(get_db_dependency),
):
    """获取任务列表（分页）"""
    try:
        # 验证分页参数
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 10

        # 构建基础查询
        query = db.query(ComparisonTask)
        if status:
            query = query.filter(ComparisonTask.status == status)

        # 获取总数
        total = query.count()

        # 应用排序逻辑（与前端相同的排序规则）
        # 1. 按状态优先级排序
        # 2. 相同状态内按完成时间倒序（如果有完成时间）
        # 3. 没有完成时间的按创建时间倒序

        # 使用CASE语句进行状态优先级排序

        status_priority = case(
            (ComparisonTask.status == TaskStatus.RUNNING, 1),
            (ComparisonTask.status == TaskStatus.PENDING, 2),
            (ComparisonTask.status == TaskStatus.COMPLETED, 3),
            (ComparisonTask.status == TaskStatus.FAILED, 4),
            else_=5,
        )

        # 复合排序：状态优先级 -> 完成时间倒序 -> 创建时间倒序
        # MySQL兼容的排序方式，手动处理NULL值排序

        # 创建一个排序表达式，让有完成时间的记录排在前面
        completed_at_sort = case(
            (ComparisonTask.completed_at.is_(None), 1), else_=0  # NULL值排在后面
        )

        tasks = (
            query.order_by(
                asc(status_priority),  # 状态优先级升序（数字小的在前）
                asc(completed_at_sort),  # 有完成时间的排在前面
                desc(ComparisonTask.completed_at),  # 完成时间倒序
                desc(ComparisonTask.created_at),  # 创建时间倒序
            )
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        return PaginatedTaskResponse(
            tasks=[convert_task_to_response(task) for task in tasks],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except Exception as e:
        logger.error(f'获取任务列表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取任务列表失败: {str(e)}')


@task_router.get(
    '/api/v1/bom-analysis/daily/tasks/comparison/{task_id}',
    response_model=TaskResponse,
)
async def get_task_by_id(
    task_id: str, db: Session = Depends(get_db_dependency)
):
    """根据ID获取任务详情"""
    try:
        task = (
            db.query(ComparisonTask)
            .filter(ComparisonTask.id == task_id)
            .first()
        )

        if not task:
            raise HTTPException(status_code=404, detail='任务不存在')

        return convert_task_to_response(task)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'获取任务详情失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取任务详情失败: {str(e)}')


@task_router.delete('/api/v1/bom-analysis/daily/tasks/comparison/{task_id}')
async def cancel_comparison_task(
    task_id: str, db: Session = Depends(get_db_dependency)
):
    """取消任务（仅限待处理状态的任务）"""
    try:
        task = (
            db.query(ComparisonTask)
            .filter(ComparisonTask.id == task_id)
            .first()
        )

        if not task:
            raise HTTPException(status_code=404, detail='任务不存在')

        if task.status != TaskStatus.PENDING:
            raise HTTPException(status_code=400, detail='只能取消待处理状态的任务')

        task.status = TaskStatus.FAILED
        task.error_message = '任务已被用户取消'
        task.completed_at = datetime.now()

        db.commit()

        return {'message': '任务已取消', 'task_id': task_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'取消任务失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'取消任务失败: {str(e)}')


@task_router.get('/api/v1/bom-analysis/daily/tasks/status')
async def get_system_status():
    """获取系统状态"""
    try:
        return {
            'queue_size': task_priority_queue.qsize(),
            'worker_alive': worker_thread.is_alive()
            if worker_thread
            else False,
            'health_check_alive': health_check_thread.is_alive()
            if health_check_thread
            else False,
            'current_task': current_task_id,
            'task_timeout': TASK_TIMEOUT,
            'health_check_interval': HEALTH_CHECK_INTERVAL,
        }
    except Exception as e:
        logger.error(f'获取系统状态失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取系统状态失败: {str(e)}')


# 定时任务相关接口
@task_router.get('/api/v1/schedule/status', response_model=ScheduleStatus)
async def get_schedule_status():
    """获取定时任务状态"""
    try:
        status = scheduler_manager.get_status()
        return ScheduleStatus(**status)
    except Exception as e:
        logger.error(f'获取定时任务状态失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取定时任务状态失败: {str(e)}')


@task_router.post('/api/v1/schedule/config')
async def update_schedule_config(config: ScheduleConfig):
    """更新定时任务配置"""
    try:
        # 验证时间格式
        for time_str in config.scheduled_times:
            try:
                hour, minute = map(int, time_str.split(':'))
                if not (0 <= hour <= 23 and 0 <= minute <= 59):
                    raise ValueError(f'无效时间: {time_str}')
            except ValueError:
                raise HTTPException(
                    status_code=400, detail=f'时间格式错误: {time_str}，应为 HH:MM 格式'
                )

        # 更新配置
        scheduler_manager.set_scheduled_times(config.scheduled_times)

        # 重新设置定时任务
        if scheduler_manager.is_running:
            scheduler_manager.setup_schedule()

        return {
            'message': '定时任务配置已更新',
            'scheduled_times': config.scheduled_times,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'更新定时任务配置失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'更新定时任务配置失败: {str(e)}')


@task_router.post('/api/v1/schedule/start')
async def start_scheduler():
    """启动定时任务调度器"""
    try:
        if scheduler_manager.is_running:
            return {'message': '定时任务调度器已在运行中'}

        scheduler_manager.start_scheduler()
        return {'message': '定时任务调度器已启动'}

    except Exception as e:
        logger.error(f'启动定时任务调度器失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'启动定时任务调度器失败: {str(e)}')


@task_router.post('/api/v1/schedule/stop')
async def stop_scheduler():
    """停止定时任务调度器"""
    try:
        if not scheduler_manager.is_running:
            return {'message': '定时任务调度器未在运行'}

        scheduler_manager.stop_scheduler()
        return {'message': '定时任务调度器已停止'}

    except Exception as e:
        logger.error(f'停止定时任务调度器失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'停止定时任务调度器失败: {str(e)}')


@task_router.post('/api/v1/schedule/trigger')
async def trigger_manual_schedule():
    """手动触发一次定时任务"""
    try:
        scheduler_manager.trigger_manual_run()
        return {'message': '已手动触发批量ERP比对任务（包含数据清理）'}

    except Exception as e:
        logger.error(f'手动触发定时任务失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'手动触发定时任务失败: {str(e)}')


@task_router.post('/api/v1/bom-analysis/daily/tasks/cleanup')
async def cleanup_old_tasks():
    """清理7天前的任务数据"""
    try:
        scheduler_manager.cleanup_old_data_only()
        return {'message': '已触发清理7天前的任务数据'}

    except Exception as e:
        logger.error(f'清理旧数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'清理旧数据失败: {str(e)}')

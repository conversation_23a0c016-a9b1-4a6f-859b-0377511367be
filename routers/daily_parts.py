from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import FileResponse
from fastapi.background import BackgroundTasks
import os
from typing import Optional, Dict, List
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, text
from pydantic import BaseModel, Field

from core.dependencies import get_db_dependency
from core.logger import logger

from db_operations import (
    get_latest_daily_parts_calculation,
    get_latest_daily_parts_error_count,
    get_daily_parts_error_summary,
    get_all_erp_daily_error_summary,
    create_part_note,
    update_part_note,
    delete_part_note,
    BomPartNote,
    get_latest_erp_notes,
    get_part_note_history,
    reactivate_part_note,
    get_all_material_filter_rules,
    get_material_filter_rule_by_id,
    create_material_filter_rule,
    update_material_filter_rule,
    delete_material_filter_rule,
    get_material_filter_rules_by_type,
)

import pandas as pd
import tempfile

from rdm_api import BomChangeLog, BomDetail, BomMaster

# Create router
daily_router = APIRouter()


# ==================== Pydantic 模型 ====================


class MaterialFilterRuleCreate(BaseModel):
    """创建物料过滤规则的请求模型"""

    description: str = Field(
        ..., min_length=1, max_length=500, description='物料描述'
    )
    is_included: bool = Field(..., description='是否包含（True=包含，False=排除）')


class MaterialFilterRuleUpdate(BaseModel):
    """更新物料过滤规则的请求模型"""

    description: Optional[str] = Field(
        None, min_length=1, max_length=500, description='物料描述'
    )
    is_included: Optional[bool] = Field(
        None, description='是否包含（True=包含，False=排除）'
    )
    is_active: Optional[bool] = Field(
        None, description='是否激活（True=激活，False=停用）'
    )


class MaterialFilterRuleResponse(BaseModel):
    """物料过滤规则的响应模型"""

    id: int
    description: str
    is_included: bool
    created_at: str
    updated_at: str
    is_active: bool

    class Config:
        from_attributes = True


@daily_router.get(
    '/api/v1/bom-analysis/daily/parts/{erp_number}/{part_number}/latest-result'
)
async def get_latest_daily_result(
    erp_number: str,
    part_number: str,
    date_str: Optional[str] = Query(
        None, description='Specific date (YYYY-MM-DD)'
    ),
    db: Session = Depends(get_db_dependency),
):
    """
    Get the latest daily calculation result file for a specific ERP and part
    """
    try:
        specific_date = (
            datetime.strptime(date_str, '%Y-%m-%d').date()
            if date_str
            else None
        )

        latest_result = get_latest_daily_parts_calculation(
            db, erp_number, part_number.zfill(2), specific_date
        )

        if not latest_result:
            raise HTTPException(
                status_code=404, detail='No daily calculation result found'
            )

        if not os.path.exists(latest_result.result_file_path):
            raise HTTPException(
                status_code=404, detail='Daily result file not found'
            )

        filename = os.path.basename(latest_result.result_file_path)

        # Add cache control headers to prevent caching
        headers = {
            'Content-Disposition': f'attachment; filename="{erp_number}_{part_number}_{filename}"',
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0',
        }

        return FileResponse(
            path=latest_result.result_file_path,
            filename=f'{erp_number}_{part_number}_{filename}',
            headers=headers,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error retrieving latest daily result: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving latest daily result: {str(e)}',
        )


@daily_router.get(
    '/api/v1/bom-analysis/daily/parts/{erp_number}/{part_number}/latest-stats'
)
async def get_latest_daily_stats(
    erp_number: str,
    part_number: str,
    is_reverse: bool = Query(False),
    date_str: Optional[str] = Query(
        None, description='Specific date (YYYY-MM-DD)'
    ),
    db: Session = Depends(get_db_dependency),
):
    """
    Get the latest daily error statistics for a specific ERP and part
    """
    try:
        specific_date = date.fromisoformat(date_str) if date_str else None

        error_count = get_latest_daily_parts_error_count(
            db, erp_number, part_number, is_reverse, specific_date
        )

        if not error_count:
            raise HTTPException(
                status_code=404, detail='No daily error statistics found'
            )

        result = error_count.to_dict()
        # 首先保留原始错误数量
        result['raw_error_count'] = result['error_count']
        result['note_applied'] = False
        result['note_content_reverse'] = None

        # 如果是反向查询，检查note_content_reverse
        if is_reverse:
            try:
                # 查询最新的note
                note = (
                    db.query(BomPartNote)
                    .filter(
                        BomPartNote.erp_number == erp_number,
                        BomPartNote.part_id == part_number,  # 使用part_id
                        BomPartNote.is_active == True,
                    )
                    .first()
                )

                # 如果存在note且note_content_reverse有内容，将error_count设为0
                if (
                    note
                    and note.note_content_reverse
                    and note.note_content_reverse.strip()
                ):
                    # result['error_count'] = 0
                    result['note_applied'] = True
                    result['note_content_reverse'] = note.note_content_reverse

            except Exception as note_error:
                logger.warning(
                    f'Error checking note for reverse query: {str(note_error)}'
                )
                # 如果查询note出错，继续使用原始数据
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error retrieving latest daily statistics: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving latest daily statistics: {str(e)}',
        )


@daily_router.get('/api/v1/bom-analysis/daily/parts/{erp_number}/all-stats')
async def get_all_daily_parts_stats(
    erp_number: str,
    is_reverse: bool = Query(False),
    date_str: Optional[str] = Query(
        None, description='Specific date (YYYY-MM-DD)'
    ),
    db: Session = Depends(get_db_dependency),
):
    """
    Get daily error statistics for all parts of a specific ERP
    返回列表格式，保持原有数据结构
    """
    try:
        specific_date = date.fromisoformat(date_str) if date_str else None

        # 获取该ERP的所有部件错误统计
        stats = get_daily_parts_error_summary(
            db, erp_number, is_reverse, specific_date
        )

        if not stats:
            return []  # 保持列表格式

        # 如果是反向查询，一次性查询该ERP的所有active notes
        notes_dict = {}
        if is_reverse:
            try:
                notes = (
                    db.query(BomPartNote)
                    .filter(
                        BomPartNote.erp_number == erp_number,
                        BomPartNote.is_active == True,
                    )
                    .all()
                )

                # 构建part_id到note的映射
                notes_dict = {
                    note.part_id: note
                    for note in notes
                    if note.note_content_reverse
                    and note.note_content_reverse.strip()
                }

            except Exception as note_error:
                logger.warning(
                    f'Error querying notes for ERP {erp_number}: {str(note_error)}'
                )

        # 处理每个统计记录，保持原有字段结构，只增加新字段
        processed_stats = []

        for stat in stats:
            # 保持原有字段不变，只增加新字段
            processed_stat = stat.copy()  # 保持原有所有字段

            # 增加新字段
            processed_stat['raw_error_count'] = stat['error_count']  # 保存原始错误数
            processed_stat['note_applied'] = False
            processed_stat['note_content_reverse'] = None

            # 如果是反向查询且该部件有对应的note
            if is_reverse and stat['part_id'] in notes_dict:
                note = notes_dict[stat['part_id']]
                # processed_stat['error_count'] = 0  # 修改错误数为0
                processed_stat['note_applied'] = True
                processed_stat[
                    'note_content_reverse'
                ] = note.note_content_reverse

            processed_stats.append(processed_stat)

        return processed_stats  # 返回列表格式，保持原有结构

    except Exception as e:
        logger.error(f'{str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'{str(e)}',
        )


@daily_router.get('/api/v1/bom-analysis/daily/parts/accuracy-stats')
async def get_part_accuracy_stats(
    drawing_numbers: str = Query(
        ..., description='图号格式: erp1-部件编号-000,erp2-部件编号-000'
    ),
    date_str: Optional[str] = Query(
        None, description='Specific date (YYYY-MM-DD)'
    ),
    db: Session = Depends(get_db_dependency),
):
    """
    查询某个ERP某个部件的最新准确率
    接口传参为 erp-部件编号-000 为图号，解析出ERP跟部件后
    把正反机以及沟通表对比准确率都返回，有细节有汇总
    """
    try:
        results = []
        # 解析图号格式: erp-部件编号-000
        for drawing_number in drawing_numbers.split(','):
            parts = drawing_number.split('-')
            if len(parts) < 3:
                raise HTTPException(
                    status_code=400, detail='图号格式错误，应为: erp-部件编号-000'
                )

            erp_number = parts[0]
            part_number = parts[1]

            # 验证图号格式
            if not parts[2].startswith('000'):
                raise HTTPException(status_code=400, detail='图号格式错误，末尾应为000开头')

            logger.info(f'查询准确率统计 - ERP: {erp_number}, 部件: {part_number}')

            specific_date = date.fromisoformat(date_str) if date_str else None

            # 获取正向对比数据
            forward_error_count = get_latest_daily_parts_error_count(
                db, erp_number, part_number, False, specific_date
            )

            # 获取反向对比数据
            reverse_error_count = get_latest_daily_parts_error_count(
                db, erp_number, part_number, True, specific_date
            )

            # 初始化结果
            result = {
                'erp_number': erp_number,
                'part_number': part_number,
                'drawing_number': drawing_number,
                'query_date': date_str
                or (
                    datetime.now().strftime('%Y-%m-%d')
                    if not specific_date
                    else specific_date.strftime('%Y-%m-%d')
                ),
                'base_comparison': None,
                'reverse_comparison': None,
                'summary': {
                    'base_accuracy_rate': 0.0,
                    'reverse_accuracy_rate': 0.0,
                    'overall_accuracy_rate': 0.0,
                },
            }

            # 处理正向对比数据
            if forward_error_count:
                forward_data = forward_error_count.to_dict()
                forward_accuracy = 0.0
                if forward_data['total_count'] > 0:
                    forward_accuracy = round(
                        (
                            (
                                forward_data['total_count']
                                - forward_data['error_count']
                            )
                            / forward_data['total_count']
                        )
                        * 100,
                        2,
                    )

                result['base_comparison'] = {
                    'error_count': forward_data['error_count'],
                    'total_count': forward_data['total_count'],
                    'accuracy_rate': forward_accuracy,
                    'create_time': forward_data['create_time'],
                    'create_date': forward_data['create_date'],
                    'cal_type': forward_data['cal_type'],
                }
                result['summary']['base_accuracy_rate'] = forward_accuracy

            # 处理反向对比数据
            if reverse_error_count:
                reverse_data = reverse_error_count.to_dict()

                reverse_accuracy = 0.0
                if reverse_data['total_count'] > 0:
                    reverse_accuracy = round(
                        (
                            (
                                reverse_data['total_count']
                                - reverse_data['error_count']
                            )
                            / reverse_data['total_count']
                        )
                        * 100,
                        2,
                    )

                result['reverse_comparison'] = {
                    'error_count': reverse_data['error_count'],
                    'total_count': reverse_data['total_count'],
                    'accuracy_rate': reverse_accuracy,
                    'create_time': reverse_data['create_time'],
                    'create_date': reverse_data['create_date'],
                    'cal_type': reverse_data['cal_type'],
                }
                result['summary']['reverse_accuracy_rate'] = reverse_accuracy

            # 计算整体准确率
            total_errors = 0
            total_counts = 0

            if forward_error_count:
                total_errors += result['base_comparison']['error_count']
                total_counts += result['base_comparison']['total_count']

            if reverse_error_count:
                total_errors += result['reverse_comparison']['error_count']
                total_counts += result['reverse_comparison']['total_count']

            if total_counts > 0:
                result['summary']['overall_accuracy_rate'] = round(
                    ((total_counts - total_errors) / total_counts) * 100, 2
                )

            # 如果没有找到任何数据，默认给100%准确率
            if not forward_error_count and not reverse_error_count:
                logger.info(
                    f'未找到ERP {erp_number} 部件 {part_number} 的数据，返回默认100%准确率'
                )
                result['base_comparison'] = {
                    'error_count': 0,
                    'total_count': 0,
                    'accuracy_rate': 100.0,
                    'create_time': None,
                    'create_date': None,
                    'cal_type': 'default',
                }
                result['reverse_comparison'] = {
                    'error_count': 0,
                    'total_count': 0,
                    'accuracy_rate': 100.0,
                    'create_time': None,
                    'create_date': None,
                    'cal_type': 'default',
                }
                result['summary'] = {
                    'base_accuracy_rate': 100.0,
                    'reverse_accuracy_rate': 100.0,
                    'overall_accuracy_rate': 100.0,
                }

            results.append(
                {
                    'drawing_number': drawing_number,
                    'success': True
                    if int(result['summary']['overall_accuracy_rate']) == 100
                    else False,
                    'message': '通过审核'
                    if int(result['summary']['overall_accuracy_rate']) == 100
                    else f"BOM比对准确率为{result['summary']['overall_accuracy_rate']}%, 无法通过审核",
                }
            )
        return {'results': results}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'查询部件准确率统计失败: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'查询部件准确率统计失败: {str(e)}',
        )


@daily_router.get('/api/v1/bom-analysis/daily/erp-stats/summary')
async def get_all_daily_erp_stats(
    is_reverse: bool = Query(False),
    date_str: Optional[str] = Query(
        None, description='Specific date (YYYY-MM-DD)'
    ),
    db: Session = Depends(get_db_dependency),
):
    """
    Get a summary of daily error statistics for all ERPs
    保持原有数据结构，只增加字段
    """
    try:
        specific_date = date.fromisoformat(date_str) if date_str else None

        # 获取原始的汇总数据
        original_summary = get_all_erp_daily_error_summary(
            db, is_reverse, specific_date
        )

        if not original_summary:
            # 保持原有结构，只增加新字段
            return {
                'total_erps': 0,
                'erp_stats': [],
                'date': date_str,
                # 新增字段
                'is_reverse': is_reverse,
                'total_raw_error_count': 0,
                'total_notes_applied': 0,
                'global_raw_accuracy_rate': 1.0,
            }

        # 如果是反向查询，查询所有active notes
        erp_notes_dict = {}
        if is_reverse:
            try:
                all_notes = (
                    db.query(BomPartNote)
                    .filter(BomPartNote.is_active == True)
                    .all()
                )

                # 构建ERP -> {part_id: note} 的映射
                for note in all_notes:
                    if (
                        note.note_content_reverse
                        and note.note_content_reverse.strip()
                    ):
                        if note.erp_number not in erp_notes_dict:
                            erp_notes_dict[note.erp_number] = {}
                        erp_notes_dict[note.erp_number][note.part_id] = note

            except Exception as note_error:
                logger.warning(f'Error querying all notes: {str(note_error)}')

        # 处理erp_stats数组，保持原有字段，只增加新字段
        total_notes_applied = 0
        total_raw_error_count = 0

        for erp_stat in original_summary.get('erp_stats', []):
            erp_number = erp_stat.get('erp_number')

            # 增加新字段，保持原有字段不变
            erp_stat['raw_error_counts'] = erp_stat.get('error_counts', 0)
            erp_stat['raw_error_rate'] = erp_stat.get('error_rate', 0)
            erp_stat['notes_applied_count'] = 0

            total_raw_error_count += erp_stat.get('error_counts', 0)

            # 如果是反向查询且有parts_details，直接使用parts_details中的数据
            if is_reverse and erp_number and erp_number in erp_notes_dict:
                parts_details = erp_stat.get('parts_details', [])
                if parts_details:
                    adjusted_error_count = 0
                    notes_applied_count = 0
                    notes_data = erp_notes_dict[erp_number]

                    # 处理parts_details中的每个部件
                    for part_detail in parts_details:
                        part_id = part_detail.get('part_id')
                        part_error_count = part_detail.get('error_count', 0)

                        # 如果该部件有对应的note，将error_count设为0
                        if part_id in notes_data:
                            # part_error_count = 0
                            notes_applied_count += 1
                            # 在parts_details中也添加note信息
                            part_detail['raw_error_count'] = part_detail.get(
                                'error_count', 0
                            )
                            # part_detail['error_count'] = 0
                            part_detail['note_applied'] = True
                            part_detail['note_content_reverse'] = notes_data[
                                part_id
                            ].note_content_reverse
                        else:
                            # 没有note的部件也添加相关字段
                            part_detail['raw_error_count'] = part_detail.get(
                                'error_count', 0
                            )
                            part_detail['note_applied'] = False
                            part_detail['note_content_reverse'] = None

                        adjusted_error_count += part_error_count

                    # 更新ERP级别的错误统计
                    erp_stat['error_counts'] = adjusted_error_count
                    erp_stat['notes_applied_count'] = notes_applied_count
                    total_notes_applied += notes_applied_count

                    # 重新计算错误率
                    total_counts = erp_stat.get('total_counts', 0)
                    if total_counts > 0:
                        erp_stat['error_rate'] = round(
                            (adjusted_error_count / total_counts) * 100, 2
                        )
                    else:
                        erp_stat['error_rate'] = 0
            else:
                # 非反向查询或没有notes，为parts_details添加新字段
                parts_details = erp_stat.get('parts_details', [])
                for part_detail in parts_details:
                    part_detail['raw_error_count'] = part_detail.get(
                        'error_count', 0
                    )
                    part_detail['note_applied'] = False
                    part_detail['note_content_reverse'] = None

        # 保持原有结构，增加新字段
        result = original_summary.copy()
        result['is_reverse'] = is_reverse
        result['total_raw_error_count'] = total_raw_error_count
        result['total_notes_applied'] = total_notes_applied

        # 计算全局原始错误率
        total_counts = sum(
            erp.get('total_counts', 0) for erp in result.get('erp_stats', [])
        )
        if total_counts > 0:
            result['global_raw_error_rate'] = round(
                (total_raw_error_count / total_counts) * 100, 2
            )
        else:
            result['global_raw_error_rate'] = 0

        return result

    except Exception as e:
        logger.error(f'Failed to get daily ERP stats summary: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Failed to get daily ERP stats summary: {str(e)}',
        )


def get_bom_data_by_erp(
    db: Session, erp: str, page: int = 1, page_size: int = 50
) -> Dict:
    """
    分页查询BOM数据

    :param db: 数据库会话
    :param erp: ERP编号
    :param page: 页码，默认为1
    :param page_size: 每页记录数，默认为50
    :return: 包含BOM数据和分页信息的字典
    """
    try:
        # 从erp_base_info表查询ERP与物料编码的对应关系
        erp_base_query = text(
            """
            SELECT
                ERP,
                second_erp,
                pcode,
                ncode
            FROM erp_base_info
            """
        )

        erp_data = db.execute(erp_base_query).fetchall()
        if not erp_data:
            return {
                'success': False,
                'message': '未找到任何ERP数据',
                'data': {
                    'total': 0,
                    'page': page,
                    'page_size': page_size,
                    'items': [],
                },
            }

        # 构建ERP与物料编码的映射表
        inv_code_map = {}

        for row in erp_data:
            # 主ERP与pcode对应
            if row.ERP and row.pcode:
                inv_code_map[row.ERP] = row.pcode

            # 处理第二ERP与ncode的对应关系
            if (
                row.second_erp
                and row.ncode
                and row.second_erp != '/'
                and row.second_erp != '无'
            ):
                # 检查second_erp是否包含多个ERP（以/分隔）
                second_erps = row.second_erp.split('/')

                # 检查ncode是否包含多个编码（以/分隔）
                ncodes = row.ncode.split('/')

                # 确保至少有一个ncode可用
                if ncodes:
                    # 以ncode的数量为准进行匹配
                    for i, ncode in enumerate(ncodes):
                        if (
                            i < len(second_erps) and second_erps[i].strip()
                        ):  # 确保有对应的second_erp且不为空
                            inv_code_map[
                                second_erps[i].strip()
                            ] = ncode.strip()

        # 查找指定ERP的物料编码
        if erp not in inv_code_map:
            return {
                'success': False,
                'message': f'未找到ERP编号为{erp}的物料编码',
                'data': {
                    'total': 0,
                    'page': page,
                    'page_size': page_size,
                    'items': [],
                },
            }

        inv_code = inv_code_map[erp]

        # 查询最新版本的BOM主表记录
        bom_master = (
            db.query(BomMaster)
            .filter(
                BomMaster.root_inv_code == inv_code,
                BomMaster.is_latest == True,
            )
            .first()
        )

        if not bom_master:
            return {
                'success': False,
                'message': f'未找到物料编码为{inv_code}的最新版本BOM数据',
                'data': {
                    'total': 0,
                    'page': page,
                    'page_size': page_size,
                    'items': [],
                },
            }

        # 查询BOM明细，带分页
        total_items = (
            db.query(func.count(BomDetail.id))
            .filter(BomDetail.master_id == bom_master.id)
            .scalar()
        )

        offset = (page - 1) * page_size
        bom_details = (
            db.query(BomDetail)
            .filter(BomDetail.master_id == bom_master.id)
            .order_by(BomDetail.depth, BomDetail.inv_code)
            .offset(offset)
            .limit(page_size)
            .all()
        )

        # 转换为字典列表
        bom_items = []
        for detail in bom_details:
            bom_items.append(
                {
                    'id': detail.id,
                    'parent_inv_code': detail.parent_inv_code,
                    'inv_code': detail.inv_code,
                    'memo_cn': detail.memo_cn,
                    'total_amount': float(detail.total_amount)
                    if detail.total_amount
                    else 0,
                    'per_amount': float(detail.per_amount)
                    if detail.per_amount
                    else 0,
                    'drawing_version': detail.drawing_version,
                    'material_type': detail.material_type,
                    'drawing_no': detail.drawing_no,
                    'unit': detail.unit,
                    'depth': detail.depth,
                    'component_part': detail.component_part,
                    'location_code': detail.location_code,
                    'package_group': detail.package_group,
                }
            )

        return {
            'success': True and bom_items,
            'message': '查询成功' if bom_items else '无数据',
            'data': {
                'erp': erp,
                'inv_code': inv_code,
                'bom_version': bom_master.version,
                'query_time': bom_master.query_time.isoformat(),
                'total': total_items,
                'page': page,
                'page_size': page_size,
                'items': bom_items,
            },
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'查询BOM数据失败: {str(e)}',
            'data': {
                'total': 0,
                'page': page,
                'page_size': page_size,
                'items': [],
            },
        }


def get_bom_change_log(
    db: Session,
    erp: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Dict:
    """
    查询BOM变更日志

    :param db: 数据库会话
    :param erp: ERP编号
    :param start_time: 查询起始时间，默认为None，表示比较最新的两个版本
    :param end_time: 查询截止时间，默认为None
    :return: 包含BOM变更日志的字典
    """
    try:
        erp_base_query = text(
            """
            SELECT
                ERP,
                second_erp,
                pcode,
                ncode
            FROM erp_base_info
            """
        )

        erp_data = db.execute(erp_base_query).fetchall()
        if not erp_data:
            return {'success': False, 'message': '未找到任何ERP数据'}

        # 构建ERP与物料编码的映射表
        inv_code_map = {}

        for row in erp_data:
            # 主ERP与pcode对应
            if row.ERP and row.pcode:
                inv_code_map[row.ERP] = row.pcode

            # 处理第二ERP与ncode的对应关系
            if (
                row.second_erp
                and row.ncode
                and row.second_erp != '/'
                and row.second_erp != '无'
            ):
                # 检查second_erp是否包含多个ERP（以/分隔）
                second_erps = row.second_erp.split('/')

                # 检查ncode是否包含多个编码（以/分隔）
                ncodes = row.ncode.split('/')

                # 确保至少有一个ncode可用
                if ncodes:
                    # 以ncode的数量为准进行匹配
                    for i, ncode in enumerate(ncodes):
                        if (
                            i < len(second_erps) and second_erps[i].strip()
                        ):  # 确保有对应的second_erp且不为空
                            inv_code_map[
                                second_erps[i].strip()
                            ] = ncode.strip()

        # 查找指定ERP的物料编码
        if erp not in inv_code_map:
            return {'success': False, 'message': f'未找到ERP编号为{erp}的物料编码'}

        inv_code = inv_code_map[erp]

        # 构建查询条件
        query = db.query(BomChangeLog).filter(
            BomChangeLog.root_inv_code == inv_code
        )

        if start_time and end_time:
            # 如果指定了时间范围，则查询该时间范围内的变更日志
            query = query.filter(
                BomChangeLog.change_time >= start_time,
                BomChangeLog.change_time <= end_time,
            )
        else:
            # 如果未指定时间范围，则查询最新版本与上一个版本的对比结果
            latest_bom = (
                db.query(BomMaster).filter(
                    BomMaster.root_inv_code == inv_code,
                    # BomMaster.is_latest == True,
                )
                # .first()
            )

            if not latest_bom:
                return {
                    'success': False,
                    'message': f'未找到物料编码为{inv_code}的最新版本BOM数据',
                    'data': {'changes': []},
                }

            # 查询最新版本的变更记录
            # query = query.filter(
            #     BomChangeLog.curr_version == latest_bom.version
            # )

        # 执行查询并按时间排序
        change_logs = query.order_by(desc(BomChangeLog.change_time)).all()

        # 转换为字典列表
        changes = []
        for log in change_logs:
            changes.append(
                {
                    'log_id': log.log_id,
                    'prev_version': log.prev_version,
                    'curr_version': log.curr_version,
                    'inv_code': log.inv_code,
                    'memo_cn': log.memo_cn,
                    'parent_inv_code': log.parent_inv_code,
                    'component_part': log.component_part,
                    'change_type': log.change_type,
                    'change_time': log.change_time.isoformat(),
                    'change_fields': log.change_fields,
                }
            )

        return {
            'success': True,
            'message': '查询成功',
            'data': {
                'erp': erp,
                'inv_code': inv_code,
                'start_time': start_time.isoformat() if start_time else None,
                'end_time': end_time.isoformat() if end_time else None,
                'changes': changes,
            },
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'查询BOM变更日志失败: {str(e)}',
            'data': {'changes': []},
        }


def get_erps_with_change_logs(
    db: Session,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Dict:
    """
    获取所有有变更数据的ERP列表

    :param db: 数据库会话
    :param start_time: 查询起始时间，默认为None
    :param end_time: 查询截止时间，默认为None
    :return: 包含ERP列表的字典
    """
    try:
        # 查询所有有变更记录的inv_code列表
        query = db.query(BomChangeLog.root_inv_code).distinct()

        # 如果指定了时间范围，则筛选该时间范围内的变更记录
        if start_time and end_time:
            query = query.filter(
                BomChangeLog.change_time >= start_time,
                BomChangeLog.change_time <= end_time,
            )

        # 获取所有有变更记录的inv_code
        root_inv_codes = [row[0] for row in query.all()]

        if not root_inv_codes:
            return {
                'success': True,
                'message': '未找到任何变更记录',
                'data': {
                    'erp_list': [],
                    'start_time': start_time.isoformat()
                    if start_time
                    else None,
                    'end_time': end_time.isoformat() if end_time else None,
                },
            }

        erp_base_query = text(
            """
            SELECT
                ERP,
                second_erp,
                pcode,
                ncode
            FROM erp_base_info
            """
        )

        erp_data = db.execute(erp_base_query).fetchall()
        if not erp_data:
            return {'success': False, 'message': '未找到任何ERP数据'}

        # 构建ERP与物料编码的映射表
        inv_code_map = {}

        for row in erp_data:
            # 主ERP与pcode对应
            if row.ERP and row.pcode:
                inv_code_map[row.pcode] = row.ERP

            # 处理第二ERP与ncode的对应关系
            if (
                row.second_erp
                and row.ncode
                and row.second_erp != '/'
                and row.second_erp != '无'
            ):
                # 检查second_erp是否包含多个ERP（以/分隔）
                second_erps = row.second_erp.split('/')

                # 检查ncode是否包含多个编码（以/分隔）
                ncodes = row.ncode.split('/')

                # 确保至少有一个ncode可用
                if ncodes:
                    # 以ncode的数量为准进行匹配
                    for i, ncode in enumerate(ncodes):
                        if (
                            i < len(second_erps) and second_erps[i].strip()
                        ):  # 确保有对应的second_erp且不为空
                            inv_code_map[ncode.strip()] = second_erps[
                                i
                            ].strip()
        # 构建ERP列表
        erp_list = []
        for inv_code in root_inv_codes:
            if inv_code in inv_code_map:
                erp = inv_code_map[inv_code]

                # 查询该ERP在指定时间范围内的变更数量
                change_count_query = db.query(
                    func.count(BomChangeLog.log_id)
                ).filter(BomChangeLog.root_inv_code == inv_code)

                if start_time and end_time:
                    change_count_query = change_count_query.filter(
                        BomChangeLog.change_time >= start_time,
                        BomChangeLog.change_time <= end_time,
                    )

                change_count = change_count_query.scalar()

                # 查询最新的变更时间
                latest_change_query = db.query(
                    func.max(BomChangeLog.change_time)
                ).filter(BomChangeLog.root_inv_code == inv_code)

                if start_time and end_time:
                    latest_change_query = latest_change_query.filter(
                        BomChangeLog.change_time >= start_time,
                        BomChangeLog.change_time <= end_time,
                    )

                latest_change_time = latest_change_query.scalar()

                erp_data = {
                    'erp': erp,
                    'inv_code': inv_code,
                    'change_count': change_count,
                    'latest_change_time': latest_change_time.isoformat()
                    if latest_change_time
                    else None,
                }

                erp_list.append(erp_data)

        # 按最新变更时间排序（降序）
        erp_list.sort(
            key=lambda x: x['latest_change_time']
            if x['latest_change_time']
            else '',
            reverse=True,
        )

        return {
            'success': True,
            'message': '查询成功',
            'data': {
                'erp_list': erp_list,
                'total_erps': len(erp_list),
                'start_time': start_time.isoformat() if start_time else None,
                'end_time': end_time.isoformat() if end_time else None,
            },
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'查询ERP列表失败: {str(e)}',
            'data': {'erp_list': []},
        }


def export_bom_data(db: Session, erp: str) -> Dict:
    """
    导出BOM完整数据

    :param db: 数据库会话
    :param erp: ERP编号
    :return: 包含完整BOM数据的字典，可用于生成Excel文件
    """
    try:
        # 从erp_base_info表查询ERP与物料编码的对应关系
        erp_base_query = text(
            """
            SELECT
                ERP,
                second_erp,
                pcode,
                ncode
            FROM erp_base_info
            """
        )

        erp_data = db.execute(erp_base_query).fetchall()
        if not erp_data:
            return {
                'success': False,
                'message': '未找到任何ERP数据',
                'data': None,
            }

        # 构建ERP与物料编码的映射表
        inv_code_map = {}

        for row in erp_data:
            # 主ERP与pcode对应
            if row.ERP and row.pcode:
                inv_code_map[row.ERP] = row.pcode

            # 处理第二ERP与ncode的对应关系
            if (
                row.second_erp
                and row.ncode
                and row.second_erp != '/'
                and row.second_erp != '无'
            ):
                # 检查second_erp是否包含多个ERP（以/分隔）
                second_erps = row.second_erp.split('/')

                # 检查ncode是否包含多个编码（以/分隔）
                ncodes = row.ncode.split('/')

                # 确保至少有一个ncode可用
                if ncodes:
                    # 以ncode的数量为准进行匹配
                    for i, ncode in enumerate(ncodes):
                        if (
                            i < len(second_erps) and second_erps[i].strip()
                        ):  # 确保有对应的second_erp且不为空
                            inv_code_map[
                                second_erps[i].strip()
                            ] = ncode.strip()

        # 查找指定ERP的物料编码
        if erp not in inv_code_map:
            return {
                'success': False,
                'message': f'未找到ERP编号为{erp}的物料编码',
                'data': None,
            }

        inv_code = inv_code_map[erp]

        # 查询最新版本的BOM主表记录
        bom_master = (
            db.query(BomMaster)
            .filter(
                BomMaster.root_inv_code == inv_code,
                BomMaster.is_latest == True,
            )
            .first()
        )

        if not bom_master:
            return {
                'success': False,
                'message': f'未找到物料编码为{inv_code}的最新版本BOM数据',
                'data': None,
            }

        # 查询所有BOM明细
        bom_details = (
            db.query(BomDetail)
            .filter(BomDetail.master_id == bom_master.id)
            .order_by(BomDetail.depth, BomDetail.inv_code)
            .all()
        )

        # 转换为字典列表，包含更多字段用于导出
        bom_items = []
        for detail in bom_details:
            bom_items.append(
                {
                    'id': detail.id,
                    'parent_inv_code': detail.parent_inv_code,
                    'inv_code': detail.inv_code,
                    'parent_id': detail.parent_id,
                    'child_id': detail.child_id,
                    'memo_cn': detail.memo_cn,
                    'total_amount': float(detail.total_amount)
                    if detail.total_amount
                    else 0,
                    'per_amount': float(detail.per_amount)
                    if detail.per_amount
                    else 0,
                    'drawing_version': detail.drawing_version,
                    'location_code': detail.location_code,
                    'package_group': detail.package_group,
                    'material_state': detail.material_state,
                    'material_type': detail.material_type,
                    'mac_ele': detail.mac_ele,
                    'm_attribute': detail.m_attribute,
                    'dept': detail.dept,
                    'drawing_no': detail.drawing_no,
                    'is_production_critical': detail.is_production_critical,
                    'is_cbb': detail.is_cbb,
                    'plan_default': detail.plan_default,
                    'manu_part_no': detail.manu_part_no,
                    'net_weight': detail.net_weight,
                    'w_unit': detail.w_unit,
                    'surface_area': detail.surface_area,
                    'surface_deal': detail.surface_deal,
                    'max_size': detail.max_size,
                    'applyer': detail.applyer,
                    'brand': detail.brand,
                    'draw_number': detail.draw_number,
                    'mat_group': detail.mat_group,
                    'mat_template': detail.mat_template,
                    'material': detail.material,
                    'product_line': detail.product_line,
                    'unit': detail.unit,
                    'package': detail.package,
                    'master_version': detail.master_version,
                    'in_out_factory': detail.in_out_factory,
                    'wbs_project_num': detail.wbs_project_num,
                    'factory_no': detail.factory_no,
                    'release_date': detail.release_date.isoformat()
                    if detail.release_date
                    else None,
                    'change_date': detail.change_date.isoformat()
                    if detail.change_date
                    else None,
                    'check_status': detail.check_status,
                    'jx_match': detail.jx_match,
                    'upload_time': detail.upload_time.isoformat()
                    if detail.upload_time
                    else None,
                    'depth': detail.depth,
                    'component_part': detail.component_part,
                }
            )

        return {
            'success': True and bom_items,
            'message': '导出成功' if bom_items else '无数据',
            'data': {
                'erp': erp,
                'inv_code': inv_code,
                'bom_version': bom_master.version,
                'query_time': bom_master.query_time.isoformat(),
                'factory_code': bom_master.factory_code,
                'wbs_project_number': bom_master.wbs_project_number,
                'total_items': bom_master.total_items,
                'items': bom_items,
            },
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'导出BOM数据失败: {str(e)}',
            'data': None,
        }


@daily_router.get('/api/v1/bom-analysis/bom/{erp}/details')
async def get_bom_details(
    erp: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db_dependency),
):
    """
    Get BOM details for a specific ERP with pagination
    """
    try:
        result = get_bom_data_by_erp(db, erp, page, page_size)

        if not result['success']:
            raise HTTPException(status_code=404, detail=result['message'])

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error retrieving BOM details: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving BOM details: {str(e)}',
        )


@daily_router.get('/api/v1/bom-analysis/bom/{erp}/change-log')
async def get_bom_changes(
    erp: str,
    start_date: Optional[str] = Query(
        None, description='Start date (YYYY-MM-DD)'
    ),
    end_date: Optional[str] = Query(None, description='End date (YYYY-MM-DD)'),
    db: Session = Depends(get_db_dependency),
):
    """
    Get BOM change log for a specific ERP
    """
    try:
        start_time = (
            datetime.strptime(start_date, '%Y-%m-%d') if start_date else None
        )
        end_time = (
            datetime.strptime(end_date, '%Y-%m-%d') if end_date else None
        )

        # If end_date is provided, set it to end of day
        if end_time:
            end_time = end_time + timedelta(days=1) - timedelta(seconds=1)

        result = get_bom_change_log(db, erp, start_time, end_time)

        if not result['success']:
            raise HTTPException(status_code=404, detail=result['message'])

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error retrieving BOM change log: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving BOM change log: {str(e)}',
        )


@daily_router.get('/api/v1/bom-analysis/bom/change-log/erp-list')
async def get_erps_with_changes(
    start_date: Optional[str] = Query(
        None, description='Start date (YYYY-MM-DD)'
    ),
    end_date: Optional[str] = Query(None, description='End date (YYYY-MM-DD)'),
    db: Session = Depends(get_db_dependency),
):
    """
    Get list of all ERPs that have BOM change logs
    """
    try:
        start_time = (
            datetime.strptime(start_date, '%Y-%m-%d') if start_date else None
        )
        end_time = (
            datetime.strptime(end_date, '%Y-%m-%d') if end_date else None
        )

        # If end_date is provided, set it to end of day
        if end_time:
            end_time = end_time + timedelta(days=1) - timedelta(seconds=1)

        result = get_erps_with_change_logs(db, start_time, end_time)

        if not result['success']:
            raise HTTPException(status_code=404, detail=result['message'])

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error retrieving ERPs with change logs: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving ERPs with change logs: {str(e)}',
        )


@daily_router.get('/api/v1/bom-analysis/bom/{erp}/export')
async def export_full_bom(
    background_tasks: BackgroundTasks,  # 添加为依赖项
    erp: str,
    db: Session = Depends(get_db_dependency),
):
    """
    Export complete BOM data for a specific ERP as an Excel file
    """
    try:
        result = export_bom_data(db, erp)

        if not result['success']:
            raise HTTPException(status_code=404, detail=result['message'])

        # Create a temporary file for the Excel
        with tempfile.NamedTemporaryFile(
            suffix='.xlsx', delete=False
        ) as temp_file:
            temp_filename = temp_file.name

        # Extract data for Excel
        bom_data = result['data']
        bom_items = bom_data['items']

        # Convert to DataFrame
        df = pd.DataFrame(bom_items)

        # Create Excel writer
        with pd.ExcelWriter(temp_filename, engine='openpyxl') as writer:
            # Add main BOM data sheet
            df.to_excel(writer, sheet_name='BOM Details', index=False)

        # Return the Excel file
        filename = f"BOM_Export_{erp}_{bom_data['bom_version']}.xlsx"
        headers = {'Content-Disposition': f'attachment; filename="{filename}"'}

        background_tasks.add_task(os.unlink, temp_filename)

        return FileResponse(
            path=temp_filename,
            filename=filename,
            headers=headers,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error exporting BOM data: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error exporting BOM data: {str(e)}',
        )


# Request models
class NoteCreate(BaseModel):
    note_content: Optional[str] = Field(
        None, description='Content of the note'
    )
    note_content_reverse: Optional[str] = Field(
        None, description='Content of the note'
    )


class NoteUpdate(BaseModel):
    note_content: Optional[str] = Field(
        None, description='Updated content of the note'
    )
    note_content_reverse: Optional[str] = Field(
        None, description='Updated content of the note'
    )


# Response models
class NoteResponse(BaseModel):
    id: int
    erp_number: str
    part_id: str
    note_content: Optional[str]
    note_content_reverse: Optional[str]
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[str] = None
    version: int

    class Config:
        orm_mode = True


# Now, add these endpoints to your daily_router


@daily_router.post(
    '/api/v1/bom-analysis/notes/{note_id}/reactivate',
    response_model=NoteResponse,
)
async def reactivate_note(
    note_id: int,
    user_id: str,
    db: Session = Depends(get_db_dependency),
):
    """
    Reactivate a historical note version
    """
    try:
        reactivated_note = reactivate_part_note(
            db=db, history_note_id=note_id, username=user_id
        )
        return reactivated_note
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error reactivating note: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error reactivating note: {str(e)}'
        )


@daily_router.post(
    '/api/v1/bom-analysis/notes/{erp_number}/{part_id}',
    response_model=NoteResponse,
)
async def create_note(
    erp_number: str,
    part_id: str,
    note_data: NoteCreate,
    user_id: str,
    db: Session = Depends(get_db_dependency),
):
    """
    Create a new note for a specific ERP and part
    """
    try:
        new_note = create_part_note(
            db=db,
            erp_number=erp_number,
            part_id=part_id,
            note_content=note_data.note_content,
            note_content_reverse=note_data.note_content_reverse,
            username=user_id,
        )
        return new_note
    except Exception as e:
        logger.error(f'Error creating note: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error creating note: {str(e)}'
        )


@daily_router.get(
    '/api/v1/bom-analysis/notes/{erp_number}/latest',
    response_model=List[NoteResponse],
)
async def get_erp_latest_notes(
    erp_number: str, db: Session = Depends(get_db_dependency)
):
    """
    Get all the latest notes for a specific ERP
    """
    try:
        notes = get_latest_erp_notes(db, erp_number)
        return notes
    except Exception as e:
        logger.error(f'Error retrieving notes: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error retrieving notes: {str(e)}'
        )


@daily_router.get(
    '/api/v1/bom-analysis/notes/{erp_number}/{part_id}/history',
    response_model=List[NoteResponse],
)
async def get_part_notes_history(
    erp_number: str, part_id: str, db: Session = Depends(get_db_dependency)
):
    """
    Get the complete history of notes for a specific part
    """
    try:
        notes = get_part_note_history(db, erp_number, part_id)
        return notes
    except Exception as e:
        logger.error(f'Error retrieving note history: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error retrieving note history: {str(e)}'
        )


@daily_router.get(
    '/api/v1/bom-analysis/notes/{erp_number}/{part_id}/latest',
    response_model=NoteResponse,
)
async def get_part_latest_note(
    erp_number: str, part_id: str, db: Session = Depends(get_db_dependency)
):
    """
    Get the latest note for a specific part
    """
    try:
        note = (
            db.query(BomPartNote)
            .filter(
                BomPartNote.erp_number == erp_number,
                BomPartNote.part_id == part_id,
                BomPartNote.is_active == True,
            )
            .first()
        )

        if not note:
            raise HTTPException(
                status_code=404, detail='No note found for this part'
            )

        return note
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error retrieving latest note: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error retrieving latest note: {str(e)}'
        )


@daily_router.put(
    '/api/v1/bom-analysis/notes/{note_id}', response_model=NoteResponse
)
async def update_note(
    note_id: int,
    note_data: NoteUpdate,
    user_id: str,
    db: Session = Depends(get_db_dependency),
):
    """
    Update an existing note
    """
    try:
        updated_note = update_part_note(
            db=db,
            note_id=note_id,
            new_content=note_data.note_content,
            new_content_reverse=note_data.note_content_reverse,
            username=user_id,
        )
        return updated_note
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error updating note: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error updating note: {str(e)}'
        )


@daily_router.delete(
    '/api/v1/bom-analysis/notes/{note_id}', response_model=Dict[str, bool]
)
async def delete_note(
    note_id: int,
    user_id: str,
    db: Session = Depends(get_db_dependency),
):
    """
    Delete (deactivate) a note
    """
    try:
        success = delete_part_note(db, note_id, user_id)
        return {'success': success}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error deleting note: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error deleting note: {str(e)}'
        )


@daily_router.get(
    '/api/v1/bom-analysis/daily/erp/{erp_number}/bom-time-comparison'
)
async def get_bom_time_comparison(
    erp_number: str,
    db: Session = Depends(get_db_dependency),
):
    """
    Get BOM data comparison for specified ERP based on bom_time from erp_base_info
    Returns data for:
    - bom_time = current bom_time + 1 day from erp_base_info (延后一天)
    - bom_time = current bom_time + 1 day - 7 days (延后一天后再往前6天)
    """
    try:
        logger.info(f'Querying BOM time comparison for ERP: {erp_number}')

        # 首先从erp_base_info获取该ERP的bom_time
        get_bom_time_sql = text(
            """
            SELECT bom_time, ERP
            FROM erp_base_info
            WHERE ERP = :erp_number
            LIMIT 1
        """
        )

        bom_time_result = db.execute(
            get_bom_time_sql, {'erp_number': erp_number}
        ).fetchone()

        if not bom_time_result or not bom_time_result.bom_time:
            raise HTTPException(
                status_code=404,
                detail=f'未找到ERP {erp_number} 或该ERP没有bom_time数据',
            )

        # 延后一天的逻辑：当前bom_time + 1天
        original_bom_time = bom_time_result.bom_time
        current_bom_time = original_bom_time + timedelta(days=1)
        seven_days_ago = current_bom_time - timedelta(days=7)

        logger.info(
            f'Original bom_time: {original_bom_time}, '
            f'Adjusted current_bom_time (延后一天): {current_bom_time}, '
            f'seven_days_ago: {seven_days_ago}'
        )

        # 查询延后一天的当天和6天前的BOM数据
        comparison_sql = text(
            """
            SELECT
                'current' as data_type,
                bom.*
            FROM bom_analysis_daily_parts_error_counts_backup bom
            WHERE bom.erp_number = :erp_number
                AND DATE(bom.create_date) = :current_date

            UNION ALL

            SELECT
                'seven_days_ago' as data_type,
                bom.*
            FROM bom_analysis_daily_parts_error_counts_backup bom
            WHERE bom.erp_number = :erp_number
                AND DATE(bom.create_date) = :seven_days_ago

            ORDER BY data_type, part_id, cal_type
        """
        )

        # 执行查询
        results = db.execute(
            comparison_sql,
            {
                'erp_number': erp_number,
                'current_date': current_bom_time,
                'seven_days_ago': seven_days_ago,
            },
        ).fetchall()

        return {
            'erp_number': erp_number,
            'original_bom_time': original_bom_time.isoformat(),
            'current_bom_time': current_bom_time.isoformat(),
            'seven_days_ago': seven_days_ago.isoformat(),
            'data': [dict(result._mapping) for result in results],
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f'Error querying BOM time comparison: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error querying BOM time comparison: {str(e)}',
        )


# ==================== 物料过滤规则 API ====================


@daily_router.get(
    '/api/v1/bom-analysis/daily/material-filter-rules',
    response_model=List[MaterialFilterRuleResponse],
)
async def get_material_filter_rules(
    is_included: Optional[bool] = Query(
        None, description='过滤类型：True=包含规则，False=排除规则，None=全部'
    ),
    db: Session = Depends(get_db_dependency),
):
    """获取物料过滤规则列表"""
    try:
        if is_included is not None:
            rules = get_material_filter_rules_by_type(db, is_included)
        else:
            rules = get_all_material_filter_rules(db)

        return [
            MaterialFilterRuleResponse.model_validate(rule.to_dict())
            for rule in rules
        ]

    except Exception as e:
        logger.error(f'Error getting material filter rules: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取物料过滤规则失败: {str(e)}')


@daily_router.get(
    '/api/v1/bom-analysis/daily/material-filter-rules/{rule_id}',
    response_model=MaterialFilterRuleResponse,
)
async def get_material_filter_rule(
    rule_id: int, db: Session = Depends(get_db_dependency)
):
    """根据ID获取物料过滤规则"""
    try:
        rule = get_material_filter_rule_by_id(db, rule_id)
        if not rule:
            raise HTTPException(
                status_code=404, detail=f'未找到ID为 {rule_id} 的物料过滤规则'
            )

        return MaterialFilterRuleResponse.model_validate(rule.to_dict())

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'Error getting material filter rule {rule_id}: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取物料过滤规则失败: {str(e)}')


@daily_router.post(
    '/api/v1/bom-analysis/daily/material-filter-rules',
    response_model=MaterialFilterRuleResponse,
)
async def create_material_filter_rule_api(
    rule_data: MaterialFilterRuleCreate,
    db: Session = Depends(get_db_dependency),
):
    """创建新的物料过滤规则"""
    try:
        new_rule = create_material_filter_rule(
            db=db,
            description=rule_data.description,
            is_included=rule_data.is_included,
        )

        logger.info(
            f'Created material filter rule: {rule_data.description} (included: {rule_data.is_included})'
        )
        return MaterialFilterRuleResponse.model_validate(new_rule.to_dict())

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f'Error creating material filter rule: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建物料过滤规则失败: {str(e)}')


@daily_router.put(
    '/api/v1/bom-analysis/daily/material-filter-rules/{rule_id}',
    response_model=MaterialFilterRuleResponse,
)
async def update_material_filter_rule_api(
    rule_id: int,
    rule_data: MaterialFilterRuleUpdate,
    db: Session = Depends(get_db_dependency),
):
    """更新物料过滤规则"""
    try:
        updated_rule = update_material_filter_rule(
            db=db,
            rule_id=rule_id,
            description=rule_data.description,
            is_included=rule_data.is_included,
            is_active=rule_data.is_active,
        )

        if not updated_rule:
            raise HTTPException(
                status_code=404, detail=f'未找到ID为 {rule_id} 的物料过滤规则'
            )

        logger.info(
            f'Updated material filter rule {rule_id}: {rule_data.description} (included: {rule_data.is_included})'
        )
        return MaterialFilterRuleResponse.model_validate(
            updated_rule.to_dict()
        )

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(
            f'Error updating material filter rule {rule_id}: {str(e)}'
        )
        raise HTTPException(status_code=500, detail=f'更新物料过滤规则失败: {str(e)}')


@daily_router.delete(
    '/api/v1/bom-analysis/daily/material-filter-rules/{rule_id}'
)
async def delete_material_filter_rule_api(
    rule_id: int, db: Session = Depends(get_db_dependency)
):
    """删除物料过滤规则"""
    try:
        success = delete_material_filter_rule(db, rule_id)

        if not success:
            raise HTTPException(
                status_code=404, detail=f'未找到ID为 {rule_id} 的物料过滤规则'
            )

        logger.info(f'Deleted material filter rule {rule_id}')
        return {'message': f'物料过滤规则 {rule_id} 已删除'}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f'Error deleting material filter rule {rule_id}: {str(e)}'
        )
        raise HTTPException(status_code=500, detail=f'删除物料过滤规则失败: {str(e)}')

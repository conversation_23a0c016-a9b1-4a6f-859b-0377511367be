from fastapi import APIRouter
from fastapi import (
    File,
    UploadFile,
    Query,
    Depends,
    HTTPException,
    Form,
)
import shutil
import os
import re
from io import StringIO
from typing import List, Optional
from pydantic import BaseModel

import pandas as pd
from sqlalchemy import create_engine

# from bom_breaker import run as run_bom_breaker
from bom_drive import run as run_bom_drive
from bom_cables_count_by_line import run as run_bom_cables_count_by_line
from bom_motor_count_by_line import run as run_bom_motor_count_by_line
from bom_components_count_by_line import (
    run as run_bom_components_count_by_line,
)
from bom_history_analyzer import run as run_bom_history_analyzer
from airline_connector_count import run as run_airline_connector_count

# from boom_drive import run as run_boom_drive
from boom_vs_bom import run as run_boom_vs_bom
from cabinet_vs_bom import run as run_cabinet_vs_bom

# from bom_network_terminal_analyzer import (
#     run as run_bom_network_terminal_analyzer,
# )
from io_table_analyzer import run as run_io_table_analyzer
from sqlalchemy.orm import Session

from db_operations import (
    create_new_session,
    update_session_activity,
    get_session_by_id,
    get_uploaded_files_by_session,
    update_session_erp_number,
    add_calculation_result,
    add_uploaded_file,
    update_session_status,
    add_error_count,
    get_error_counts_by_session,
    get_latest_session_by_erp,
    get_latest_type_file_by_erp,
)

from core.dependencies import get_db_dependency
from core.config import settings

from descriptions import CALCULATION_TYPE_DESCRIPTIONS

router = APIRouter()


class CalculationResultRequest(BaseModel):
    notFed: List[str]
    alreadyFed: List[str]
    electricalMaterials: bool
    externalMaterials: bool
    ioTable: Optional[bool] = None
    selectedPartNumbers: Optional[List[str]] = None  # 新增字段
    historyBomErp: Optional[str] = None
    historyBomCount: Optional[int] = None
    historyBomPercent: Optional[float] = None


class CalculationRequest(BaseModel):
    calculations: CalculationResultRequest


class AvailableCalculation(BaseModel):
    notFed: List[str]
    alreadyFed: List[str]
    electricalMaterials: bool
    externalMaterials: bool
    ioTable: bool


class CalculationResponse(BaseModel):
    success: bool
    available_calculations: AvailableCalculation
    uploaded_file_types: List[str]


# 定义过滤函数
def filter_part_numbers(df):
    # 创建部件编号的副本以进行处理
    part_numbers = df['部件编号'].astype(str)

    # 检查包含字母的部件编号
    contains_letters = part_numbers.str.contains('[A-Za-z]', na=False)

    # 处理数字部分（包括小数点）
    # 将部件编号转换为浮点数进行比较，忽略转换错误
    numeric_values = pd.to_numeric(part_numbers, errors='coerce')
    greater_than_99 = numeric_values > 99

    # 合并过滤条件
    final_mask = contains_letters | greater_than_99

    # 返回过滤后的数据框
    filtered_df = df[final_mask].copy()

    # 保持原始格式
    return filtered_df


def filter_by_part_numbers(
    df: pd.DataFrame, part_numbers: List[str], file_type: str
) -> pd.DataFrame:
    """
    按部件编号筛选数据框

    Args:
        df: 要筛选的数据框
        part_numbers: 部件编号列表
        file_type: 文件类型 ('1' 为机电沟通表, '2'/'3'/'4' 为BOM表)
    Returns:
        筛选后的数据框
    """
    if not part_numbers:
        return df

    if file_type == '1':  # 机电沟通表
        # 确保部件编号格式统一
        df['部件编号'] = df['部件编号'].fillna('')
        df['部件编号筛选'] = df['部件编号'].apply(
            lambda x: str(int(x)) if isinstance(x, float) else str(x)
        )
        df['部件编号筛选'] = df['部件编号筛选'].apply(lambda x: x.zfill(2))

        df = df[df['部件编号筛选'].isin(part_numbers)]
        df = df.drop('部件编号筛选', axis=1)
        return df

    else:  # BOM表
        # 提取部件号
        df['所属部件'] = df['所属部件'].fillna('')
        df['部件编号'] = df['所属部件'].apply(extract_second_part)
        # 只保留有效的部件号
        valid_part_df = df[df['部件编号'].notna() & (df['部件编号'] != '')]

        # 格式化部件号
        valid_part_df = valid_part_df.copy()
        valid_part_df['部件编号'] = valid_part_df['部件编号'].apply(
            lambda x: x.zfill(2)
        )

        # 根据部件号筛选

        valid_part_df = valid_part_df[valid_part_df['部件编号'].isin(part_numbers)]
        valid_part_df = valid_part_df.drop('部件编号', axis=1)
        return valid_part_df


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    return value.split('-')[1] if match else ''


@router.post('/calculate')
async def calculate(
    req: str = Form(...),
    file: Optional[UploadFile] = File(None),
    session_id: str = Query(..., description='用户会话ID'),
    db: Session = Depends(get_db_dependency),
):
    req = CalculationRequest.parse_raw(req)
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    uploaded_files = get_uploaded_files_by_session(db, session_id)
    # if len(uploaded_files) < 4:
    #     raise HTTPException(
    #         status_code=400, detail='Not all required files uploaded'
    #     )

    # 创建数据库引擎
    engine = create_engine(settings.DATABASE_URL)

    try:
        # 从MySQL读取数据到DataFrame
        df = pd.read_sql_table('mech_motor_details', engine)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f'Error reading table from database: {str(e)}',
        )

    user_result_dir = os.path.join(
        'results', session.erp_number, session.user_id, session_id
    )
    os.makedirs(user_result_dir, exist_ok=True)

    # 创建临时文件目录
    temp_dir = os.path.join(user_result_dir, 'temp')
    os.makedirs(temp_dir, exist_ok=True)

    calculation_results = []

    uploaded_files = {
        file.file_type: file.file_path for file in uploaded_files
    }

    # if uploaded_files.get('2'):
    #     res = run_bom_network_terminal_analyzer(
    #         uploaded_files['2'],  # BOM表
    #         user_result_dir,
    #     )
    #     calculation_results.extend(res)

    # for item in req.calculations.notFed:
    #     if item == 'drive':

    if req.calculations.historyBomErp:
        if 'bomhistorycheck' not in req.calculations.alreadyFed:
            req.calculations.alreadyFed.append('bomhistorycheck')

    for item in req.calculations.alreadyFed:
        # 从MySQL读取数据到DataFrame
        mapping_df = pd.read_sql(
            'SELECT two_digit_code, three_digit_code FROM bom_parts_mapping',
            engine,
        )
        code_mapping = pd.Series(
            mapping_df['three_digit_code'].values,
            index=mapping_df['two_digit_code'],
        ).to_dict()
        res = None
        if item == 'motor':
            selected_part_numbers = req.calculations.selectedPartNumbers or []
            mech_file_path = uploaded_files.get('1')
            bom_file_path = uploaded_files.get('2')
            # 如果有选择部件号，处理文件1和文件2
            if selected_part_numbers and (
                uploaded_files.get('1') or uploaded_files.get('2')
            ):
                try:
                    # 处理机电沟通表（文件1）
                    if uploaded_files.get('1'):
                        mech_df = pd.read_excel(uploaded_files['1'])
                        filtered_mech_df = filter_by_part_numbers(
                            mech_df.copy(), selected_part_numbers, '1'
                        )
                        filtered_mech_path = os.path.join(
                            temp_dir, 'filtered_mech_comm.xlsx'
                        )
                        filtered_mech_df.to_excel(
                            filtered_mech_path, index=False
                        )
                        mech_file_path = filtered_mech_path

                    # 处理BOM表（文件2）
                    if uploaded_files.get('2'):
                        bom_df = pd.read_excel(uploaded_files['2'])
                        filtered_bom_df = filter_by_part_numbers(
                            bom_df.copy(), selected_part_numbers, '2'
                        )
                        filtered_bom_path = os.path.join(
                            temp_dir, 'filtered_bom.xlsx'
                        )
                        filtered_bom_df.to_excel(
                            filtered_bom_path, index=False
                        )
                        bom_file_path = filtered_bom_path

                except Exception as e:
                    raise HTTPException(
                        status_code=500,
                        detail=f'Error filtering files: {str(e)}',
                    )
            try:
                res = run_bom_motor_count_by_line(
                    bom_file_path,  # BOM表
                    mech_file_path,  # 机电沟通表
                    df.copy(),
                    user_result_dir,
                    code_mapping=code_mapping,
                )
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f'伺服电机比对异常: {str(e)}',
                )
        elif item == 'drive':
            res = run_bom_drive(
                uploaded_files['2'],  # BOM表
                uploaded_files['1'],  # 机电沟通表
                df.copy(),
                user_result_dir,
                code_mapping=code_mapping,
            )
        elif item == 'cable':
            res = run_bom_cables_count_by_line(
                uploaded_files['2'],  # BOM表
                uploaded_files['1'],  # 机电沟通表
                df.copy(),
                user_result_dir,
                code_mapping=code_mapping,
            )
        # elif item == 'breaker':
        #     res = run_bom_breaker(
        #         uploaded_files['2'],  # BOM表
        #         uploaded_files['1'],  # 机电沟通表
        #         df.copy(),
        #         user_result_dir,
        #         code_mapping=code_mapping,
        #     )
        elif item == 'mechcheck':
            res = run_bom_components_count_by_line(
                uploaded_files['2'],  # BOM表
                uploaded_files['1'],  # 机电沟通表
                df.copy(),
                user_result_dir,
                code_mapping=code_mapping,
            )
        elif item == 'bomhistorycheck':
            try:
                # 从MySQL读取数据到DataFrame
                material_info = pd.read_sql_table(
                    'bom_machine_type_material_info', engine
                )
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f'Error reading table from database: {str(e)}',
                )
            if req.calculations.historyBomErp:
                if file:
                    history_session_id = await get_latest_session_by_erp(
                        db, req.calculations.historyBomErp
                    )
                    if not history_session_id:
                        history_session = create_new_session(db, 'uploader')
                        history_session = update_session_erp_number(
                            db,
                            history_session.id,
                            req.calculations.historyBomErp,
                        )
                        history_session_id = history_session.id

                    # 1. 保存上传文件
                    user_upload_dir = os.path.join(
                        'uploads',
                        req.calculations.historyBomErp,
                        session.user_id,
                        history_session_id,
                    )
                    os.makedirs(user_upload_dir, exist_ok=True)
                    file_path = os.path.join(user_upload_dir, file.filename)
                    content = await file.read()
                    # 写入文件
                    with open(file_path, 'wb') as buffer:
                        buffer.write(content)

                    add_uploaded_file(
                        db,
                        history_session_id,
                        file.filename,
                        file_path,
                        '2',
                        os.path.getsize(file_path),
                    )
                else:
                    file_obj = await get_latest_type_file_by_erp(
                        db, req.calculations.historyBomErp, '2'
                    )
                    if not file_obj:
                        raise HTTPException(
                            status_code=404,
                            detail='历史文件未找到',
                        )

                    file_path = file_obj.file_path

                res = run_bom_history_analyzer(
                    uploaded_files['2'],  # BOM表
                    file_path,
                    material_info,
                    user_result_dir,
                    session.erp_number,
                    req.calculations.historyBomErp,
                )
        elif item == 'airline':
            res = run_airline_connector_count(
                uploaded_files['2'],  # BOM表
                user_result_dir,
            )

        if res:
            calculation_results.extend(res)

    if req.calculations.electricalMaterials:
        res = run_boom_vs_bom(
            uploaded_files['3'],  # BOM表
            uploaded_files['2'],  # 机电沟通表
            df.copy(),
            user_result_dir,
        )
        calculation_results.extend(res)

    if req.calculations.ioTable:
        eplan_df = pd.read_excel(uploaded_files['5'])
        try:
            # 从MySQL读取数据到DataFrame
            io_df = pd.read_sql_query(
                f"SELECT * FROM io_version_info WHERE ERP='{session.erp_number}'",
                engine,
            )
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f'Error reading table from database: {str(e)}',
            )
        res = run_io_table_analyzer(
            eplan_df,  # EPlan IO表
            io_df,  # IO表
            user_result_dir,
        )
        calculation_results.extend(res)

    if req.calculations.externalMaterials:
        res = run_cabinet_vs_bom(
            uploaded_files['4'],  # BOM表
            uploaded_files['2'],  # 机电沟通表
            df.copy(),
            user_result_dir,
        )
        calculation_results.extend(res)

    # if uploaded_files.get('2'):
    #     res = run_airline_connector_count(
    #         uploaded_files['2'],  # BOM表
    #         user_result_dir,
    #     )
    #     calculation_results.extend(res)

    for result in calculation_results:
        add_calculation_result(db, session_id, result['type'], result['path'])
        if (
            result['type']
            in [
                'drive',
                'motor',
                'cable',
                'bomhistorycheck',
                'electric',
                'cabinet',
                'mechcheck',
                'iotable',
                'airline',
            ]
            and result['path'].endswith('.xlsx')
            and os.path.exists(result['path'])
            and not req.calculations.selectedPartNumbers
        ):
            # Count errors and total rows
            df = pd.read_excel(result['path'])
            if result['type'] == 'drive':
                error_count = abs(
                    df['数量差异'].iloc[-1]
                )  # Assuming '异常列' is the column name for errors
                total_count = int(
                    df['总数量_df_db'].iloc[-1].split('(')[0].strip()
                )
            elif result['type'] == 'cabinet':
                error_count = df['数量差异'].abs().sum()
                # Assuming '异常列' is the column name for errors
                # if pd.isna(df['投料总数量'].iloc[-1]):
                total_count = df['物料清单数量'].sum()
                # else:
                #    total_count = df['投料总数量'].iloc[-1]
            elif result['type'] == 'electric':
                error_count = (
                    df['数量差异'].abs().sum()
                )  # Assuming '异常列' is the column name for errors
                # if pd.isna(df['投料总数量'].iloc[-1]):
                #     total_count = (
                #         0
                #         if pd.isna(df['预投料数量'].iloc[-1])
                #         else df['预投料数量'].iloc[-1]
                #     )
                # else:
                total_count = df['投料总数量'].sum()
            elif result['type'] == 'airline':
                error_count = df['数量差异'].abs().sum()
                total_count = df['公头数量'].sum()
            elif result['type'] == 'bomhistorycheck':
                error_count = 0
                if '异常原因' not in df.columns:
                    df['异常原因'] = ''
                if '负责人' not in df.columns:
                    df['负责人'] = ''
                for idx, row in df.iterrows():
                    if row['物料差异'] != 0:
                        if (
                            req.calculations.historyBomCount
                            and abs(row['物料差异'])
                            <= req.calculations.historyBomCount
                        ):
                            df.loc[idx, '异常原因'] = '系统忽略'
                            df.loc[idx, '负责人'] = '系统'
                        if (
                            req.calculations.historyBomPercent
                            and row[f'{session.erp_number}物料数量(当前)'] != 0
                            and abs(
                                row['物料差异']
                                / row[f'{session.erp_number}物料数量(当前)']
                            )
                            <= req.calculations.historyBomPercent
                        ):
                            df.loc[idx, '异常原因'] = '系统忽略'
                            df.loc[idx, '负责人'] = '系统'
                        error_count = error_count + 1
                total_count = len(df)

                def highlight_row(row):
                    if row['物料差异'] > 0 and row['异常原因'] != '系统忽略':
                        return ['background-color: red'] * len(row)
                    if row['物料差异'] < 0 and row['异常原因'] != '系统忽略':
                        return ['background-color: yellow'] * len(row)
                    return [''] * len(row)

                # Apply styling and save to Excel
                styled_df = df.style.apply(highlight_row, axis=1)
                styled_df.to_excel(result['path'], index=False)
                error_count = len(
                    df[
                        (df['异常原因'].isna() | (df['异常原因'] == ''))
                        & (
                            df['物料差异'].notna()
                            & (df['物料差异'] != '')
                            & (df['物料差异'] != 0)
                        )
                    ]
                )
                total_count = len(df)
            else:
                error_count = (
                    df['异常'].fillna(0).sum()
                )  # Assuming '异常列' is the column name for errors
                total_count = len(df)

                if result['type'] in ['motor', 'cable', 'mechcheck']:
                    df_filtered = filter_part_numbers(df)
                    error_count_filtered = df_filtered['异常'].fillna(0).sum()
                    total_count_filtered = len(df_filtered)
                    add_error_count(
                        db,
                        session_id,
                        f"{result['type']}_om",
                        error_count_filtered,
                        total_count_filtered,
                    )

            # Save error count to database
            add_error_count(
                db, session_id, result['type'], error_count, total_count
            )

    update_session_activity(db, session_id)
    update_session_status(db, session_id, 'completed')

    # Get all error counts for this session
    error_counts = get_error_counts_by_session(db, session_id)
    error_count_summary = [
        {
            'file_type': ec.cal_type,
            'error_count': ec.error_count,
            'total_count': ec.total_count,
        }
        for ec in error_counts
    ]

    return {'success': True, 'error_count_summary': error_count_summary}


@router.get('/check_available_calculations')
async def check_available_calculations(
    session_id: str = Query(..., description='用户会话ID'),
    db: Session = Depends(get_db_dependency),
) -> CalculationResponse:
    """
    检查当前session可用的计算方式
    """
    # 获取已上传的文件
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    uploaded_files = get_uploaded_files_by_session(db, session_id)
    uploaded_file_types = [str(file.file_type) for file in uploaded_files]

    # 定义所有可能的计算类型及其需要的文件
    calculation_definitions = {
        # "network_terminal": {
        #     "name": "网络端子分析",
        #     "description": "分析网络端子连接情况",
        #     "required_files": ["2"],  # 只需要BOM表
        # },
        'not_fed': {
            'value': ['drive'],
            'name': 'drive',
            'description': '分析未上料的驱动器情况',
            'required_files': ['1', '3'],  # 需要机电沟通表和BOM表
        },
        'already_fed': {
            'value': [
                'motor',
                'drive',
                'cable',
                'breaker',
                'mechcheck',
                'bomhistorycheck',
            ],
            'name': '已上料断路器分析',
            'description': '分析已上料的断路器情况',
            'required_files': ['1', '2'],  # 需要机电沟通表和BOM表
        },
        'electrical_materials': {
            'value': True,
            'name': '电气物料分析',
            'description': '分析电气物料情况',
            'required_files': ['2', '3'],  # 需要BOM表和机电沟通表
        },
        'external_materials': {
            'value': True,
            'name': '外购件分析',
            'description': '分析外购件情况',
            'required_files': ['2', '4'],  # 需要BOM表和外购件表
        },
        'io_table': {
            'value': True,
            'name': 'Elpan图纸IO点位表分析',
            'description': '8Elpan图纸IO点位表校验',
            'required_files': ['5'],  # 需要Eplan IO表
        },
        'airline': {
            'value': True,
            'name': '航插公母头匹配检查表',
            'description': '9航插公母头匹配检查表',
            'required_files': ['2'],  # 需要Eplan IO表
        },
    }

    # 检查每种计算类型的可用性
    available_calculations = {
        'notFed': [],
        'alreadyFed': [],
        'electricalMaterials': False,
        'externalMaterials': False,
        'ioTable': False,
        'airline': False,
    }
    for calc_type, definition in calculation_definitions.items():
        required_files = definition['required_files']
        missing_files = [
            f for f in required_files if f not in uploaded_file_types
        ]

        if not missing_files:
            if calc_type == 'electrical_materials':
                available_calculations['electricalMaterials'] = definition[
                    'value'
                ]
            elif calc_type == 'external_materials':
                available_calculations['externalMaterials'] = definition[
                    'value'
                ]
            elif calc_type == 'airline':
                available_calculations['airline'] = definition['value']
            elif calc_type == 'io_table':
                available_calculations['ioTable'] = definition['value']
            elif calc_type == 'not_fed':
                available_calculations['notFed'] = definition['value']
            elif calc_type == 'already_fed':
                available_calculations['alreadyFed'] = definition['value']
    if available_calculations['electricalMaterials']:
        available_calculations['alreadyFed'].append('electricalMaterials')
    if available_calculations['externalMaterials']:
        available_calculations['alreadyFed'].append('externalMaterials')
    if available_calculations['ioTable']:
        available_calculations['alreadyFed'].append('ioTable')
    if available_calculations['airline']:
        available_calculations['alreadyFed'].append('airline')
    return CalculationResponse(
        success=True,
        available_calculations=available_calculations,
        uploaded_file_types=uploaded_file_types,
    )


@router.post('/upload')
async def upload_file(
    file: UploadFile = File(...),
    step: str = Query(..., description='当前上传的文件类型'),
    is_text_input: str = Form(...),
    session_id: str = Query(..., description='用户会话ID'),
    db: Session = Depends(get_db_dependency),
):
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    user_upload_dir = os.path.join(
        'uploads', session.erp_number, session.user_id, session.id
    )
    os.makedirs(user_upload_dir, exist_ok=True)

    is_text_input = is_text_input.lower() == 'true'

    if is_text_input:
        file_content = await file.read()
        file_name = f'{step}.xlsx'
        content = file_content.decode('utf-8')
        df = pd.read_csv(StringIO(content), sep='\t')
        file_path = os.path.join(user_upload_dir, file_name)
        df.to_excel(file_path, index=False)
    else:
        file_name = f'{step}_{file.filename}'
        file_path = os.path.join(user_upload_dir, file_name)
        with open(file_path, 'wb') as buffer:
            shutil.copyfileobj(file.file, buffer)

    new_file = add_uploaded_file(
        db, session_id, file_name, file_path, step, os.path.getsize(file_path)
    )
    update_session_activity(db, session_id)
    return {'filename': new_file.filename, 'step': new_file.file_type}


@router.get('/calculation_descriptions')
async def get_calculation_descriptions():
    """
    获取所有计算类型的解释性描述
    返回每种计算类型的标题、描述、异常定义等信息
    """
    return {'success': True, 'data': CALCULATION_TYPE_DESCRIPTIONS}


@router.get('/calculation_description/{cal_type}')
async def get_calculation_description(cal_type: str):
    """
    获取指定计算类型的解释性描述

    :param cal_type: 计算类型
    :return: 该类型的描述信息
    """
    if cal_type not in CALCULATION_TYPE_DESCRIPTIONS:
        raise HTTPException(status_code=404, detail=f"计算类型 '{cal_type}' 不存在")

    return {'success': True, 'data': CALCULATION_TYPE_DESCRIPTIONS[cal_type]}

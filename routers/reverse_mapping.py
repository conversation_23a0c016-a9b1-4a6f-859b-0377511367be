from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile
from sqlalchemy.orm import Session
from typing import Optional
from pydantic import BaseModel
from core.dependencies import get_db_dependency
from core.logger import logger
from db_operations import (
    get_all_reverse_mapping,
    get_total_reverse_mapping_count,
    get_reverse_mapping_by_id,
    create_reverse_mapping,
    update_reverse_mapping,
    delete_reverse_mapping,
    batch_upsert_reverse_mappings,
)
from io import StringIO
import pandas as pd

router = APIRouter()


# 请求和响应模型
class BomReverseMappingCreate(BaseModel):
    inv_code: str
    memo_cn: str
    reverse_inv_code: str
    reverse_memo_cn: str


class BomReverseMappingUpdate(BaseModel):
    inv_code: Optional[str] = None
    memo_cn: Optional[str] = None
    reverse_inv_code: Optional[str] = None
    reverse_memo_cn: Optional[str] = None


# 路由定义
@router.get('/api/v1/bom-analysis/daily/reverse_mapping')
async def list_reverse_mappings(
    skip: int = Query(0, description='跳过的记录数'),
    limit: int = Query(10, description='返回的记录数'),
    search: Optional[str] = Query(None, description='搜索关键词'),
    sort_field: str = Query('id', description='排序字段'),
    sort_order: str = Query('asc', description='排序方向 (asc/desc)'),
    db: Session = Depends(get_db_dependency),
):
    """获取航插信息列表"""
    try:
        connectors = get_all_reverse_mapping(
            db, skip, limit, search, sort_field, sort_order
        )
        total = get_total_reverse_mapping_count(db, search)

        return {
            'success': True,
            'data': [item.to_dict() for item in connectors],
            'total': total,
            'page': skip // limit + 1,
            'page_size': limit,
        }
    except Exception as e:
        logger.error(f'Error getting airline connectors: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving airline connectors: {str(e)}',
        )


@router.get('/api/v1/bom-analysis/daily/reverse_mapping/{connector_id}')
async def get_reverse_mapping(
    connector_id: int, db: Session = Depends(get_db_dependency)
):
    """获取特定航插信息"""
    connector = get_reverse_mapping_by_id(db, connector_id)
    if not connector:
        raise HTTPException(status_code=404, detail='Connector not found')
    return {'success': True, 'data': connector.to_dict()}


@router.post('/api/v1/bom-analysis/daily/reverse_mapping')
async def add_reverse_mapping(
    connector: BomReverseMappingCreate,
    db: Session = Depends(get_db_dependency),
):
    """创建新的航插记录"""
    try:
        new_connector = create_reverse_mapping(
            db,
            connector.inv_code,
            connector.memo_cn,
            connector.reverse_inv_code,
            connector.reverse_memo_cn,
        )
        return {'success': True, 'data': new_connector.to_dict()}
    except Exception as e:
        logger.error(f'Error creating airline connector: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error creating airline connector: {str(e)}',
        )


@router.put('/api/v1/bom-analysis/daily/reverse_mapping/{connector_id}')
async def update_reverse_mapping_api(
    connector_id: int,
    connector: BomReverseMappingUpdate,
    db: Session = Depends(get_db_dependency),
):
    """更新航插信息"""
    updated_connector = update_reverse_mapping(
        db,
        connector_id,
        connector.inv_code,
        connector.memo_cn,
        connector.reverse_inv_code,
        connector.reverse_memo_cn,
    )

    if not updated_connector:
        raise HTTPException(status_code=404, detail='Connector not found')

    return {'success': True, 'data': updated_connector.to_dict()}


@router.delete('/api/v1/bom-analysis/daily/reverse_mapping/{connector_id}')
async def delete_reverse_mapping_api(
    connector_id: int, db: Session = Depends(get_db_dependency)
):
    """删除航插记录"""
    success = delete_reverse_mapping(db, connector_id)
    if not success:
        raise HTTPException(status_code=404, detail='Connector not found')

    return {'success': True, 'message': 'Connector deleted successfully'}


@router.post('/api/v1/bom-analysis/daily/reverse_mapping/batch-import')
async def batch_import_reverse_mappings(
    file: UploadFile = File(..., description='包含正反机映射的制表符分隔文件(.tsv 或 .txt)'),
    db: Session = Depends(get_db_dependency),
):
    """
    通过上传制表符分隔的文本文件(.tsv)批量导入正反机映射关系.
    文件内容应为UTF-8编码，表头必须包含: inv_code, memo_cn, reverse_inv_code, reverse_memo_cn
    """
    # (可选) 检查文件扩展名，建议用户上传 .tsv 或 .txt 文件
    if not file.filename.endswith(('.tsv', '.txt', '.csv')):
        logger.warning(
            f"File '{file.filename}' does not have a standard text file extension."
        )
        # 这里可以选择不报错，继续尝试解析

    try:
        # 1. 异步读取文件的全部内容 (字节)
        content_bytes = await file.read()

        # 2. 将字节解码为字符串 (假设为 UTF-8 编码)
        try:
            content_str = content_bytes.decode('utf-8')
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=400, detail='文件编码错误. 请确保文件为 UTF-8 编码.'
            )

        # 3. 使用 StringIO 将字符串包装成文件对象，并用 pandas 读取
        df = pd.read_csv(StringIO(content_str), sep='\t')

        # 验证表头是否包含所有必需的列
        required_columns = {
            '正机物料编码',
            '正机物料描述',
            '反机物料编码',
            '反机物料描述',
        }
        if not required_columns.issubset(df.columns):
            missing_cols = required_columns - set(df.columns)
            raise HTTPException(
                status_code=400, detail=f"文件缺少必需的列: {', '.join(missing_cols)}"
            )

        # 将NaN值替换为None，以避免数据库错误
        df = df.where(pd.notna(df), None)

        # 将DataFrame转换为字典列表
        records = df.rename(
            {
                '正机物料编码': 'inv_code',
                '正机物料描述': 'memo_cn',
                '反机物料编码': 'reverse_inv_code',
                '反机物料描述': 'reverse_memo_cn',
            },
            axis=1,
        ).to_dict(orient='records')
        if not records:
            raise HTTPException(status_code=400, detail='文件为空或格式不正确，没有可导入的数据.')

        # 调用数据库操作函数进行批量创建
        created_count = batch_upsert_reverse_mappings(db=db, mappings=records)

        return {'success': True, 'message': f'成功导入 {created_count} 条记录.'}

    except Exception as e:
        logger.error(f'Error during batch import: {str(e)}')
        if isinstance(e, HTTPException):
            raise e
        # pd.read_csv 可能会因格式问题抛出错误，这些是客户端错误 (400)
        if 'Error tokenizing data' in str(e):
            raise HTTPException(
                status_code=400,
                detail=f'文件内容格式错误，请确保是正确的制表符分隔文件: {str(e)}',
            )
        raise HTTPException(
            status_code=500,
            detail=f'处理文件时发生意外错误: {str(e)}',
        )

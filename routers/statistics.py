from fastapi import APIRouter
from fastapi import (
    Depends,
    HTTPException,
    Query,
)
from typing import Optional
from sqlalchemy.orm import Session
from db_operations import (
    get_erp_calculation_count,
    get_latest_erp_calculation_errors,
    get_erp_calculation_history,
    get_all_erp_cross_session_statistics,
    get_session_by_id,
    update_error_count,
)

from core.dependencies import get_current_user_id, get_db_dependency
from core.logger import logger

router = APIRouter()


@router.get('/erp_statistics/{erp_number}')
async def get_erp_statistics(
    erp_number: str,
    db: Session = Depends(get_db_dependency),
):
    calculation_count = get_erp_calculation_count(db, erp_number)
    latest_erp_calculation = get_latest_erp_calculation_errors(db, erp_number)
    calculation_history = get_erp_calculation_history(db, erp_number)

    return {
        'erp_number': erp_number,
        'calculation_count': calculation_count,
        'latest_calculation': latest_erp_calculation,
        'calculation_history': calculation_history,
    }


@router.get('/all_erp_statistics')
async def get_all_erp_statistics(
    db: Session = Depends(get_db_dependency),
    token_user_id: str = Depends(get_current_user_id),
):
    """
    Get statistics for all ERPs in the system
    Returns a list of ERPs with their latest calculation results and error statistics
    """
    try:
        statistics = get_all_erp_cross_session_statistics(db)
        return {
            'success': True,
            'data': statistics,
            'total_erps': len(statistics),
        }
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f'Error getting ERP statistics: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'Error retrieving ERP statistics: {str(e)}',
        )


@router.post('/update_error_count')
async def update_error_count_endpoint(
    session_id: str = Query(..., description='用户会话ID'),
    cal_type: str = Query(..., description='计算类型'),
    error_count: Optional[int] = Query(..., description='错误数'),
    total_count: Optional[int] = Query(..., description='总数'),
    manual_confirm: Optional[bool] = Query(False, description='是否手动确认'),
    db: Session = Depends(get_db_dependency),
):
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    updated_record = update_error_count(
        db, session_id, cal_type, error_count, total_count, manual_confirm
    )
    if not updated_record:
        raise HTTPException(
            status_code=404, detail='Error count record not found'
        )

    return {'success': True, 'message': 'Error count updated successfully'}

from fastapi import APIRouter
from fastapi import (
    Query,
    Depends,
    HTTPException,
)
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel

from sqlalchemy.orm import Session
from db_operations import (
    get_error_reason,
    create_error_reason,
    delete_error_reason,
    update_error_reason,
    get_error_reasons,
)

from core.dependencies import get_db_dependency

router = APIRouter()


class ErrorReasonCreate(BaseModel):
    erp_number: int
    comment: str


class ErrorReasonUpdate(BaseModel):
    comment: str


class ErrorReasonResponse(BaseModel):
    id: int
    erp_number: int
    comment: str
    create_time: datetime


@router.post('/error_reasons/', response_model=ErrorReasonResponse)
async def create_reason(
    reason: ErrorReasonCreate,
    db: Session = Depends(get_db_dependency),
    user_id: str = Query(..., description='用户ID'),
):
    """创建新的错误原因评论"""
    try:
        error_reason = create_error_reason(
            db, reason.erp_number, reason.comment, user_id
        )
        return error_reason
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get('/error_reasons/', response_model=List[ErrorReasonResponse])
async def list_reasons(
    erp_number: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db_dependency),
):
    """获取错误原因列表"""
    try:
        reasons = get_error_reasons(db, erp_number, skip, limit)
        return reasons
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get('/error_reasons/{reason_id}', response_model=ErrorReasonResponse)
async def get_reason(
    reason_id: int,
    db: Session = Depends(get_db_dependency),
):
    """获取特定错误原因详情"""
    reason = get_error_reason(db, reason_id)
    if reason is None:
        raise HTTPException(status_code=404, detail='Error reason not found')
    return reason


@router.put('/error_reasons/{reason_id}', response_model=ErrorReasonResponse)
async def update_reason(
    reason_id: int,
    reason: ErrorReasonUpdate,
    db: Session = Depends(get_db_dependency),
    user_id: str = Query(..., description='用户ID'),
):
    """更新错误原因评论"""
    updated_reason = update_error_reason(
        db, reason_id, reason.comment, user_id
    )
    if updated_reason is None:
        raise HTTPException(status_code=404, detail='Error reason not found')
    return updated_reason


@router.delete('/error_reasons/{reason_id}')
async def delete_reason(
    reason_id: int,
    db: Session = Depends(get_db_dependency),
    user_id: str = Query(..., description='用户ID'),
):
    """删除错误原因评论"""
    success = delete_error_reason(db, reason_id, user_id)
    if not success:
        raise HTTPException(status_code=404, detail='Error reason not found')
    return {'status': 'success', 'message': 'Error reason deleted'}

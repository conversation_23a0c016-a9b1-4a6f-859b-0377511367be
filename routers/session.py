from fastapi import APIRouter
from core.dependencies import get_current_user_id, get_db_dependency
from fastapi import (
    Query,
    Depends,
    HTTPException,
)

import os

from typing import Optional


import pandas as pd
from sqlalchemy import text

from sqlalchemy.orm import Session

from db_operations import (
    create_new_session,
    get_session_by_id,
    update_session_erp_number,
    get_sessions,
    add_uploaded_file,
)
from core.logger import logger


router = APIRouter()


@router.post('/create_session')
async def create_session(
    user_id: str = Query(..., description='用户ID'),
    db: Session = Depends(get_db_dependency),
    token_user_id: str = Depends(get_current_user_id),
):
    if not user_id:
        user_id = token_user_id
    if not user_id:
        raise HTTPException(
            status_code=401, detail='Could not validate credentials'
        )
    new_session = create_new_session(db, user_id)
    return {'session_id': new_session.id}


@router.get('/get_sessions')
async def api_get_sessions(
    user_id: Optional[str] = Query(None, description='用户ID'),
    erp_number: Optional[str] = Query(None, description='ERP编号'),
    status: Optional[str] = Query(None, description='状态'),
    db: Session = Depends(get_db_dependency),
    token_user_id: str = Depends(get_current_user_id),
):
    if not user_id:
        user_id = token_user_id
    if not user_id:
        raise HTTPException(
            status_code=401, detail='Could not validate credentials'
        )
    sessions = get_sessions(db, user_id, erp_number, status)
    return {'sessions': [session.to_dict() for session in sessions]}


@router.post('/record_erp')
async def record_erp(
    erp_number: str,
    session_id: str = Query(..., description='用户会话ID'),
    download_mech_info: bool = Query(True, description='是否下载机电沟通表'),
    download_hard_info: bool = Query(True, description='是否下载柜外清单'),
    db: Session = Depends(get_db_dependency),
):
    # 先获取 session
    session = get_session_by_id(db, session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    try:
        # 创建保存目录
        user_upload_dir = os.path.join(
            'uploads', erp_number, session.user_id, session_id
        )
        os.makedirs(user_upload_dir, exist_ok=True)
        if download_mech_info:
            find_data = True
            query = text(
                """
                SELECT
                    id AS `序号`,
                    componentEngineer AS `部件工程师`,
                    revisionDate AS `修订日期`,
                    partNumber AS `部件编号`,
                    emCN AS `EM中文`,
                    emName AS `EM名称`,
                    deviceIdentifier AS `设备标识符`,
                    functionDescription AS `功能描述`,
                    category AS `类别`,
                    brand AS `品牌`,
                    model AS `型号`,
                    airInterface AS `气接口`,
                    initialPosition AS `初始位`,
                    valveModel AS `阀型号`,
                    valveIslandOrSingleValve AS `阀岛/单片阀`,
                    singleOrDoubleControl AS `单/双控`,
                    extensionDetection AS `伸位检测`,
                    retractionDetection AS `缩位检测`,
                    note1 AS `备注1`,
                    note2 AS `备注2`,
                    positiveLimit AS `正.极限`,
                    negativeLimit AS `负.极限`,
                    origin AS `原点`,
                    passDragChain AS `是否过拖链`,
                    reductionRatio AS `减速比`,
                    `lead` AS `导程`,
                    rollerDiameter AS `辊径`,
                    motorRotationDirection AS `电机旋转方向`,
                    inertiaRatio AS `惯量比`
                FROM erp_mech_info WHERE ERP = :erp_number
            """
            )
            result = db.execute(query, {'erp_number': erp_number})
            data = result.fetchall()

            # 保存为Excel文件
            excel_file_name = f'mech_info_{erp_number}.xlsx'
            file_path = os.path.join(user_upload_dir, excel_file_name)

            # 将查询结果转换为DataFrame
            df = pd.DataFrame(data)
            if not df.empty:
                df.to_excel(file_path, index=False)

                # 记录文件信息
                add_uploaded_file(
                    db,
                    session_id,
                    excel_file_name,
                    file_path,
                    1,
                    os.path.getsize(file_path),
                )
            else:
                find_data = False

        if download_hard_info:
            find_hard_info = True
            query = text(
                """
                SELECT
                    partNumber as '部件',
                    functionDescription as '功能描述',
                    materialCode as '物料编号',
                    model as '型号',
                    CASE
                        WHEN sum REGEXP '^[0-9]+$' THEN sum
                        ELSE NULL
                    END as '总数量',
                    brand as '品牌'
                FROM inner_hard_info
                WHERE erp = :erp_number and is_delete = 0
            """
            )
            result = db.execute(query, {'erp_number': erp_number})
            data = result.fetchall()

            # 保存为Excel文件
            excel_file_name = f'hard_info_{erp_number}.xlsx'
            file_path = os.path.join(user_upload_dir, excel_file_name)

            # 将查询结果转换为DataFrame
            df = pd.DataFrame(data)
            if not df.empty:
                df.to_excel(file_path, index=False)

                # 记录文件信息
                add_uploaded_file(
                    db,
                    session_id,
                    excel_file_name,
                    file_path,
                    4,
                    os.path.getsize(file_path),
                )
            else:
                find_hard_info = False

        # 更新session的erp编号
        session = update_session_erp_number(db, session_id, erp_number)
        if not session:
            raise HTTPException(status_code=404, detail='Session not found')

    except Exception as e:
        logger.error(f'Error processing ERP data: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error processing ERP data: {str(e)}'
        )

    return {
        'success': True,
        'find_data': find_data,
        'find_hard_info': find_hard_info,
    }

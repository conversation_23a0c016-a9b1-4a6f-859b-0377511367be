import uuid
from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Date,
    Text,
    Index,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, timezone, date

# 定义数据库模型
Base = declarative_base()


class SessionModel(Base):
    __tablename__ = 'bom_analysis_sessions'

    id = Column(
        String(36),
        primary_key=True,
        index=True,
        default=lambda: str(uuid.uuid4()),
    )
    user_id = Column(String(36), index=True)
    erp_number = Column(String(50))
    created_at = Column(DateTime, default=datetime.now(timezone.utc))
    last_activity = Column(
        DateTime,
        default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc),
    )
    status = Column(
        String(20), default='active'
    )  # 例如：active, completed, expired

    # 关联的上传文件和计算结果
    uploaded_files = relationship('UploadedFile', back_populates='session')
    calculation_results = relationship(
        'CalculationResult', back_populates='session'
    )
    error_counts = relationship('ErrorCount', back_populates='session')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'erp_number': self.erp_number,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'status': self.status,
        }


class UploadedFile(Base):
    __tablename__ = 'bom_analysis_uploaded_files'

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(36), ForeignKey('bom_analysis_sessions.id'))
    filename = Column(String(255))
    file_path = Column(String(512))
    file_type = Column(String(50))
    upload_time = Column(DateTime, default=datetime.now(timezone.utc))
    file_size = Column(Integer)  # 以字节为单位

    session = relationship('SessionModel', back_populates='uploaded_files')


class CalculationResult(Base):
    __tablename__ = 'bom_analysis_calculation_results'

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(36), ForeignKey('bom_analysis_sessions.id'))
    calculation_type = Column(String(50))
    result_file_path = Column(String(512))
    calculation_time = Column(DateTime, default=datetime.now(timezone.utc))
    is_latest = Column(Boolean, default=False)

    session = relationship(
        'SessionModel', back_populates='calculation_results'
    )


# New model for error count
class ErrorCount(Base):
    __tablename__ = 'bom_analysis_error_counts'

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(36), ForeignKey('bom_analysis_sessions.id'))
    cal_type = Column(String(50))
    error_count = Column(Integer)
    total_count = Column(Integer)
    manual_confirm = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now(timezone.utc))
    is_latest = Column(Boolean, default=False)

    session = relationship('SessionModel', back_populates='error_counts')

    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'cal_type': self.cal_type,
            'error_count': self.error_count,
            'total_count': self.total_count,
            'manual_confirm': self.manual_confirm,
            'create_time': self.create_time.isoformat()
            if self.create_time
            else None,
        }


class MachineTypeMaterialInfo(Base):
    __tablename__ = 'bom_machine_type_material_info'

    id = Column(Integer, primary_key=True, index=True)
    machine_type = Column(String(255))
    material_detail = Column(String(255))
    anotation = Column(String(255))

    def to_dict(self):
        return {
            'id': self.id,
            'machine_type': self.machine_type,
            'material_detail': self.material_detail,
            'anotation': self.anotation,
        }


class ErrorReason(Base):
    __tablename__ = 'bom_analysis_error_reasons'

    id = Column(Integer, primary_key=True, index=True)
    create_user = Column(String(50))
    erp_number = Column(String(50))
    comment = Column(String(255))
    create_time = Column(DateTime, default=datetime.now(timezone.utc))

    def to_dict(self):
        return {
            'id': self.id,
            'create_user': self.create_user,
            'erp_number': self.erp_number,
            'comment': self.comment,
            'create_time': self.create_time.isoformat()
            if self.create_time
            else None,
        }


class PartsErrorCount(Base):
    __tablename__ = 'bom_analysis_parts_error_counts'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50))
    part_id = Column(String(50))
    error_count = Column(Integer)
    total_count = Column(Integer)
    create_time = Column(DateTime, default=datetime.now(timezone.utc))
    cal_type = Column(String(50))

    def to_dict(self):
        return {
            'id': self.id,
            'erp_number': self.erp_number,
            'part_id': self.part_id,
            'error_count': self.error_count,
            'total_count': self.total_count,
            'create_time': self.create_time.isoformat()
            if self.create_time
            else None,
            'cal_type': self.cal_type,
        }


class PartsCalculationResult(Base):
    __tablename__ = 'bom_analysis_parts_calculation_results'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50))
    part_id = Column(String(50))
    result_file_path = Column(String(512))
    calculation_time = Column(DateTime, default=datetime.now(timezone.utc))


class PartsUploadedFile(Base):
    __tablename__ = 'bom_analysis_parts_uploaded_files'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50))
    part_id = Column(String(50))
    file_path = Column(String(512))
    file_name = Column(String(50))
    upload_time = Column(DateTime, default=datetime.now(timezone.utc))
    file_size = Column(Integer)  # 以字节为单位


# Daily parts error count table
class DailyPartsErrorCount(Base):
    __tablename__ = 'bom_analysis_daily_parts_error_counts'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50))
    part_id = Column(String(50))
    error_count = Column(Integer)
    total_count = Column(Integer)
    create_time = Column(DateTime, default=datetime.now)
    create_date = Column(Date, default=date.today)
    cal_type = Column(String(50))

    def to_dict(self):
        return {
            'id': self.id,
            'erp_number': self.erp_number,
            'part_id': self.part_id,
            'error_count': self.error_count,
            'total_count': self.total_count,
            'create_time': self.create_time.isoformat()
            if self.create_time
            else None,
            'create_date': self.create_date.isoformat()
            if self.create_date
            else None,
            'cal_type': self.cal_type,
        }


# Daily parts calculation result table
class DailyPartsCalculationResult(Base):
    __tablename__ = 'bom_analysis_daily_parts_calculation_results'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50))
    part_id = Column(String(50))
    result_file_path = Column(String(512))
    calculation_time = Column(DateTime, default=datetime.now)
    calculation_date = Column(Date, default=date.today)

    def to_dict(self):
        return {
            'id': self.id,
            'erp_number': self.erp_number,
            'part_id': self.part_id,
            'result_file_path': self.result_file_path,
            'calculation_time': self.calculation_time.isoformat()
            if self.calculation_time
            else None,
            'calculation_date': self.calculation_date.isoformat()
            if self.calculation_date
            else None,
        }


# Daily parts uploaded file table
class DailyPartsUploadedFile(Base):
    __tablename__ = 'bom_analysis_daily_parts_uploaded_files'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50))
    part_id = Column(String(50))
    file_path = Column(String(512))
    file_name = Column(String(255))
    upload_time = Column(DateTime, default=datetime.now)
    upload_date = Column(Date, default=date.today)
    file_size = Column(Integer)  # Size in bytes

    def to_dict(self):
        return {
            'id': self.id,
            'erp_number': self.erp_number,
            'part_id': self.part_id,
            'file_path': self.file_path,
            'file_name': self.file_name,
            'upload_time': self.upload_time.isoformat()
            if self.upload_time
            else None,
            'upload_date': self.upload_date.isoformat()
            if self.upload_date
            else None,
            'file_size': self.file_size,
        }


class AirlineConnectorInfo(Base):
    __tablename__ = 'bom_airline_connector_info'

    id = Column(Integer, primary_key=True, index=True)
    male_connector_code = Column(String(255))
    male_connector_name = Column(String(255))
    female_connector_code = Column(String(255))
    female_connector_name = Column(String(255))
    remark = Column(String(255))

    def to_dict(self):
        return {
            'id': self.id,
            'male_connector_code': self.male_connector_code,
            'male_connector_name': self.male_connector_name,
            'female_connector_code': self.female_connector_code,
            'female_connector_name': self.female_connector_name,
            'remark': self.remark,
        }


class BomPartNote(Base):
    __tablename__ = 'bom_part_notes'

    id = Column(Integer, primary_key=True, index=True)
    erp_number = Column(String(50), nullable=False)
    part_id = Column(String(50), nullable=False)
    note_content = Column(Text, nullable=True)
    note_content_reverse = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(100), nullable=True)
    updated_at = Column(DateTime, nullable=True)
    version = Column(Integer, nullable=False, default=1)

    # Define indexes
    __table_args__ = (
        Index('idx_erp_part', erp_number, part_id),
        Index('idx_erp_active', erp_number, is_active),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'erp_number': self.erp_number,
            'part_id': self.part_id,
            'note_content': self.note_content,
            'note_content_reverse': self.note_content_reverse,
            'is_active': self.is_active,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat()
            if self.created_at
            else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat()
            if self.updated_at
            else None,
            'version': self.version,
        }


class BomReverseMapping(Base):
    __tablename__ = 'bom_reverse_mapping'

    id = Column(Integer, primary_key=True, index=True)
    inv_code = Column(String(255))
    memo_cn = Column(String(255))
    reverse_inv_code = Column(String(255))
    reverse_memo_cn = Column(String(255))
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(100))
    updated_at = Column(DateTime, default=datetime.now)

    def to_dict(self):
        return {
            'id': self.id,
            'inv_code': self.inv_code,
            'memo_cn': self.memo_cn,
            'reverse_inv_code': self.reverse_inv_code,
            'reverse_memo_cn': self.reverse_memo_cn,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat()
            if self.created_at
            else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat()
            if self.updated_at
            else None,
        }


class MaterialFilterRule(Base):
    """物料描述过滤规则表"""

    __tablename__ = 'material_filter_rules'

    id = Column(Integer, primary_key=True, index=True)
    description = Column(String(500), nullable=False, unique=True)
    is_included = Column(Boolean, nullable=False)  # True表示包含，False表示排除
    created_at = Column(DateTime, default=datetime.now(timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc),
    )
    is_active = Column(Boolean, default=True)

    # 添加索引
    __table_args__ = (
        Index('idx_description_active', 'description', 'is_active'),
        Index('idx_included_active', 'is_included', 'is_active'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'description': self.description,
            'is_included': self.is_included,
            'created_at': self.created_at.isoformat()
            if self.created_at
            else None,
            'updated_at': self.updated_at.isoformat()
            if self.updated_at
            else None,
            'is_active': self.is_active,
        }

CREATE TABLE IF NOT EXISTS BOM_MASTER (
    id INT AUTO_INCREMENT PRIMARY KEY,                  -- 自增主键
    root_inv_code VARCHAR(50) NOT NULL,                 -- 根物料编码
    factory_code VARCHAR(20) NOT NULL,                  -- 工厂编码
    wbs_project_number VARCHAR(50),                     -- WBS项目号
    query_time DATETIME NOT NULL,                       -- 查询时间
    is_latest BOOLEAN DEFAULT TRUE,                     -- 是否为最新版本
    version INT DEFAULT 1,                              -- 版本号
    total_items INT DEFAULT 0,                          -- 总物料数
    remark VARCHAR(255),                                -- 备注
    INDEX idx_root_inv_code (root_inv_code),
    INDEX idx_query_time (query_time),
    INDEX idx_is_latest (is_latest)
);

CREATE TABLE IF NOT EXISTS BOM_DETAIL (
    id VARCHAR(50) PRIMARY KEY,                     -- 物料在BOM中的唯一ID
    master_id INT NOT NULL,                         -- 关联到BOM_MASTER的ID

    -- 核心层级关系字段
    parent_inv_code VARCHAR(50),                    -- 父物料编码(ParentInvCode)
    inv_code VARCHAR(50) NOT NULL,                  -- 物料编码(cInvCode)
    parent_id VARCHAR(50),                          -- 父ID(ParentID)
    child_id VARCHAR(50),                           -- 子ID(ID)
    old_parent_id VARCHAR(50),                      -- 数据库ID(OLDParentID)

    -- 数量和版本信息
    total_amount DECIMAL(18,4) DEFAULT 1,           -- 总数量(计算字段)
    per_amount DECIMAL(18,4) DEFAULT 1,             -- 数量(dPerAmount)
    drawing_version VARCHAR(50),                    -- 版本(cDrawingVersion)

    -- 位置和分组信息
    location_code VARCHAR(100),                     -- 位号(cLocationCode)
    package_group VARCHAR(100),                     -- 组批包(cPackageGroup)

    -- 来源信息
    source VARCHAR(50),                             -- 来源(cSource)
    convert_batch VARCHAR(50),                      -- 样机转批量(cConvertBatch)
    brand_4_bom VARCHAR(50),                        -- 品牌来源BOM树(Brand4Bom)

    -- 物料基本信息
    memo_cn VARCHAR(255),                           -- 新物料中文描述(cMemoCN)
    material_state VARCHAR(50),                     -- 物料状态(cMaterialState)
    material_type VARCHAR(50),                      -- 物料类型(cMaterialType)
    mac_ele VARCHAR(50),                            -- 机械电气(cMacEle)
    m_attribute VARCHAR(100),                       -- 工艺属性(cMAttribute)
    dept VARCHAR(50),                               -- 实验室办公室(cDept)
    drawing_no VARCHAR(100),                        -- 关联图号(cDrawingNo)

    -- 物料特殊属性
    is_production_critical VARCHAR(10),             -- 是否生产关键物料(cIsMatProductionCritical)
    is_cbb VARCHAR(10),                             -- 是否CBB(cIsCBB)
    old_memo VARCHAR(255),                          -- 旧物料描述(cOldMemo)
    plan_default VARCHAR(50),                       -- 计划默认属性(cPlanDefault)
    manu_part_no VARCHAR(100),                      -- 制造商零件号(cManuPartNo)

    -- 物理属性
    net_weight VARCHAR(20),                       -- 净重(dNetWeight)
    w_unit VARCHAR(20),                             -- 重量单位(cWUnit)
    surface_area VARCHAR(20),                     -- 表面积(dSurfaceArea)
    surface_deal VARCHAR(100),                      -- 表面处理(cSurfaceDeal)
    max_size VARCHAR(100),                          -- 大小量纲(cMaxSize)

    -- 责任和品牌信息
    applyer VARCHAR(50),                            -- 负责人(cApplyer)
    brand VARCHAR(50),                              -- 品牌(cBrand)
    draw_number VARCHAR(50),                        -- 旧编码(cDrawNumber)

    -- 物料分类信息
    mat_group VARCHAR(50),                          -- 物料组(cMatGroup)
    mat_template VARCHAR(100),                      -- 物料模板(cMatTemplate)
    material VARCHAR(100),                          -- 材料(cMaterial)
    product_line VARCHAR(100),                      -- 产品线(cProductLine)

    -- 单位和封装信息
    unit VARCHAR(20),                               -- 计量单位(cUnit)
    package VARCHAR(50),                            -- 封装(cPackage)

    -- 系统信息
    master_version VARCHAR(50),                     -- 主数据版本(cMasterVersion)
    in_out_factory VARCHAR(20),                     -- 厂内厂外(cInOutFactory)
    wbs_project_num VARCHAR(50),                    -- WBS项目号(WBSProjectNum)
    factory_no VARCHAR(50),                         -- 工厂号(FactoryNo)

    -- 时间信息
    release_date DATETIME,                          -- 发布时间(cReleaseDate)
    change_date DATETIME,                           -- 变更日期(rChangeDate)

    -- 状态信息
    check_status VARCHAR(50),                       -- 发布状态(CheckStatus)
    jx_match VARCHAR(50),                           -- 机械齐套(jxMatch)
    upload_time DATETIME,                           -- 上传时间(uploadTime)

    -- 其他信息
    remark VARCHAR(255),                            -- 备注(remark)
    depth INT DEFAULT 0,                            -- 层级(计算字段)
    component_part VARCHAR(100),                    -- 所属部件(计算字段)

    FOREIGN KEY (master_id) REFERENCES BOM_MASTER(id),
    INDEX idx_master_id (master_id),
    INDEX idx_inv_code (inv_code),
    INDEX idx_parent_inv_code (parent_inv_code)
);

CREATE TABLE IF NOT EXISTS BOM_CHANGE_LOG (
    log_id INT AUTO_INCREMENT PRIMARY KEY,              -- 日志ID
    root_inv_code VARCHAR(50) NOT NULL,                 -- 根物料编码
    prev_version INT,                                   -- 前一版本号
    curr_version INT NOT NULL,                          -- 当前版本号
    inv_code VARCHAR(50) NOT NULL,                      -- 物料编码
    parent_inv_code VARCHAR(50),                        -- 父物料编码
    component_part VARCHAR(100),                        -- 部件编号
    memo_cn VARCHAR(255),                               -- 物料描述
    change_type ENUM('ADD','MODIFY','DELETE') NOT NULL, -- 变更类型
    change_time DATETIME NOT NULL,                      -- 变更时间
    change_fields JSON,                                 -- 具体变更内容

    INDEX idx_root_inv_code (root_inv_code),
    INDEX idx_inv_code (inv_code),
    INDEX idx_change_time (change_time)
);

-- Daily parts error count table
CREATE TABLE IF NOT EXISTS `bom_analysis_daily_parts_error_counts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `erp_number` varchar(50) DEFAULT NULL,
  `part_id` varchar(50) DEFAULT NULL,
  `error_count` int DEFAULT NULL,
  `total_count` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `create_date` date DEFAULT (CURRENT_DATE),
  `cal_type` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bom_analysis_daily_parts_error_counts_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Daily parts calculation result table
CREATE TABLE IF NOT EXISTS `bom_analysis_daily_parts_calculation_results` (
  `id` int NOT NULL AUTO_INCREMENT,
  `erp_number` varchar(50) DEFAULT NULL,
  `part_id` varchar(50) DEFAULT NULL,
  `result_file_path` varchar(512) DEFAULT NULL,
  `calculation_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `calculation_date` date DEFAULT (CURRENT_DATE),
  PRIMARY KEY (`id`),
  KEY `idx_bom_analysis_daily_parts_calculation_results_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Daily parts uploaded file table
CREATE TABLE IF NOT EXISTS `bom_analysis_daily_parts_uploaded_files` (
  `id` int NOT NULL AUTO_INCREMENT,
  `erp_number` varchar(50) DEFAULT NULL,
  `part_id` varchar(50) DEFAULT NULL,
  `file_path` varchar(512) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `upload_date` date DEFAULT (CURRENT_DATE),
  `file_size` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bom_analysis_daily_parts_uploaded_files_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

UPDATE bom_analysis_parts_error_counts SET cal_type = '' WHERE cal_type IS NULL;

from sqlalchemy import create_engine, select, func
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import distinct
import click
from typing import List

# 假设你的配置模块名为 config
from core.config import settings
from models import CalculationResult, ErrorCount, SessionModel


def get_all_erp_numbers(db) -> List[str]:
    """获取数据库中所有的ERP numbers"""
    return [
        r[0]
        for r in db.query(distinct(SessionModel.erp_number))
        .filter(SessionModel.erp_number.isnot(None))
        .all()
    ]


def update_is_latest_calculation_results_for_erp(db, erp_number: str):
    """更新指定ERP number的CalculationResult记录的is_latest状态"""
    print(f'Processing CalculationResult for ERP: {erp_number}')

    # 获取相关的session ids
    session_ids = select(SessionModel.id).where(
        SessionModel.erp_number == erp_number
    )

    # 先将该ERP的所有记录设为False
    records_count = (
        db.query(CalculationResult)
        .filter(CalculationResult.session_id.in_(session_ids))
        .update(
            {CalculationResult.is_latest: False}, synchronize_session=False
        )
    )

    print(f'Reset {records_count} records to is_latest=False')

    # 获取每个calculation_type的最新记录信息
    subq = (
        db.query(
            CalculationResult.id.label('result_id'),
            CalculationResult.calculation_type,
            SessionModel.last_activity,
            SessionModel.id.label('session_id'),
        )
        .join(SessionModel, CalculationResult.session_id == SessionModel.id)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
            CalculationResult.result_file_path.endswith('.xlsx'),
        )
        .subquery()
    )

    latest_records = (
        db.query(func.max(subq.c.result_id).label('latest_id'))
        .group_by(subq.c.calculation_type)
        .all()
    )

    if latest_records:
        record_ids = [r[0] for r in latest_records]
        updated_count = (
            db.query(CalculationResult)
            .filter(CalculationResult.id.in_(record_ids))
            .update(
                {CalculationResult.is_latest: True}, synchronize_session=False
            )
        )
        print(f'Updated {updated_count} records to is_latest=True')

    db.commit()


def update_is_latest_error_counts_for_erp(db, erp_number: str):
    """更新指定ERP number的ErrorCount记录的is_latest状态"""
    print(f'Processing ErrorCount for ERP: {erp_number}')

    # 获取相关的session ids
    session_ids = select(SessionModel.id).where(
        SessionModel.erp_number == erp_number
    )

    # 先将该ERP的所有记录设为False
    records_count = (
        db.query(ErrorCount)
        .filter(ErrorCount.session_id.in_(session_ids))
        .update({ErrorCount.is_latest: False}, synchronize_session=False)
    )

    print(f'Reset {records_count} records to is_latest=False')

    # 获取每个cal_type的最新记录信息
    subq = (
        db.query(
            ErrorCount.id.label('result_id'),
            ErrorCount.cal_type,
            SessionModel.last_activity,
            SessionModel.id.label('session_id'),
        )
        .join(SessionModel, ErrorCount.session_id == SessionModel.id)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
        )
        .subquery()
    )

    latest_records = (
        db.query(func.max(subq.c.result_id).label('latest_id'))
        .group_by(subq.c.cal_type)
        .all()
    )

    if latest_records:
        record_ids = [r[0] for r in latest_records]
        updated_count = (
            db.query(ErrorCount)
            .filter(ErrorCount.id.in_(record_ids))
            .update({ErrorCount.is_latest: True}, synchronize_session=False)
        )
        print(f'Updated {updated_count} records to is_latest=True')

    db.commit()


@click.command()
@click.option(
    '--erp',
    help='Specific ERP number to update. If not provided, will update all ERPs.',
)
@click.option(
    '--table',
    type=click.Choice(['all', 'calculation', 'error']),
    default='all',
    help='Which table to update',
)
def main(erp: str, table: str):
    """更新数据库中的is_latest字段"""
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()

    try:
        if erp:
            erp_numbers = [erp]
        else:
            erp_numbers = get_all_erp_numbers(db)
            erp_numbers = [erp for erp in erp_numbers if len(erp) == 5]
            print(f'Found {len(erp_numbers)} ERP numbers to process')

        for erp_number in erp_numbers:
            print(f'\nProcessing ERP number: {erp_number}')

            if table in ['all', 'calculation']:
                update_is_latest_calculation_results_for_erp(db, erp_number)

            if table in ['all', 'error']:
                update_is_latest_error_counts_for_erp(db, erp_number)

    except Exception as e:
        print(f'Error occurred: {e}')
        db.rollback()
        raise e
    finally:
        db.close()


if __name__ == '__main__':
    main()

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''=================================================
@Project -> File   ：pc_to_pi.py -> bom_cp_db
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2024/9/4 16:07
@Desc   ：
=================================================='''
import re
import pandas as pd

ERPS = [36615, 36732, 36808, 36824, 36826, 36919]


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    if match:
        return value.split('-')[1]  # 返回第二个部分
    return ''  # 如果不匹配，返回空字符串


# 读取Excel文件并处理 BOM 数据
def process_bom_file(erp):
    df = pd.read_excel(f'tests/{erp}投料信息.xlsx')
    df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
    df_bom = df.loc[
        (df['物料类型'] == '外购件') & (df['物料描述'].str.contains('伺服电机', na=False))
    ]

    # 修改 '物料描述' 列和提取 '部件号'
    df_bom.loc[:, '物料描述'] = (
        df_bom['物料描述']
        .str.replace('伺服电机_', '', regex=False)
        .str.replace(r'-XD.*', '', regex=True)
    )
    df_bom.loc[:, '部件编号'] = df_bom['父级'].apply(extract_second_part)
    df_bom = df_bom.sort_values(by='部件编号', ascending=True).reset_index(
        drop=True
    )

    return df_bom


# 汇总BOM信息
def summarize_bom(group):
    bom_summary = group.groupby('部件编号')['总数量'].sum().reset_index()
    return ', '.join(
        [f'{row["部件编号"]} ({row["总数量"]})' for _, row in bom_summary.iterrows()]
    )


# 处理 BOM 汇总
def summarize_bom_data(df_bom):
    return (
        df_bom.groupby('物料描述')
        .apply(
            lambda x: pd.Series(
                {'总数量': x['总数量'].sum(), '部件号统计': summarize_bom(x)}
            )
        )
        .reset_index()
    )


# 读取和处理机电沟通表
def process_mechanical_file(erp):
    df1 = pd.read_excel(f'tests/{erp}-机电沟通表.xlsx')
    df1 = df1[~df1['EM中文'].str.contains('蛇形', na=False)]
    df_db = df1[df1['类别'] == '伺服电机'][['型号', '部件编号']].rename(
        columns={'型号': '物料描述'}
    )
    df_db = (
        df_db.astype({'部件编号': 'int'})
        .sort_values(by='部件编号')
        .reset_index(drop=True)
        .astype({'部件编号': 'str'})
    )
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace('伺服电机_', '', regex=False)
        .str.replace(r'-XD.*', '', regex=True)
    )

    return df_db


# 汇总机电沟通表数据
def summarize_mechanical_data(df_db):
    df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')
    df_db_2 = (
        df_db.groupby('物料描述')['部件编号']
        .apply(
            lambda x: ', '.join(
                x.value_counts().index
                + ' ('
                + x.value_counts().astype(str)
                + ')'
            )
        )
        .reset_index(name='部件号统计')
    )
    return pd.merge(df_db_1, df_db_2, on='物料描述', how='left')


# 格式化部件号
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 比较两者的部件号和数量差异
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


def compare_part_numbers(row):
    bom_parts = parse_part_numbers(row['部件号统计_df_bom'])
    db_parts = parse_part_numbers(row['部件号统计_df_db'])

    all_parts = sorted(set(bom_parts.keys()).union(db_parts.keys()))
    diff_results = []

    for part in all_parts:
        bom_count = bom_parts.get(part, 0)
        db_count = db_parts.get(part, 0)
        diff = bom_count - db_count
        if diff != 0:
            diff_results.append(f'{part} (差异: {diff})')

    return ', '.join(diff_results) if diff_results else '数量相等'


# 添加对比结果状态列
def compare_status(row):
    if row['数量差异'] > 0:
        return 'df_bom数量更多'
    elif row['数量差异'] < 0:
        return 'df_db数量更多'
    else:
        return '数量相等'


# 高亮差异
def highlight_differences(s):
    if s['物料描述'] == '总数':
        return ['background-color: lightblue'] * len(s)
    elif abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# 主函数，执行所有流程
def main(ERP):
    # 读取和处理 BOM 文件
    df_bom = process_bom_file(ERP)
    df_bom_summary = summarize_bom_data(df_bom)

    # 读取和处理机电沟通表
    df_db = process_mechanical_file(ERP)
    df_db_summary = summarize_mechanical_data(df_db)

    # 合并数据并进行对比
    comparison_df = pd.merge(
        df_bom_summary[['物料描述', '总数量', '部件号统计']],
        df_db_summary[['物料描述', '总数量', '部件号统计']],
        on='物料描述',
        how='outer',
        suffixes=('_df_bom', '_df_db'),
    )

    # 填充 NaN 值
    comparison_df.fillna(0, inplace=True)

    # 计算数量差异和状态
    comparison_df['数量差异'] = (
        comparison_df['总数量_df_bom'] - comparison_df['总数量_df_db']
    )
    comparison_df['对比结果'] = comparison_df.apply(compare_status, axis=1)

    # 格式化部件号
    comparison_df['部件号统计_df_bom'] = comparison_df['部件号统计_df_bom'].apply(
        format_part_numbers
    )
    comparison_df['部件号统计_df_db'] = comparison_df['部件号统计_df_db'].apply(
        format_part_numbers
    )

    # 对比部件号
    comparison_df['部件号对比结果'] = comparison_df.apply(
        compare_part_numbers, axis=1
    )

    # 计算汇总行
    total_bom = comparison_df['总数量_df_bom'].sum()
    total_db = comparison_df['总数量_df_db'].sum()
    total_diff = comparison_df['数量差异'].sum()

    # 添加汇总行
    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '总数量_df_bom': [total_bom],
            '总数量_df_db': [total_db],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
        }
    )
    comparison_df = pd.concat([comparison_df, summary_row], ignore_index=True)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 导出到 Excel
    comparison_df_styled.to_excel(
        f'tests/{ERP}comparison_result__of_motor.xlsx', index=False
    )


def run(bom_file, db_file, mapping_df, output_path):
    # 读取和处理 BOM 文件
    df_bom = process_bom_file(bom_file)
    df_bom_summary = summarize_bom_data(df_bom)

    # 读取和处理机电沟通表
    df_db = process_mechanical_file(db_file, mapping_df)
    df_db_summary = summarize_mechanical_data(df_db)

    # 合并数据并进行对比
    comparison_df = pd.merge(
        df_bom_summary[['物料描述', '总数量', '部件号统计']],
        df_db_summary[['物料描述', '总数量', '部件号统计']],
        on='物料描述',
        how='outer',
        suffixes=('_df_bom', '_df_db'),
    )

    # 填充 NaN 值
    comparison_df.fillna(0, inplace=True)

    # 计算数量差异和状态
    comparison_df['数量差异'] = (
        comparison_df['总数量_df_bom'] - comparison_df['总数量_df_db']
    )
    comparison_df['对比结果'] = comparison_df.apply(compare_status, axis=1)

    # 格式化部件号
    comparison_df['部件号统计_df_bom'] = comparison_df['部件号统计_df_bom'].apply(
        format_part_numbers
    )
    comparison_df['部件号统计_df_db'] = comparison_df['部件号统计_df_db'].apply(
        format_part_numbers
    )

    # 对比部件号
    comparison_df['部件号对比结果'] = comparison_df.apply(
        compare_part_numbers, axis=1
    )

    # 计算汇总行
    total_bom = comparison_df['总数量_df_bom'].sum()
    total_db = comparison_df['总数量_df_db'].sum()
    total_diff = comparison_df['数量差异'].sum()

    # 添加汇总行
    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '总数量_df_bom': [total_bom],
            '总数量_df_db': [total_db],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
        }
    )
    comparison_df = pd.concat([comparison_df, summary_row], ignore_index=True)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 导出到 Excel
    comparison_df_styled.to_excel(f'{output_path}/', index=False)


if __name__ == '__main__':
    for ERP in ERPS:
        main(ERP)

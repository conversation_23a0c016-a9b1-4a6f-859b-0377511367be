# 电缆比对脚本更新说明

## 更新概述

基于新的数据结构，更新了电缆比对脚本 `bom_cables_count_by_line_with_len.py`，主要变化如下：

### 1. 数据源变化
- **移除**: `mech_motor_details.csv` 文件
- **新增**: `erp_cable_devices_202507241658.csv` 文件作为电缆数据源

### 2. 数据处理逻辑简化
- **BOM处理**: 直接提取"中文_"之后括号之前的型号，不再使用复杂的型号提取逻辑
- **电缆设备处理**: 根据EM编号和功能描述整理电缆数据，每个伺服对应动力线和编码器线
- **型号匹配**: 使用新表中的长度数据直接与BOM中的数据进行比对

### 3. 去除后处理
- **不再添加-H后缀**: BOM表和机电沟通表中的电缆型号都不再人工添加-H后缀
- **保持原始型号**: 直接使用数据源中的完整型号（包含长度信息）

## 主要函数

### `run_cable_comparison(bom_file, mech_info_file, cable_devices_file, output_path)`
主要的运行函数，参数：
- `bom_file`: BOM文件路径
- `mech_info_file`: 机电沟通表文件路径  
- `cable_devices_file`: 电缆设备CSV文件路径
- `output_path`: 输出路径

### `read_and_clean_cable_devices(file_path)`
读取并处理电缆设备CSV文件：
- 提取EM编号（从device_identifier中提取）
- 使用完整的型号（包含长度信息）
- 按动力线和编码器线分类

### `merge_db_and_cable_devices(df_db, cable_devices_df, output_path)`
合并机电沟通表与电缆设备数据：
- 根据EM名称进行匹配
- 直接使用电缆设备表中的型号，不做后处理

## 测试结果

使用测试数据 `/tests/37979/` 进行验证：
- **总记录数**: 185
- **正常匹配**: 63
- **异常记录**: 122

## 比对结果表结构

保持原有的表结构不变：
- 伺服电机物料描述
- 部件编号  
- EM中文
- 功能描述
- 动力线物料描述
- 编码器线物料描述
- 刹车线物料描述
- BOM表动力线物料描述
- BOM表编码器线物料描述
- BOM表刹车线物料描述
- 异常

## 使用示例

```python
# 运行电缆比对
result_path = run_cable_comparison(
    bom_file='tests/37979/2_37979-b0710.xlsx',
    mech_info_file='tests/37979/mech_info_37979.xlsx', 
    cable_devices_file='tests/37979/erp_cable_devices_202507241658.csv',
    output_path='tests/37979'
)
```

## 注意事项

1. **数据匹配**: 机电沟通表中的'EM名称'列需要与电缆设备数据中的EM编号匹配
2. **型号格式**: 电缆型号包含完整的长度信息，如`S6-L-M107-8.0-T`
3. **异常处理**: 如果动力线或编码器线任一为空，则标记为异常
4. **刹车线**: 新数据中暂无刹车线信息，相关字段为None

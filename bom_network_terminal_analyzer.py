import pandas as pd
import numpy as np


class NetworkTerminalPairsAnalyzer:
    """
    分析BOM中的网线-驱动器、端子台-X210A配对关系的分析器
    包含三组主要分析：
    1. 网线(8780208)与驱动器(8710201)的配对分析
    2. 10位端子台与X210A-1SL8的配对分析
    3. 10位端子台与X210A-2S的配对分析
    """

    def __init__(self, file_path):
        self.df = pd.read_excel(file_path)
        self.result_df = pd.DataFrame(
            columns=['物料编码1', '物料描述1', '数量1', '物料编码2', '物料描述2', '数量2', '对比结果']
        )

    def analyze_network_drive_pairs(self):
        """分析网线和驱动器配对"""
        # 网线分析 (8780208)
        network_cables = self.df[self.df['物料组'] == '8780208'][
            ['物料编码', '物料描述', '总数量']
        ]
        network_rows = network_cables.rename(
            columns={'物料编码': '物料编码1', '物料描述': '物料描述1', '总数量': '数量1'}
        )

        # 驱动器分析 (8710201)
        drives = self.df[self.df['物料组'] == '8710201'][['物料编码', '物料描述', '总数量']]
        # 模块查找
        modules = self.df[
            self.df['物料描述'].str.contains('模块', na=False)
            & ~self.df['物料描述'].str.contains('虚拟件', na=False)
        ][['物料编码', '物料描述', '总数量']]

        drives = pd.concat([drives, modules])

        drives_rows = drives.rename(
            columns={'物料编码': '物料编码2', '物料描述': '物料描述2', '总数量': '数量2'}
        )

        # 添加明细行
        self.result_df = pd.concat(
            [
                self.result_df,
                network_rows.assign(物料编码2='', 物料描述2='', 数量2=np.nan),
                drives_rows.assign(物料编码1='', 物料描述1='', 数量1=np.nan),
            ]
        )

        sum1, sum2, result = self.compare_quantity(network_cables, drives)

        # 添加合计行
        summary_row = pd.DataFrame(
            {
                '物料编码1': [''],
                '物料描述1': ['网线(合计)'],
                '数量1': [sum1],
                '物料编码2': [''],
                '物料描述2': ['驱动器(合计)'],
                '数量2': [sum2],
                '对比结果': [result],
            }
        )
        self.result_df = pd.concat([self.result_df, summary_row])

    def compare_quantity(self, df1, df2, coefficient=1):
        """比较两行"""
        sum1 = df1['总数量'].sum() if not df1.empty else 0
        sum2 = df2['总数量'].sum() if not df2.empty else 0
        if sum1 == sum2 * coefficient:
            return sum1, sum2, '数量匹配'
        elif sum1 > sum2 * coefficient:
            return sum1, sum2, '物料1更多'
        elif sum1 < sum2 * coefficient:
            return sum1, sum2, '物料2更多'

    def analyze_terminal_x210a_1sl8_pairs(self):
        """分析10位端子台和X210A-1SL8配对"""
        # 10位端子台分析
        terminal_10p = self.df[
            self.df['物料描述'].str.contains('端子台', na=False)
            & self.df['物料描述'].str.contains('10位', na=False)
        ][['物料编码', '物料描述', '总数量']]

        terminal_rows = terminal_10p.rename(
            columns={'物料编码': '物料编码1', '物料描述': '物料描述1', '总数量': '数量1'}
        )

        # X210A-1SL8分析
        x210a_1sl8 = self.df[
            self.df['物料描述'].str.contains('X210A-1SL8', na=False)
        ][['物料编码', '物料描述', '总数量']]

        x210a_rows = x210a_1sl8.rename(
            columns={'物料编码': '物料编码2', '物料描述': '物料描述2', '总数量': '数量2'}
        )

        # 添加明细行
        self.result_df = pd.concat(
            [
                self.result_df,
                terminal_rows.assign(物料编码2='', 物料描述2='', 数量2=np.nan),
                x210a_rows.assign(物料编码1='', 物料描述1='', 数量1=np.nan),
            ]
        )

        sum1, sum2, result = self.compare_quantity(terminal_10p, x210a_1sl8)

        # 添加合计行
        summary_row = pd.DataFrame(
            {
                '物料编码1': [''],
                '物料描述1': ['10位端子台(合计)'],
                '数量1': [sum1],
                '物料编码2': [''],
                '物料描述2': ['X210A-1SL8(合计)'],
                '数量2': [sum2],
                '对比结果': [result],
            }
        )
        self.result_df = pd.concat([self.result_df, summary_row])

    def analyze_terminal_x210a_2s_pairs(self):
        """分析10位端子台和X210A-2S配对"""
        # 10位端子台分析
        terminal_20p = self.df[
            self.df['物料描述'].str.contains('端子台', na=False)
            & self.df['物料描述'].str.contains('20位', na=False)
        ][['物料编码', '物料描述', '总数量']]

        terminal_rows = terminal_20p.rename(
            columns={'物料编码': '物料编码1', '物料描述': '物料描述1', '总数量': '数量1'}
        )

        # X210A-2S分析
        x210a_2s = self.df[self.df['物料描述'].str.contains('X210A-2S', na=False)][
            ['物料编码', '物料描述', '总数量']
        ]

        x210a_rows = x210a_2s.rename(
            columns={'物料编码': '物料编码2', '物料描述': '物料描述2', '总数量': '数量2'}
        )

        # 添加明细行
        self.result_df = pd.concat(
            [
                self.result_df,
                terminal_rows.assign(物料编码2='', 物料描述2='', 数量2=np.nan),
                x210a_rows.assign(物料编码1='', 物料描述1='', 数量1=np.nan),
            ]
        )

        sum1, sum2, result = self.compare_quantity(
            terminal_20p, x210a_2s, coefficient=2
        )

        # 添加合计行
        summary_row = pd.DataFrame(
            {
                '物料编码1': [''],
                '物料描述1': ['20位端子台(合计)'],
                '数量1': [sum1],
                '物料编码2': [''],
                '物料描述2': ['X210A-2S(合计)'],
                '数量2': [sum2],
                '对比结果': [result],
            }
        )
        self.result_df = pd.concat([self.result_df, summary_row])

    def analyze_all_pairs(self):
        """执行所有配对分析"""
        self.analyze_network_drive_pairs()
        self.analyze_terminal_x210a_1sl8_pairs()
        self.analyze_terminal_x210a_2s_pairs()
        # 重置索引并删除索引列
        self.result_df = self.result_df.reset_index(drop=True)
        return self.result_df

    def export_results(self, output_path):
        """导出分析结果到Excel文件"""
        # 先保存Excel文件
        self.result_df.to_excel(output_path, index=False)

        # 打开工作簿添加条件格式
        from openpyxl import load_workbook
        from openpyxl.styles import PatternFill

        wb = load_workbook(output_path)
        ws = wb.active

        # 定义填充颜色
        yellow_fill = PatternFill(
            start_color='FFFF00', end_color='FFFF00', fill_type='solid'
        )  # 黄色
        red_fill = PatternFill(
            start_color='FF0000', end_color='FF0000', fill_type='solid'
        )  # 红色

        # 获取"对比结果"列的列号
        for idx, col in enumerate(ws[1], 1):  # 遍历表头行
            if col.value == '对比结果':
                result_col = idx
                break

        # 为每一行添加条件格式
        for row in range(2, ws.max_row + 1):  # 从第2行开始（跳过表头）
            cell = ws.cell(row=row, column=result_col)
            if cell.value == '物料1更多':
                # 为整行设置黄色
                for col in range(1, ws.max_column + 1):
                    ws.cell(row=row, column=col).fill = yellow_fill
            elif cell.value == '物料2更多':
                # 为整行设置红色
                for col in range(1, ws.max_column + 1):
                    ws.cell(row=row, column=col).fill = red_fill

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
                adjusted_width = max_length + 2
                ws.column_dimensions[column_letter].width = adjusted_width

        # 保存更改
        wb.save(output_path)


def main():
    """主函数"""
    # 使用示例
    file_path = (
        '/home/<USER>/code/bom-analysis/tests/37100投料信息.xlsx'  # 替换为实际的文件路径
    )
    output_path = '网线与端子台投料校验.xlsx'  # 输出文件路径

    # 创建分析器实例
    analyzer = NetworkTerminalPairsAnalyzer(file_path)

    # 执行分析
    result = analyzer.analyze_all_pairs()

    # 导出结果
    analyzer.export_results(output_path)

    print('分析完成，结果已导出到:', output_path)
    return result


def run(bom_file, output_path):
    # 创建分析器实例
    analyzer = NetworkTerminalPairsAnalyzer(bom_file)

    # 执行分析
    analyzer.analyze_all_pairs()

    # 导出结果
    analyzer.export_results(f'{output_path}/网线与端子台投料校验.xlsx')

    return ({'type': 'bom', 'path': f'{output_path}/网线与端子台投料校验.xlsx'},)


if __name__ == '__main__':
    result_df = main()

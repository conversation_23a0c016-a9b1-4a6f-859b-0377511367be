#!/usr/bin/env python3
"""
定时任务模块
支持在固定时间点自动运行全量ERP比对任务
"""

import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import List

from core.logger import logger
from core.database import SessionLocal
from daily_bom_comparison import get_active_erp_projects


class ScheduledTaskManager:
    """定时任务管理器"""

    def __init__(self):
        self.scheduler_thread = None
        self.is_running = False
        self.scheduled_times = ['08:00', '12:00', '18:00', '22:00']  # 默认时间点
        self.task_api_url = (
            'http://localhost:8080/api/v1/bom-analysis/daily/tasks/comparison'
        )

    def set_scheduled_times(self, times: List[str]):
        """设置定时执行时间点"""
        self.scheduled_times = times
        logger.info(f'定时任务时间点已更新: {times}')

    def get_scheduled_times(self) -> List[str]:
        """获取当前定时执行时间点"""
        return self.scheduled_times

    def cleanup_old_tasks(self):
        """清理7天前的任务数据"""
        try:
            logger.info('开始清理7天前的任务数据')

            # 计算7天前的时间
            seven_days_ago = datetime.now() - timedelta(days=7)

            db_session = SessionLocal()
            try:
                from routers.task_manager import ComparisonTask

                # 查询7天前的任务
                old_tasks = (
                    db_session.query(ComparisonTask)
                    .filter(ComparisonTask.created_at < seven_days_ago)
                    .all()
                )

                if not old_tasks:
                    logger.info('没有找到需要清理的7天前任务数据')
                    return

                # 统计要删除的任务
                task_count = len(old_tasks)
                status_count = {}
                for task in old_tasks:
                    status = task.status
                    status_count[status] = status_count.get(status, 0) + 1

                logger.info(f'找到 {task_count} 个7天前的任务需要清理')
                logger.info(f'任务状态分布: {status_count}')

                # 删除旧任务
                deleted_count = (
                    db_session.query(ComparisonTask)
                    .filter(ComparisonTask.created_at < seven_days_ago)
                    .delete()
                )

                db_session.commit()

                logger.info(f'成功清理 {deleted_count} 个7天前的任务数据')

            finally:
                db_session.close()

        except Exception as e:
            logger.error(f'清理旧任务数据失败: {str(e)}')

    def create_bulk_comparison_task(self):
        """创建批量比对任务"""
        try:
            logger.info('开始执行定时批量ERP比对任务')

            # 首先清理7天前的数据
            self.cleanup_old_tasks()

            # 获取活跃的ERP项目
            db_session = SessionLocal()
            try:
                projects = get_active_erp_projects(db_session)
                if not projects:
                    logger.warning('未找到活跃的ERP项目，跳过定时任务')
                    return

                erp_numbers = [project['erp_number'] for project in projects]
                logger.info(f'找到 {len(erp_numbers)} 个活跃ERP项目: {erp_numbers}')

                # 调用任务管理API创建任务
                self._submit_tasks_via_api(erp_numbers)

            finally:
                db_session.close()

        except Exception as e:
            logger.error(f'定时批量比对任务执行失败: {str(e)}')

    def _submit_tasks_via_api(self, erp_numbers: List[str]):
        """通过API提交任务"""
        try:
            import requests

            payload = {
                'erp_numbers': erp_numbers,
                'created_by': '定时任务系统',
                'priority': 3,  # 低优先级
            }

            response = requests.post(
                self.task_api_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30,
            )

            if response.status_code == 200:
                result = response.json()
                created_count = len(result.get('created_tasks', []))
                skipped_count = len(result.get('skipped_erps', []))

                logger.info(
                    f'定时任务提交完成: 创建 {created_count} 个任务, 跳过 {skipped_count} 个重复ERP'
                )

                if result.get('skipped_erps'):
                    logger.info(f"跳过的ERP: {result['skipped_erps']}")

            else:
                logger.error(
                    f'任务提交失败: HTTP {response.status_code}, {response.text}'
                )

        except requests.exceptions.RequestException as e:
            logger.error(f'任务提交网络错误: {str(e)}')
        except Exception as e:
            logger.error(f'任务提交异常: {str(e)}')

    def _submit_tasks_directly(self, erp_numbers: List[str]):
        """直接提交任务（备用方案）"""
        try:
            from routers.task_manager import (
                task_priority_queue,
                PriorityTaskItem,
                ComparisonTask,
                TaskStatus,
                TaskPriority,
            )
            from core.database import SessionLocal

            db_session = SessionLocal()
            try:
                created_count = 0
                skipped_count = 0

                for erp_number in erp_numbers:
                    # 检查是否有重复任务
                    existing_task = (
                        db_session.query(ComparisonTask)
                        .filter(
                            ComparisonTask.erp_number == erp_number,
                            ComparisonTask.status.in_(
                                [TaskStatus.PENDING, TaskStatus.RUNNING]
                            ),
                        )
                        .first()
                    )

                    if existing_task:
                        skipped_count += 1
                        continue

                    # 创建新的低优先级任务
                    task = ComparisonTask(
                        erp_number=erp_number,
                        created_by='定时任务系统',
                        status=TaskStatus.PENDING,
                        priority=TaskPriority.LOW,
                    )

                    db_session.add(task)
                    db_session.commit()
                    db_session.refresh(task)

                    # 添加到优先级任务队列（低优先级）
                    priority_item = PriorityTaskItem(
                        priority=int(TaskPriority.LOW),
                        task_id=task.id,
                        erp_number=erp_number,
                        created_at=task.created_at,
                    )
                    task_priority_queue.put(priority_item)
                    created_count += 1

                logger.info(
                    f'定时任务直接提交完成: 创建 {created_count} 个低优先级任务, 跳过 {skipped_count} 个重复ERP'
                )

            finally:
                db_session.close()

        except Exception as e:
            logger.error(f'直接任务提交失败: {str(e)}')

    def setup_schedule(self):
        """设置定时任务"""
        # 清除现有的定时任务
        schedule.clear()

        # 为每个时间点设置定时任务
        for time_str in self.scheduled_times:
            schedule.every().day.at(time_str).do(
                self.create_bulk_comparison_task
            )
            logger.info(f'已设置定时任务: 每天 {time_str} 执行批量ERP比对')

    def start_scheduler(self):
        """启动定时任务调度器"""
        if self.is_running:
            logger.warning('定时任务调度器已在运行中')
            return

        self.setup_schedule()
        self.is_running = True

        def run_scheduler():
            logger.info('定时任务调度器已启动')
            while self.is_running:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # 每分钟检查一次
                except Exception as e:
                    logger.error(f'定时任务调度器异常: {str(e)}')
                    time.sleep(60)

        self.scheduler_thread = threading.Thread(
            target=run_scheduler, daemon=True
        )
        self.scheduler_thread.start()

    def stop_scheduler(self):
        """停止定时任务调度器"""
        if not self.is_running:
            return

        self.is_running = False
        schedule.clear()

        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)

        logger.info('定时任务调度器已停止')

    def get_next_run_times(self) -> List[dict]:
        """获取下次执行时间"""
        next_runs = []
        now = datetime.now()

        for time_str in self.scheduled_times:
            try:
                hour, minute = map(int, time_str.split(':'))
                next_run = now.replace(
                    hour=hour, minute=minute, second=0, microsecond=0
                )

                # 如果今天的时间已过，则计算明天的时间
                if next_run <= now:
                    next_run += timedelta(days=1)

                next_runs.append(
                    {
                        'time': time_str,
                        'next_run': next_run.strftime('%Y-%m-%d %H:%M:%S'),
                        'remaining_seconds': int(
                            (next_run - now).total_seconds()
                        ),
                    }
                )

            except ValueError:
                logger.error(f'无效的时间格式: {time_str}')

        return sorted(next_runs, key=lambda x: x['remaining_seconds'])

    def get_status(self) -> dict:
        """获取定时任务状态"""
        return {
            'is_running': self.is_running,
            'scheduled_times': self.scheduled_times,
            'next_runs': self.get_next_run_times(),
            'scheduler_alive': self.scheduler_thread.is_alive()
            if self.scheduler_thread
            else False,
        }

    def trigger_manual_run(self, cleanup_old_data=True):
        """手动触发一次批量任务"""
        logger.info('手动触发批量ERP比对任务')

        if cleanup_old_data:
            self.cleanup_old_tasks()

        # 执行批量任务（不再重复清理）
        try:
            logger.info('开始执行手动批量ERP比对任务')

            # 获取活跃的ERP项目
            db_session = SessionLocal()
            try:
                projects = get_active_erp_projects(db_session)
                if not projects:
                    logger.warning('未找到活跃的ERP项目，跳过手动任务')
                    return

                erp_numbers = [project['erp_number'] for project in projects]
                logger.info(f'找到 {len(erp_numbers)} 个活跃ERP项目: {erp_numbers}')

                # 调用任务管理API创建任务
                self._submit_tasks_via_api(erp_numbers)

            finally:
                db_session.close()

        except Exception as e:
            logger.error(f'手动批量比对任务执行失败: {str(e)}')

    def cleanup_old_data_only(self):
        """仅清理旧数据（不执行批量任务）"""
        logger.info('手动触发清理旧数据')
        self.cleanup_old_tasks()


# 全局定时任务管理器实例
scheduler_manager = ScheduledTaskManager()


def start_scheduled_tasks():
    """启动定时任务（在应用启动时调用）"""
    scheduler_manager.start_scheduler()


def stop_scheduled_tasks():
    """停止定时任务（在应用关闭时调用）"""
    scheduler_manager.stop_scheduler()


if __name__ == '__main__':
    # 测试运行
    logger.info('启动定时任务测试')
    scheduler_manager.start_scheduler()

    try:
        while True:
            time.sleep(10)
            status = scheduler_manager.get_status()
            logger.info(f'定时任务状态: {status}')
    except KeyboardInterrupt:
        logger.info('停止定时任务测试')
        scheduler_manager.stop_scheduler()

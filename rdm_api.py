import requests
import json
import pandas as pd
from datetime import datetime
import time
import uuid
import logging
from collections import defaultdict
from logging.handlers import RotatingFileHandler
from sqlalchemy import (
    create_engine,
    Column,
    Integer,
    String,
    DateTime,
    Boolean,
    ForeignKey,
    Enum,
    JSON,
    DECIMAL,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy import func, desc

# 配置日志，使用RotatingFileHandler限制日志文件大小
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler(
            'rdm_bom_tracker.log',
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
        ),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# 创建SQLAlchemy基类
Base = declarative_base()


# 定义数据模型
class BomMaster(Base):
    """BOM主表数据模型"""

    __tablename__ = 'BOM_MASTER'

    id = Column(Integer, primary_key=True, autoincrement=True)
    root_inv_code = Column(String(50), nullable=False, index=True)
    factory_code = Column(String(20), nullable=False)
    wbs_project_number = Column(String(50))
    query_time = Column(DateTime, nullable=False, default=datetime.now)
    is_latest = Column(Boolean, default=True, index=True)
    version = Column(Integer, default=1)
    total_items = Column(Integer, default=0)
    remark = Column(String(255))

    # 定义与BomDetail的关系
    details = relationship(
        'BomDetail', back_populates='master', cascade='all, delete-orphan'
    )

    def __repr__(self):
        return f"<BomMaster(id={self.id}, root_inv_code='{self.root_inv_code}', version={self.version})>"


class BomDetail(Base):
    """BOM明细数据模型"""

    __tablename__ = 'BOM_DETAIL'

    id = Column(String(50), primary_key=True)
    master_id = Column(
        Integer, ForeignKey('BOM_MASTER.id'), nullable=False, index=True
    )
    parent_inv_code = Column(String(50), index=True)
    inv_code = Column(String(50), nullable=False, index=True)
    parent_id = Column(String(50))
    child_id = Column(String(50))
    old_parent_id = Column(String(50))

    # 数量和版本信息
    total_amount = Column(DECIMAL(18, 4), default=1)
    per_amount = Column(DECIMAL(18, 4), default=1)
    drawing_version = Column(String(50))

    # 位置和分组信息
    location_code = Column(String(100))
    package_group = Column(String(100))

    # 来源信息
    source = Column(String(50))
    convert_batch = Column(String(50))
    brand_4_bom = Column(String(50))

    # 物料基本信息
    memo_cn = Column(String(255))
    material_state = Column(String(50))
    material_type = Column(String(50))
    mac_ele = Column(String(50))
    m_attribute = Column(String(100))
    dept = Column(String(50))
    drawing_no = Column(String(100))

    # 物料特殊属性
    is_production_critical = Column(String(10))
    is_cbb = Column(String(10))
    old_memo = Column(String(255))
    plan_default = Column(String(50))
    manu_part_no = Column(String(100))

    # 物理属性
    net_weight = Column(String(20))
    w_unit = Column(String(20))
    surface_area = Column(String(20))
    surface_deal = Column(String(100))
    max_size = Column(String(100))

    # 责任和品牌信息
    applyer = Column(String(50))
    brand = Column(String(50))
    draw_number = Column(String(50))

    # 物料分类信息
    mat_group = Column(String(50))
    mat_template = Column(String(100))
    material = Column(String(100))
    product_line = Column(String(100))

    # 单位和封装信息
    unit = Column(String(20))
    package = Column(String(50))

    # 系统信息
    master_version = Column(String(50))
    in_out_factory = Column(String(20))
    wbs_project_num = Column(String(50))
    factory_no = Column(String(50))

    # 时间信息
    release_date = Column(DateTime)
    change_date = Column(DateTime)

    # 状态信息
    check_status = Column(String(50))
    jx_match = Column(String(50))
    upload_time = Column(DateTime)

    # 其他信息
    remark = Column(String(255))
    depth = Column(Integer, default=0)
    component_part = Column(String(100))

    # 定义与BomMaster的关系
    master = relationship('BomMaster', back_populates='details')

    def __repr__(self):
        return f"<BomDetail(id='{self.id}', inv_code='{self.inv_code}')>"


class BomChangeLog(Base):
    """BOM变更日志数据模型"""

    __tablename__ = 'BOM_CHANGE_LOG'

    log_id = Column(Integer, primary_key=True, autoincrement=True)
    root_inv_code = Column(String(50), nullable=False, index=True)
    prev_version = Column(Integer)
    curr_version = Column(Integer, nullable=False)
    parent_inv_code = Column(String(50))
    component_part = Column(String(100))
    inv_code = Column(String(50), nullable=False, index=True)
    memo_cn = Column(String(255))
    parent_inv_code = Column(String(50))
    change_type = Column(
        Enum('ADD', 'MODIFY', 'DELETE', name='change_type_enum'),
        nullable=False,
    )
    change_time = Column(
        DateTime, nullable=False, default=datetime.now, index=True
    )
    change_fields = Column(JSON)

    def __repr__(self):
        return f"<BomChangeLog(log_id={self.log_id}, change_type='{self.change_type}')>"


class RDMBomTracker:
    def __init__(self):
        """
        初始化RDM BOM变更跟踪器

        Args:
            db_config (dict): 数据库配置
        """
        # 设置环境信息
        self.base_url = 'https://esb-new.leadchina.cn/api/ipaas/RDM'
        self.headers = {
            'companycode': '001',
            'userid': 'server_request_api',
            'password': 'Qwerty123456',
            'Content-Type': 'application/json',
        }

        # 记录已查询过的物料编码，避免重复查询
        self.queried_materials = set()

        # 保存所有查询结果
        self.all_results = []

        # 记录主表ID
        self.master_id = None

        # 当前根物料信息
        self.root_inv_code = None
        self.current_version = None

        # 数据库连接配置
        self.db_config = {
            'host': '*************',
            'user': 'root',
            'password': 'leadchina',
            'database': 'IO',
            'charset': 'utf8mb4',
        }

        # 创建数据库连接
        self.engine = create_engine(
            f"mysql+pymysql://{self.db_config['user']}:{self.db_config['password']}@"
            f"{self.db_config['host']}/{self.db_config['database']}?charset={self.db_config['charset']}"
        )

        # 创建会话工厂
        self.Session = sessionmaker(bind=self.engine)

        # 创建表（如果不存在）
        Base.metadata.create_all(self.engine)

    def query_material(
        self, inv_code, factory_code='1110', wbs_project_number=''
    ):
        """
        查询单个物料信息

        Args:
            inv_code (str): 物料编码
            factory_code (str): 工厂编码，默认为"1110"
            wbs_project_number (str): WBS项目号，默认为空

        Returns:
            dict: 查询结果，失败时返回None
        """
        # 如果已经查询过，直接返回
        if inv_code in self.queried_materials:
            return None

        # 标记为已查询
        self.queried_materials.add(inv_code)

        # 构建API URL
        url = f'{self.base_url}/api_chn_0000_rdm_0014'

        # 构建请求参数
        payload = {
            'searchInvcode': inv_code,
            'FactoryCode': factory_code,
            'WBSProjectNumber': wbs_project_number,
        }

        try:
            # 发送请求
            response = requests.post(
                url, headers=self.headers, data=json.dumps(payload)
            )
            response.raise_for_status()

            # 解析响应结果
            result = response.json()

            # 检查业务响应码
            if result.get('Code') == 0:
                return result.get('Result', [])
            else:
                logger.error(f"查询物料 {inv_code} 失败: {result.get('Message')}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f'查询物料 {inv_code} 请求异常: {str(e)}')
            return None

    def calculate_total_quantity(self, id, material_dict):
        """
        计算物料的总数量，通过回溯父物料关系

        Args:
            inv_code (str): 物料编码
            all_results (list): 所有物料查询结果

        Returns:
            float: 总数量
        """

        # 跟踪回溯路径以防止循环引用
        visited = set()

        def trace_back(current_code, visited_set):
            # 如果已经访问过，防止循环引用
            if current_code in visited_set:
                logger.warning(f'检测到循环引用: {current_code}')
                return 1.0, []

            # 添加到已访问集合
            visited_set.add(current_code)

            # 获取当前物料
            current_item = material_dict.get(current_code)
            if not current_item:
                return 1.0, []

            # 获取父物料编码和数量
            parent_id = current_item.get('ParentID')
            quantity = float(current_item.get('dPerAmount', 1.0))

            # 如果没有父物料或父物料为空，直接返回当前数量
            if not parent_id or parent_id.strip() == '':
                return quantity, [current_code]

            # 递归计算父物料数量
            parent_quantity, path = trace_back(parent_id, visited_set)

            # 返回累积数量和路径
            return quantity * parent_quantity, [current_code] + path

        # 计算总数量和路径
        total_quantity, path = trace_back(id, visited)

        return total_quantity

    def process_all_results(self):
        """
        处理所有查询结果，计算总数量并更新数据
        """
        # 为每个物料计算总数量
        for item in self.all_results:
            parent_id = item.get('ParentID')
            id = item.get('ID')
            if parent_id:
                total_quantity = self.calculate_total_quantity(
                    id, self.material_dict
                )
                item['total_amount'] = total_quantity
            else:
                item['total_amount'] = item['dPerAmount']

    def query_bom(
        self,
        inv_code,
        factory_code='1020',
        wbs_project_number='',
        depth=0,
        parent_component='',
    ):
        """
        查询物料BOM结构

        Args:
            inv_code (str): 物料编码
            factory_code (str): 工厂编码，默认为"1020"
            wbs_project_number (str): WBS项目号，默认为空
            depth (int): 查询深度，0表示首次查询，1表示第二次查询
            parent_component (str): 所属部件

        Returns:
            list: 包含物料BOM结构的列表
        """
        # 查询前，先记录根物料信息到主表
        if depth == 0:
            self.root_inv_code = inv_code
            self.current_version = self.get_next_version(inv_code)
            self.master_id = self.save_master_info(
                inv_code,
                factory_code,
                wbs_project_number,
                self.current_version,
            )

        # 查询当前物料
        result = self.query_material(
            inv_code, factory_code, wbs_project_number
        )

        if not result:
            return []

        # 为查询结果添加层级信息和唯一ID
        for item in result:
            item['depth'] = depth
            if item['cInvCode'] != inv_code:
                item['component_part'] = parent_component

            # 生成唯一ID
            item['id'] = str(uuid.uuid4())

            # 添加主表ID
            item['master_id'] = self.master_id

        if depth > 0:
            result = [
                item for item in result if item.get('cInvCode') != inv_code
            ]
        # 记录查询结果
        self.all_results.extend(result)

        # 如果是第一层查询，只处理"部件"类型的物料进行下一级查询
        if depth == 0:
            # 收集第一层中所有类型为"部件"的物料编码
            component_items = [
                item for item in result if item.get('cMaterialType') == '部件'
            ]

            logger.info(f'第一层查询完成，找到 {len(component_items)} 个部件类型的物料')

            # 查询所有"部件"类型的子物料
            for component in component_items:
                child_inv_code = component.get('cInvCode')
                component_drawing_no = component.get('cDrawingNo', '')
                if child_inv_code:
                    logger.info(f'开始查询部件物料: {child_inv_code}')

                    # 查询子物料
                    self.query_bom(
                        child_inv_code,
                        factory_code,
                        wbs_project_number,
                        depth=1,
                        parent_component=component_drawing_no,
                    )

            # 创建查找字典，方便快速查找物料
            self.material_dict = {}
            for item in self.all_results:
                self.material_dict[item.get('ID')] = item

            # 查询完成后，计算总数量
            self.process_all_results()

            # 查询完成后，将所有结果保存到数据库并分析变更
            self.save_to_database()
            self.detect_and_record_changes()

            # 更新主表中的总物料数
            self.update_total_items()

            logger.info(
                f'查询完成，物料 {inv_code} 版本 {self.current_version} 共有 {len(self.all_results)} 个物料项'
            )

            return self.all_results

        # 如果已经是第二层查询，不再继续递归
        if depth >= 1:
            return self.all_results

        return self.all_results

    def get_next_version(self, root_inv_code):
        """
        获取下一个版本号

        Args:
            root_inv_code (str): 根物料编码

        Returns:
            int: 下一个版本号
        """
        try:
            session = self.Session()

            # 查询最新版本
            max_version = (
                session.query(func.max(BomMaster.version))
                .filter(BomMaster.root_inv_code == root_inv_code)
                .scalar()
            )

            if max_version:
                return max_version + 1
            else:
                return 1

        except Exception as e:
            logger.error(f'获取下一个版本号失败: {str(e)}')
            return 1
        finally:
            session.close()

    def save_master_info(
        self, root_inv_code, factory_code, wbs_project_number, version
    ):
        """
        保存查询批次主信息到BOM_MASTER表

        Args:
            root_inv_code (str): 根物料编码
            factory_code (str): 工厂编码
            wbs_project_number (str): WBS项目号
            version (int): 版本号

        Returns:
            int: 新插入记录的ID
        """
        try:
            # 创建会话
            session = self.Session()

            # 先将该根物料的所有历史记录标记为非最新
            session.query(BomMaster).filter(
                BomMaster.root_inv_code == root_inv_code
            ).filter(BomMaster.is_latest == True).update(
                {BomMaster.is_latest: False}
            )

            # 创建新记录
            new_master = BomMaster(
                root_inv_code=root_inv_code,
                factory_code=factory_code,
                wbs_project_number=wbs_project_number,
                query_time=datetime.now(),
                is_latest=True,
                version=version,
            )

            # 添加到会话并提交
            session.add(new_master)
            session.commit()

            # 获取新插入记录的ID
            master_id = new_master.id

            logger.info(f'保存BOM主表信息成功: ID={master_id}, 版本={version}')

            return master_id

        except Exception as e:
            session.rollback()
            logger.error(f'保存BOM主表信息失败: {str(e)}')
            return None
        finally:
            session.close()

    def save_to_database(self):
        """
        将查询结果保存到数据库
        """
        if not self.all_results:
            logger.warning('没有查询结果，无法保存到数据库')
            return

        try:
            # 创建会话
            session = self.Session()

            # 批量插入BOM明细数据
            for item in self.all_results:
                # 创建BomDetail实例，从item中提取所有可用字段
                bom_detail = BomDetail(
                    id=item.get('id'),
                    master_id=item.get('master_id'),
                    # 核心层级关系字段
                    parent_inv_code=item.get('ParentInvCode', ''),
                    inv_code=item.get('cInvCode', ''),
                    parent_id=item.get('ParentID', ''),
                    child_id=item.get('ID', ''),
                    old_parent_id=item.get('OLDParentID', ''),
                    # 数量和版本信息
                    total_amount=item.get('total_amount', 1),
                    per_amount=item.get('dPerAmount', 1),
                    drawing_version=item.get('cDrawingVersion', ''),
                    # 位置和分组信息
                    location_code=item.get('cLocationCode', ''),
                    package_group=item.get('cPackageGroup', ''),
                    # 来源信息
                    source=item.get('cSource', ''),
                    convert_batch=item.get('cConvertBatch', ''),
                    brand_4_bom=item.get('Brand4Bom', ''),
                    # 物料基本信息
                    memo_cn=item.get('cMemoCN', ''),
                    material_state=item.get('cMaterialState', ''),
                    material_type=item.get('cMaterialType', ''),
                    mac_ele=item.get('cMacEle', ''),
                    m_attribute=item.get('cMAttribute', ''),
                    dept=item.get('cDept', ''),
                    drawing_no=item.get('cDrawingNo', ''),
                    # 物料特殊属性
                    is_production_critical=item.get(
                        'cIsMatProductionCritical', ''
                    ),
                    is_cbb=item.get('cIsCBB', ''),
                    old_memo=item.get('cOldMemo', ''),
                    plan_default=item.get('cPlanDefault', ''),
                    manu_part_no=item.get('cManuPartNo', ''),
                    # 物理属性
                    net_weight=item.get('dNetWeight'),
                    w_unit=item.get('cWUnit', ''),
                    surface_area=item.get('dSurfaceArea'),
                    surface_deal=item.get('cSurfaceDeal', ''),
                    max_size=item.get('cMaxSize', ''),
                    # 责任和品牌信息
                    applyer=item.get('cApplyer', ''),
                    brand=item.get('cBrand', ''),
                    draw_number=item.get('cDrawNumber', ''),
                    # 物料分类信息
                    mat_group=item.get('cMatGroup', ''),
                    mat_template=item.get('cMatTemplate', ''),
                    material=item.get('cMaterial', ''),
                    product_line=item.get('cProductLine', ''),
                    # 单位和封装信息
                    unit=item.get('cUnit', ''),
                    package=item.get('cPackage', ''),
                    # 系统信息
                    master_version=item.get('cMasterVersion', ''),
                    in_out_factory=item.get('cInOutFactory', ''),
                    wbs_project_num=item.get('WBSProjectNum', ''),
                    factory_no=item.get('FactoryNo', ''),
                    # 时间信息
                    release_date=self._parse_datetime(
                        item.get('cReleaseDate')
                    ),
                    change_date=self._parse_datetime(item.get('rChangeDate')),
                    # 状态信息
                    check_status=item.get('CheckStatus', ''),
                    jx_match=item.get('jxMatch', ''),
                    upload_time=self._parse_datetime(item.get('uploadTime')),
                    # 其他信息
                    remark=item.get('remark', ''),
                    depth=item.get('depth', 0),
                    component_part=item.get('component_part', ''),
                )

                # 添加到会话
                session.add(bom_detail)

            # 提交事务
            session.commit()
            logger.info(f'成功保存BOM明细到数据库: {len(self.all_results)} 条记录')

        except Exception as e:
            session.rollback()
            logger.error(f'保存BOM明细到数据库失败: {str(e)}')
        finally:
            session.close()

    def _parse_datetime(self, datetime_str):
        """
        解析日期时间字符串为datetime对象

        Args:
            datetime_str (str): 日期时间字符串

        Returns:
            datetime: 解析后的datetime对象，失败时返回None
        """
        if not datetime_str:
            return None

        try:
            # 常见的日期时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d',
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(datetime_str, fmt)
                except ValueError:
                    continue

            # 如果所有格式都失败，记录并返回None
            logger.warning(f'无法解析日期时间字符串: {datetime_str}')
            return None

        except Exception as e:
            logger.error(f'解析日期时间出错: {str(e)}')
            return None

    def update_total_items(self):
        """
        更新主表中的总物料数
        """
        try:
            session = self.Session()

            # 计算总物料数
            total_count = (
                session.query(func.count(BomDetail.id))
                .filter(BomDetail.master_id == self.master_id)
                .scalar()
            )

            # 更新主表记录
            session.query(BomMaster).filter(
                BomMaster.id == self.master_id
            ).update({BomMaster.total_items: total_count})

            session.commit()

        except Exception as e:
            session.rollback()
            logger.error(f'更新总物料数失败: {str(e)}')
        finally:
            session.close()

    def detect_and_record_changes(self):
        """
        检测变更并记录
        """
        # 如果是首次查询，不需要对比
        if self.current_version <= 1:
            logger.info('这是首次查询该物料，没有变更记录')
            return

        try:
            session = self.Session()

            # 获取前一个版本的ID
            prev_master = (
                session.query(BomMaster)
                .filter(BomMaster.root_inv_code == self.root_inv_code)
                .filter(BomMaster.version == self.current_version - 1)
                .first()
            )

            if not prev_master:
                logger.warning('找不到前一个版本信息，无法进行变更对比')
                return

            prev_master_id = prev_master.id
            prev_version = prev_master.version

            # 获取当前版本和前一个版本的物料项数量
            current_item_count = (
                session.query(func.count(BomDetail.id))
                .filter(BomDetail.master_id == self.master_id)
                .scalar()
            )

            prev_item_count = (
                session.query(func.count(BomDetail.id))
                .filter(BomDetail.master_id == prev_master_id)
                .scalar()
            )

            # 如果当前版本或前一个版本没有物料项，跳过比对
            if current_item_count == 0 or prev_item_count == 0:
                logger.warning('当前版本或前一个版本没有任何物料项，可能是API查询失败，跳过变更对比')
                return

            # 获取当前版本的所有物料
            current_items = (
                session.query(BomDetail)
                .filter(BomDetail.master_id == self.master_id)
                .all()
            )

            # 获取前一个版本的所有物料
            prev_items = (
                session.query(BomDetail)
                .filter(BomDetail.master_id == prev_master_id)
                .all()
            )

            # Initialize a defaultdict of lists
            current_dict = defaultdict(list)

            # Populate the defaultdict
            for item in current_items:
                key = f'{item.component_part}_{item.parent_inv_code}_{item.inv_code}'
                current_dict[key].append(
                    {
                        'inv_code': item.inv_code,
                        'memo_cn': item.memo_cn,
                        'parent_inv_code': item.parent_inv_code,
                        'total_amount': float(item.total_amount)
                        if item.total_amount
                        else 0,
                        'per_amount': float(item.per_amount)
                        if item.per_amount
                        else 0,
                        'depth': item.depth,
                        'component_part': item.component_part,
                        'material_type': item.material_type,
                        'drawing_no': item.drawing_no,
                    }
                )

            # Convert defaultdict to regular dict if needed
            current_dict = dict(current_dict)

            # Initialize a defaultdict of lists
            prev_dict = defaultdict(list)

            # Populate the defaultdict
            for item in prev_items:
                key = f'{item.component_part}_{item.parent_inv_code}_{item.inv_code}'
                prev_dict[key].append(
                    {
                        'inv_code': item.inv_code,
                        'memo_cn': item.memo_cn,
                        'parent_inv_code': item.parent_inv_code,
                        'total_amount': float(item.total_amount)
                        if item.total_amount
                        else 0,
                        'per_amount': float(item.per_amount)
                        if item.per_amount
                        else 0,
                        'depth': item.depth,
                        'component_part': item.component_part,
                        'material_type': item.material_type,
                        'drawing_no': item.drawing_no,
                    }
                )

            # Convert defaultdict to regular dict if needed
            prev_dict = dict(prev_dict)

            # 找出新增物料
            added_codes = set(current_dict.keys()) - set(prev_dict.keys())
            for inv_code in added_codes:
                items = current_dict[inv_code]
                for item in items:
                    self.record_change(
                        prev_version=prev_version,
                        curr_version=self.current_version,
                        inv_code=item['inv_code'],
                        memo_cn=item['memo_cn'],
                        parent_inv_code=item['parent_inv_code'],
                        component_part=item['component_part'],
                        change_type='ADD',
                        change_fields={
                            'total_amount': item['total_amount'],
                            'per_amount': item['per_amount'],
                            'material_type': item['material_type'],
                            'memo_cn': item['memo_cn'],
                        },
                    )

            # 找出删除物料
            deleted_codes = set(prev_dict.keys()) - set(current_dict.keys())
            for inv_code in deleted_codes:
                items = prev_dict[inv_code]
                for item in items:
                    self.record_change(
                        prev_version=prev_version,
                        curr_version=self.current_version,
                        inv_code=item['inv_code'],
                        memo_cn=item['memo_cn'],
                        parent_inv_code=item['parent_inv_code'],
                        component_part=item['component_part'],
                        change_type='DELETE',
                        change_fields={
                            'total_amount': item['total_amount'],
                            'per_amount': item['per_amount'],
                            'memo_cn': item['memo_cn'],
                            'material_type': item['material_type'],
                        },
                    )

            # 找出修改物料
            common_codes = set(current_dict.keys()).intersection(
                set(prev_dict.keys())
            )
            modified_count = 0

            for inv_code in common_codes:
                current_items = current_dict[inv_code]
                prev_items = prev_dict[inv_code]

                # 把 current_items 中已经处理过的项标记出来
                processed_curr_indices = set()

                # 对每个 prev_item 尝试在 current_items 中找匹配项
                for prev_idx, prev_item in enumerate(prev_items):
                    prev_memo_cn = prev_item['memo_cn']
                    found_exact_match = False
                    best_match_idx = -1
                    min_differences = float('inf')

                    # 第一轮：在 current_items 中查找完全匹配项（所有字段都相同）
                    for curr_idx, curr_item in enumerate(current_items):
                        if curr_idx in processed_curr_indices:
                            continue  # 跳过已处理的项

                        if curr_item['memo_cn'] == prev_memo_cn:
                            # 检查所有字段是否完全匹配
                            if (
                                curr_item['total_amount']
                                == prev_item['total_amount']
                                and curr_item['per_amount']
                                == prev_item['per_amount']
                                and curr_item['material_type']
                                == prev_item['material_type']
                                and curr_item['drawing_no']
                                == prev_item['drawing_no']
                            ):

                                # 找到完全匹配
                                found_exact_match = True
                                processed_curr_indices.add(curr_idx)
                                break  # 找到完全匹配就退出
                            else:
                                # 计算差异数量
                                differences = 0
                                if (
                                    curr_item['total_amount']
                                    != prev_item['total_amount']
                                ):
                                    differences += 1
                                if (
                                    curr_item['per_amount']
                                    != prev_item['per_amount']
                                ):
                                    differences += 1
                                if (
                                    curr_item['material_type']
                                    != prev_item['material_type']
                                ):
                                    differences += 1
                                if (
                                    curr_item['drawing_no']
                                    != prev_item['drawing_no']
                                ):
                                    differences += 1

                                # 记录最佳匹配（差异最小的）
                                if differences < min_differences:
                                    min_differences = differences
                                    best_match_idx = curr_idx

                    # 如果没有找到完全匹配，但找到了最佳匹配
                    if not found_exact_match and best_match_idx != -1:
                        curr_item = current_items[best_match_idx]
                        processed_curr_indices.add(best_match_idx)

                        change_fields = {}
                        if (
                            curr_item['total_amount']
                            != prev_item['total_amount']
                        ):
                            change_fields['total_amount'] = [
                                prev_item['total_amount'],
                                curr_item['total_amount'],
                            ]
                        if curr_item['per_amount'] != prev_item['per_amount']:
                            change_fields['per_amount'] = [
                                prev_item['per_amount'],
                                curr_item['per_amount'],
                            ]
                        if (
                            curr_item['material_type']
                            != prev_item['material_type']
                        ):
                            change_fields['material_type'] = [
                                prev_item['material_type'],
                                curr_item['material_type'],
                            ]
                        if curr_item['drawing_no'] != prev_item['drawing_no']:
                            change_fields['drawing_no'] = [
                                prev_item['drawing_no'],
                                curr_item['drawing_no'],
                            ]

                        # 记录修改
                        self.record_change(
                            prev_version=prev_version,
                            curr_version=self.current_version,
                            inv_code=curr_item['inv_code'],
                            memo_cn=curr_item['memo_cn'],
                            parent_inv_code=curr_item['parent_inv_code'],
                            component_part=curr_item['component_part'],
                            change_type='MODIFY',
                            change_fields=change_fields,
                        )
                        modified_count += 1

                    # 如果完全没有找到匹配项（既没有完全匹配，也没有部分匹配）
                    elif not found_exact_match and best_match_idx == -1:
                        self.record_change(
                            prev_version=prev_version,
                            curr_version=self.current_version,
                            inv_code=prev_item['inv_code'],
                            memo_cn=prev_item['memo_cn'],
                            parent_inv_code=prev_item['parent_inv_code'],
                            component_part=prev_item['component_part'],
                            change_type='DELETE',
                            change_fields={
                                'total_amount': prev_item['total_amount'],
                                'per_amount': prev_item['per_amount'],
                                'material_type': prev_item['material_type'],
                                'memo_cn': prev_item['memo_cn'],
                            },
                        )

                # 处理 current_items 中未处理的项（新增的项）
                for curr_idx, curr_item in enumerate(current_items):
                    if curr_idx not in processed_curr_indices:
                        self.record_change(
                            prev_version=prev_version,
                            curr_version=self.current_version,
                            inv_code=curr_item['inv_code'],
                            memo_cn=curr_item['memo_cn'],
                            parent_inv_code=curr_item['parent_inv_code'],
                            component_part=curr_item['component_part'],
                            change_type='ADD',
                            change_fields={
                                'total_amount': curr_item['total_amount'],
                                'per_amount': curr_item['per_amount'],
                                'material_type': curr_item['material_type'],
                                'memo_cn': curr_item['memo_cn'],
                            },
                        )
            logger.info(
                f'变更检测完成: 新增 {len(added_codes)}, 删除 {len(deleted_codes)}, 修改 {modified_count}'
            )

        except Exception as e:
            logger.error(f'检测变更失败: {str(e)}')
        finally:
            session.close()

        # 删除两天前的BOM详细数据
        self.clean_old_bom_details()

    def clean_old_bom_details(self):
        """
        删除3天前的BOM详细数据和7天前的变更记录
        """
        try:
            session = self.Session()

            # 计算3天前的日期（用于清理BOM详细数据）
            three_days_ago = datetime.now() - pd.Timedelta(days=3)

            # 计算7天前的日期（用于清理变更记录）
            seven_days_ago = datetime.now() - pd.Timedelta(days=7)

            # 找出3天前的所有主表记录
            old_masters = (
                session.query(BomMaster)
                .filter(BomMaster.query_time < three_days_ago)
                .all()
            )

            # 清理BOM详细数据
            bom_detail_deleted = 0
            if old_masters:
                # 获取所有旧主表ID
                old_master_ids = [master.id for master in old_masters]

                # 删除相关的明细记录
                bom_detail_deleted = (
                    session.query(BomDetail)
                    .filter(BomDetail.master_id.in_(old_master_ids))
                    .delete(synchronize_session=False)
                )

                logger.info(f'已清理3天前的BOM详细数据: {bom_detail_deleted} 条记录')
            else:
                logger.info('没有找到3天前的BOM数据')

            # 清理7天前的变更记录
            change_log_deleted = (
                session.query(BomChangeLog)
                .filter(BomChangeLog.change_time < seven_days_ago)
                .delete(synchronize_session=False)
            )

            if change_log_deleted > 0:
                logger.info(f'已清理7天前的BOM变更记录: {change_log_deleted} 条记录')
            else:
                logger.info('没有找到7天前的BOM变更记录')

            # 不删除主表记录，只删除明细记录和变更记录
            # 这样可以保留历史版本信息，但不保留详细数据

            session.commit()
            logger.info(
                f'数据清理完成 - BOM详细数据: {bom_detail_deleted} 条, 变更记录: {change_log_deleted} 条'
            )

        except Exception as e:
            session.rollback()
            logger.error(f'清理旧数据失败: {str(e)}')
        finally:
            session.close()

    def record_change(
        self,
        prev_version,
        curr_version,
        inv_code,
        memo_cn,
        parent_inv_code,
        component_part,
        change_type,
        change_fields,
    ):
        """
        记录变更到BOM_CHANGE_LOG表

        Args:
            prev_version (int): 前一版本号
            curr_version (int): 当前版本号
            inv_code (str): 物料编码
            memo_cn (str): 物料描述
            parent_inv_code (str): 父物料编码
            change_type (str): 变更类型 (ADD/MODIFY/DELETE)
            change_fields (dict): 变更字段
        """
        try:
            session = self.Session()

            # 创建变更日志记录
            change_log = BomChangeLog(
                root_inv_code=self.root_inv_code,
                prev_version=prev_version,
                curr_version=curr_version,
                inv_code=inv_code,
                memo_cn=memo_cn,
                component_part=component_part,
                parent_inv_code=parent_inv_code,
                change_type=change_type,
                change_time=datetime.now(),
                change_fields=change_fields,
            )

            # 添加到会话并提交
            session.add(change_log)
            session.commit()

        except Exception as e:
            session.rollback()
            logger.error(f'记录变更失败: {str(e)}')
        finally:
            session.close()

    def get_bom_dataframe(self, root_inv_code=None, version=None):
        """
        将BOM数据从数据库导出到Excel文件

        Args:
            root_inv_code (str): 根物料编码，如果为None则使用当前查询的根物料
            version (int): 要导出的版本号，如果为None则导出最新版本
            filename (str): 文件名，如果为None则自动生成

        Returns:
            str: 保存的文件路径
        """
        try:
            session = self.Session()

            # 确定要导出的根物料
            if root_inv_code is None:
                if self.root_inv_code:
                    root_inv_code = self.root_inv_code
                else:
                    logger.error('未指定导出的根物料编码')
                    return None

            # 查询主表记录
            if version:
                # 查询指定版本
                master_record = (
                    session.query(BomMaster)
                    .filter(BomMaster.root_inv_code == root_inv_code)
                    .filter(BomMaster.version == version)
                    .first()
                )

                if not master_record:
                    logger.error(f'找不到物料 {root_inv_code} 的版本 {version} 的主记录')
                    return None
            else:
                # 查询最新版本
                master_record = (
                    session.query(BomMaster)
                    .filter(BomMaster.root_inv_code == root_inv_code)
                    .filter(BomMaster.is_latest == True)
                    .first()
                )

                if not master_record:
                    logger.error(f'找不到物料 {root_inv_code} 的最新版本主记录')
                    return None

            # 获取主表ID和版本
            master_id = master_record.id
            version = master_record.version

            # 查询明细表记录
            detail_records = (
                session.query(BomDetail)
                .filter(BomDetail.master_id == master_id)
                .all()
            )

            if not detail_records:
                logger.warning(f'物料 {root_inv_code} 版本 {version} 没有任何明细记录')
                return None

            # 定义新的表头映射
            headers_mapping = {
                'depth': '层级',
                'component_part': '所属部件',
                # "parent_id": "父级",
                'parent_inv_code': '父级编码',
                'inv_code': '物料编码',
                'memo_cn': '物料描述',
                'per_amount': '单层数量',
                'total_amount': '总数量',
                'unit': '单位',
                'drawing_version': '版本',
                'material_type': '物料类型',
                'mat_template': '物料模板',
                'drawing_no': '图号',
                'mat_group': '物料组',
                'material': '材料',
                'brand': '品牌',
                'product_line': '产品线',
                'check_status': 'BOM状态',
                'material_state': '物料状态',
                'source': '来源',
                'mac_ele': '机械电气',
                'upload_time': '上传时间',
                'release_date': '发布时间',
                'change_date': '变更时间',
                'm_attribute': '工艺属性',
                'package_group': '组批包',
                'master_version': '主数据版本',
                'in_out_factory': 'ERP',
            }

            # 转换记录为字典列表
            data = []
            for record in detail_records:
                item = {}
                for db_field, excel_header in headers_mapping.items():
                    # 获取字段值
                    value = getattr(record, db_field, '')

                    # 特殊处理日期时间字段
                    if (
                        db_field
                        in ['upload_time', 'release_date', 'change_date']
                        and value
                    ):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')

                    # 特殊处理数值字段
                    if db_field in ['per_amount', 'total_amount'] and value:
                        value = float(value)

                    item[excel_header] = value

                data.append(item)

            # 转换为DataFrame
            result_df = pd.DataFrame(data)

            # 如果有列缺失，添加空列
            for header in headers_mapping.values():
                if header not in result_df.columns:
                    result_df[header] = ''

            # 重新排序列
            if set(headers_mapping.values()).issubset(set(result_df.columns)):
                result_df = result_df[list(headers_mapping.values())]

            return result_df

        except Exception as e:
            logger.error(f'导出Excel失败: {str(e)}')
            return None
        finally:
            session.close()

    def save_to_excel(self, root_inv_code=None, version=None, filename=None):
        """
        将BOM数据从数据库导出到Excel文件

        Args:
            root_inv_code (str): 根物料编码，如果为None则使用当前查询的根物料
            version (int): 要导出的版本号，如果为None则导出最新版本
            filename (str): 文件名，如果为None则自动生成

        Returns:
            str: 保存的文件路径
        """
        try:
            session = self.Session()

            # 确定要导出的根物料
            if root_inv_code is None:
                if self.root_inv_code:
                    root_inv_code = self.root_inv_code
                else:
                    logger.error('未指定导出的根物料编码')
                    return None

            # 查询主表记录
            if version:
                # 查询指定版本
                master_record = (
                    session.query(BomMaster)
                    .filter(BomMaster.root_inv_code == root_inv_code)
                    .filter(BomMaster.version == version)
                    .first()
                )

                if not master_record:
                    logger.error(f'找不到物料 {root_inv_code} 的版本 {version} 的主记录')
                    return None
            else:
                # 查询最新版本
                master_record = (
                    session.query(BomMaster)
                    .filter(BomMaster.root_inv_code == root_inv_code)
                    .filter(BomMaster.is_latest == True)
                    .first()
                )

                if not master_record:
                    logger.error(f'找不到物料 {root_inv_code} 的最新版本主记录')
                    return None

            # 获取主表ID和版本
            master_id = master_record.id
            version = master_record.version

            # 查询明细表记录
            detail_records = (
                session.query(BomDetail)
                .filter(BomDetail.master_id == master_id)
                .all()
            )

            if not detail_records:
                logger.warning(f'物料 {root_inv_code} 版本 {version} 没有任何明细记录')
                return None

            # 定义新的表头映射
            headers_mapping = {
                'depth': '层级',
                'component_part': '所属部件',
                # "parent_id": "父级",
                'parent_inv_code': '父级编码',
                'inv_code': '物料编码',
                'memo_cn': '物料描述',
                'per_amount': '单层数量',
                'total_amount': '总数量',
                'unit': '单位',
                'drawing_version': '版本',
                'material_type': '物料类型',
                'mat_template': '物料模板',
                'drawing_no': '图号',
                'mat_group': '物料组',
                'material': '材料',
                'brand': '品牌',
                'product_line': '产品线',
                'check_status': 'BOM状态',
                'material_state': '物料状态',
                'source': '来源',
                'mac_ele': '机械电气',
                'upload_time': '上传时间',
                'release_date': '发布时间',
                'change_date': '变更时间',
                'm_attribute': '工艺属性',
                'package_group': '组批包',
                'master_version': '主数据版本',
                'in_out_factory': 'ERP',
            }

            # 转换记录为字典列表
            data = []
            for record in detail_records:
                item = {}
                for db_field, excel_header in headers_mapping.items():
                    # 获取字段值
                    value = getattr(record, db_field, '')

                    # 特殊处理日期时间字段
                    if (
                        db_field
                        in ['upload_time', 'release_date', 'change_date']
                        and value
                    ):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')

                    # 特殊处理数值字段
                    if db_field in ['per_amount', 'total_amount'] and value:
                        value = float(value)

                    item[excel_header] = value

                data.append(item)

            # 转换为DataFrame
            result_df = pd.DataFrame(data)

            # 如果有列缺失，添加空列
            for header in headers_mapping.values():
                if header not in result_df.columns:
                    result_df[header] = ''

            # 重新排序列
            if set(headers_mapping.values()).issubset(set(result_df.columns)):
                result_df = result_df[list(headers_mapping.values())]

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = (
                    f'物料_{root_inv_code}_BOM查询结果_V{version}_{timestamp}.xlsx'
                )

            # 保存到Excel
            result_df.to_excel(filename, index=False)
            logger.info(f'BOM数据已导出到: {filename}')
            return filename

        except Exception as e:
            logger.error(f'导出Excel失败: {str(e)}')
            return None
        finally:
            session.close()

    # 实用方法：查询变更记录
    def get_change_logs(
        self,
        root_inv_code,
        start_time=None,
        end_time=None,
        change_type=None,
        limit=100,
    ):
        """
        查询特定物料的变更记录

        Args:
            root_inv_code (str): 根物料编码
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间
            change_type (str): 变更类型 (ADD/MODIFY/DELETE)
            limit (int): 最大记录数

        Returns:
            list: 变更记录列表
        """
        try:
            session = self.Session()

            # 构建查询
            query = session.query(BomChangeLog).filter(
                BomChangeLog.root_inv_code == root_inv_code
            )

            # 添加时间筛选
            if start_time:
                query = query.filter(BomChangeLog.change_time >= start_time)

            if end_time:
                query = query.filter(BomChangeLog.change_time <= end_time)

            # 添加变更类型筛选
            if change_type:
                query = query.filter(BomChangeLog.change_type == change_type)

            # 添加排序和限制
            query = query.order_by(desc(BomChangeLog.change_time)).limit(limit)

            # 执行查询
            results = query.all()

            # 转换结果为字典列表
            logs = []
            for row in results:
                log = {
                    'log_id': row.log_id,
                    'root_inv_code': row.root_inv_code,
                    'prev_version': row.prev_version,
                    'curr_version': row.curr_version,
                    'inv_code': row.inv_code,
                    'component_part': row.component_part,
                    'memo_cn': row.memo_cn,
                    'parent_inv_code': row.parent_inv_code,
                    'change_type': row.change_type,
                    'change_time': row.change_time,
                    'change_fields': row.change_fields,
                }
                logs.append(log)

            return logs

        except Exception as e:
            logger.error(f'查询变更记录失败: {str(e)}')
            return []
        finally:
            session.close()

    def get_version_comparison(self, root_inv_code, version1, version2):
        """
        比较两个版本的差异

        Args:
            root_inv_code (str): 根物料编码
            version1 (int): 版本1
            version2 (int): 版本2

        Returns:
            dict: 包含差异信息的字典
        """
        try:
            session = self.Session()

            # 获取两个版本的主表记录
            version_records = (
                session.query(BomMaster)
                .filter(BomMaster.root_inv_code == root_inv_code)
                .filter(BomMaster.version.in_([version1, version2]))
                .all()
            )

            if len(version_records) != 2:
                logger.error(f'找不到指定的版本信息: {version1}, {version2}')
                return {'error': '找不到指定的版本信息'}

            # 整理版本信息
            version_map = {
                record.version: record.id for record in version_records
            }
            master_id1 = version_map.get(version1)
            master_id2 = version_map.get(version2)

            # 获取两个版本的物料清单
            items1 = (
                session.query(BomDetail)
                .filter(BomDetail.master_id == master_id1)
                .all()
            )

            items2 = (
                session.query(BomDetail)
                .filter(BomDetail.master_id == master_id2)
                .all()
            )

            # 转换为字典，以物料编码为键
            items1_dict = {item.inv_code: item for item in items1}
            items2_dict = {item.inv_code: item for item in items2}

            # 分析差异
            added = []
            deleted = []
            modified = []

            # 找出新增物料
            for inv_code in set(items2_dict.keys()) - set(items1_dict.keys()):
                item = items2_dict[inv_code]
                added.append(
                    {
                        'inv_code': item.inv_code,
                        'memo_cn': item.memo_cn,
                        'parent_inv_code': item.parent_inv_code,
                        'per_amount': float(item.per_amount)
                        if item.per_amount
                        else 0,
                        'depth': item.depth,
                        'component_part': item.component_part,
                        'material_type': item.material_type,
                        'drawing_no': item.drawing_no,
                    }
                )

            # 找出删除物料
            for inv_code in set(items1_dict.keys()) - set(items2_dict.keys()):
                item = items1_dict[inv_code]
                deleted.append(
                    {
                        'inv_code': item.inv_code,
                        'memo_cn': item.memo_cn,
                        'parent_inv_code': item.parent_inv_code,
                        'per_amount': float(item.per_amount)
                        if item.per_amount
                        else 0,
                        'depth': item.depth,
                        'component_part': item.component_part,
                        'material_type': item.material_type,
                        'drawing_no': item.drawing_no,
                    }
                )

            # 找出修改物料
            for inv_code in set(items1_dict.keys()).intersection(
                set(items2_dict.keys())
            ):
                item1 = items1_dict[inv_code]
                item2 = items2_dict[inv_code]

                # 比较关键字段
                changes = {}
                for field_name, field1, field2 in [
                    (
                        'parent_inv_code',
                        item1.parent_inv_code,
                        item2.parent_inv_code,
                    ),
                    (
                        'per_amount',
                        float(item1.per_amount) if item1.per_amount else 0,
                        float(item2.per_amount) if item2.per_amount else 0,
                    ),
                    ('depth', item1.depth, item2.depth),
                    (
                        'component_part',
                        item1.component_part,
                        item2.component_part,
                    ),
                ]:
                    if field1 != field2:
                        changes[field_name] = {'from': field1, 'to': field2}

                if changes:
                    modified.append(
                        {
                            'inv_code': item2.inv_code,
                            'memo_cn': item2.memo_cn,
                            'changes': changes,
                        }
                    )

            return {
                'root_inv_code': root_inv_code,
                'version1': version1,
                'version2': version2,
                'added_count': len(added),
                'deleted_count': len(deleted),
                'modified_count': len(modified),
                'added': added,
                'deleted': deleted,
                'modified': modified,
            }

        except Exception as e:
            logger.error(f'比较版本差异失败: {str(e)}')
            return {'error': str(e)}
        finally:
            session.close()

    def export_change_log(
        self, root_inv_code, filename=None, start_time=None, end_time=None
    ):
        """
        导出物料变更记录到Excel

        Args:
            root_inv_code (str): 根物料编码
            filename (str): 文件名，如果为None则自动生成
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间

        Returns:
            str: 保存的文件路径
        """
        try:
            # 查询变更记录
            logs = self.get_change_logs(
                root_inv_code,
                start_time=start_time,
                end_time=end_time,
                limit=1000,  # 增加查询上限，确保获取所有记录
            )

            if not logs:
                logger.warning(f'没有找到物料 {root_inv_code} 的变更记录')
                return None

            # 转换为DataFrame
            data = []
            for log in logs:
                # 解析变更字段
                changes = log.get('change_fields', {})
                if changes:
                    if isinstance(changes, str):
                        try:
                            changes = json.loads(changes)
                        except:
                            changes = {}

                    if isinstance(changes, dict):
                        changes_str = ', '.join(
                            [f'{k}: {v}' for k, v in changes.items()]
                        )
                    else:
                        changes_str = str(changes)
                else:
                    changes_str = ''

                row = {
                    '变更ID': log.get('log_id'),
                    '物料编码': log.get('inv_code'),
                    '物料描述': log.get('memo_cn'),
                    '父级编码': log.get('parent_inv_code'),
                    '变更类型': log.get('change_type'),
                    '从版本': log.get('prev_version'),
                    '到版本': log.get('curr_version'),
                    '变更时间': log.get('change_time'),
                    '变更内容': changes_str,
                }
                data.append(row)

            df = pd.DataFrame(data)

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'物料_{root_inv_code}_变更记录_{timestamp}.xlsx'

            # 保存到Excel
            df.to_excel(filename, index=False)
            logger.info(f'变更记录已保存到: {filename}')
            return filename

        except Exception as e:
            logger.error(f'导出变更记录失败: {str(e)}')
            return None


def main():
    """
    主函数
    """
    print('RDM物料BOM递归查询与变更跟踪工具')
    print('-' * 50)

    # 创建RDM BOM查询工具实例
    tracker = RDMBomTracker()

    # 菜单
    while True:
        print('\n功能菜单:')
        print('1. 查询BOM并跟踪变更')
        print('2. 查看变更记录')
        print('3. 比较两个版本')
        print('4. 导出变更记录')
        print('0. 退出')

        choice = input('\n请选择功能 (0-4): ')

        if choice == '1':
            # 查询BOM
            root_inv_code = input('请输入要查询的物料编码: ')
            factory_code = input('请输入工厂编码 (默认1020): ') or '1020'
            wbs_project_number = input('请输入WBS项目号 (可留空): ')

            print(f'\n开始查询物料: {root_inv_code}')

            start_time = time.time()
            results = tracker.query_bom(
                root_inv_code, factory_code, wbs_project_number
            )
            end_time = time.time()

            if results:
                print(f'\n查询完成! 共获取了 {len(results)} 条记录')
                print(f'查询耗时: {end_time - start_time:.2f} 秒')

                save_excel = input('\n是否保存到Excel (y/n): ')
                if save_excel.lower() == 'y':
                    filename = tracker.save_to_excel()
                    if filename:
                        print(f'已保存到文件: {filename}')
            else:
                print('\n查询失败或没有查询结果')

        elif choice == '2':
            # 查看变更记录
            root_inv_code = input('请输入要查询的物料编码: ')
            days = input('查询最近几天的记录 (默认30天): ') or '30'

            try:
                days = int(days)
                end_time = datetime.now()
                start_time = end_time - pd.Timedelta(days=days)

                logs = tracker.get_change_logs(
                    root_inv_code, start_time=start_time, end_time=end_time
                )

                if logs:
                    print(f'\n找到 {len(logs)} 条变更记录:')
                    for log in logs:
                        change_time = log.get('change_time').strftime(
                            '%Y-%m-%d %H:%M:%S'
                        )
                        print(
                            f"[{change_time}] {log.get('change_type')} - 物料: {log.get('inv_code')} ({log.get('memo_cn')})"
                        )
                else:
                    print(f'\n未找到物料 {root_inv_code} 在过去 {days} 天的变更记录')
            except ValueError:
                print('请输入有效的天数')

        elif choice == '3':
            # 比较两个版本
            root_inv_code = input('请输入要比较的物料编码: ')
            try:
                version1 = int(input('请输入第一个版本号: '))
                version2 = int(input('请输入第二个版本号: '))

                comparison = tracker.get_version_comparison(
                    root_inv_code, version1, version2
                )

                if 'error' in comparison:
                    print(f"\n比较失败: {comparison['error']}")
                else:
                    print('\n版本比较结果:')
                    print(f"物料: {comparison['root_inv_code']}")
                    print(
                        f"从版本 {comparison['version1']} 到版本 {comparison['version2']}"
                    )
                    print(f"新增: {comparison['added_count']} 项")
                    print(f"删除: {comparison['deleted_count']} 项")
                    print(f"修改: {comparison['modified_count']} 项")

                    show_details = input('\n是否显示详细信息 (y/n): ')
                    if show_details.lower() == 'y':
                        # 显示修改详情
                        if comparison['modified_count'] > 0:
                            print('\n修改物料:')
                            for item in comparison['modified']:
                                print(
                                    f"  物料: {item['inv_code']} ({item['memo_cn']})"
                                )
                                for field, change in item['changes'].items():
                                    print(
                                        f"    - {field}: {change['from']} -> {change['to']}"
                                    )

                        # 显示新增物料
                        if comparison['added_count'] > 0:
                            print('\n新增物料:')
                            for item in comparison['added']:
                                print(
                                    f"  物料: {item['inv_code']} ({item['memo_cn']})"
                                )

                        # 显示删除物料
                        if comparison['deleted_count'] > 0:
                            print('\n删除物料:')
                            for item in comparison['deleted']:
                                print(
                                    f"  物料: {item['inv_code']} ({item['memo_cn']})"
                                )
            except ValueError:
                print('请输入有效的版本号')

        elif choice == '4':
            # 导出变更记录
            root_inv_code = input('请输入要导出变更记录的物料编码: ')
            days = input('导出最近几天的记录 (默认30天): ') or '30'

            try:
                days = int(days)
                end_time = datetime.now()
                start_time = end_time - pd.Timedelta(days=days)

                filename = tracker.export_change_log(
                    root_inv_code, start_time=start_time, end_time=end_time
                )

                if filename:
                    print(f'\n变更记录已导出至: {filename}')
                else:
                    print(f'\n未找到物料 {root_inv_code} 在过去 {days} 天的变更记录，或导出失败')
            except ValueError:
                print('请输入有效的天数')

        elif choice == '0':
            print('\n感谢使用，再见!')
            break

        else:
            print('\n无效的选择，请重新输入')


if __name__ == '__main__':
    main()

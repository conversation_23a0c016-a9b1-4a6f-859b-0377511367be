#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import re
import os
import pandas as pd

ERPS = [37077]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    return value.split('-')[1] if match else ''


# 读取并物料信息Csv文件
def read_and_clean_motor_detail(file_path, df=None):
    if df is None:
        df = pd.read_csv(file_path)
    df['invertekDriveModel'] = df['invertekDriveModel'].str.split('/')
    df = df.explode('invertekDriveModel')
    # 清理物料描述并提取部件编号
    df['物料描述'] = (
        df['motorModel']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .replace(r'-XD.*', '', regex=True)
    )
    df['驱动器物料描述'] = (
        df['invertekDriveModel']
        .str.replace(r'-XD.*', '', regex=True)
        .replace(r'MIT\.', '', regex=True)
    )
    df = df[['物料描述', '驱动器物料描述']].dropna()

    return df.groupby('物料描述').agg(list).reset_index()


def merge_bom_and_motor_detail(df_bom, df_motor_detail, erp, output_path):
    df = df_bom.merge(df_motor_detail, on='物料描述', how='left')
    with open(os.path.join(output_path, 'drive_motor_missing.txt'), 'w') as f:
        f.write(f'{erp}: ')
        f.write(','.join(df[df['驱动器物料描述'].isna()]['物料描述'].unique()))
        f.write('\n')
    df = df.explode('驱动器物料描述')
    return df


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df['型号'] = df['型号'].str.replace(r"^'+", '', regex=True)
    df['*数量'] = df['*数量'].apply(
        lambda x: re.sub(r"^'+", '', x)
        if pd.notna(x) and isinstance(x, str)
        else x
    )
    df['名称'] = df['名称'].str.replace(r"^'+", '', regex=True)

    df['总数量'] = pd.to_numeric(df['*数量'], errors='coerce')
    df_bom = df[(df['名称'] == '伺服驱动器') | (df['名称'] == '伺服控制器')]

    # 清理物料描述并提取部件编号
    df_bom['物料描述'] = df_bom['型号'].replace(r'-XD.*', '', regex=True)

    return df_bom


# 对BOM数据按物料描述进行汇总
def summarize_bom_data(df_bom):
    # 分组汇总
    df_bom = (
        df_bom.groupby('物料描述')
        .apply(lambda x: pd.Series({'总数量': x['总数量'].sum()}))
        .reset_index()
    )

    return df_bom


# 读取机电沟通Excel文件
def read_and_clean_db_file(file_path, mapping_df=None, output_path='tests'):
    df = pd.read_excel(file_path)
    df['部件编号'] = df['部件编号'].fillna('')
    df2 = read_and_clean_motor_detail(
        'tests/mech_motor_details.csv', mapping_df
    )

    df = df[~df['EM中文'].str.contains('蛇形', na=False)]
    df_db = df[df['类别'] == '伺服电机'][['型号', '部件编号']].rename(
        columns={'型号': '物料描述'}
    )

    # 清理物料描述
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .replace(r'-XD.*', '', regex=True)
    )
    df_db = merge_bom_and_motor_detail(
        df_db, df2, file_path[6:11], output_path
    )
    df_db['物料描述备注'] = df_db['物料描述']
    df_db['物料描述'] = df_db['驱动器物料描述'].str.strip()
    df_db['部件编号'] = df_db['部件编号'].astype(str)
    return df_db


# 对数据库文件按物料描述进行汇总
def summarize_db_data(df_db):
    df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')
    # 合并结果
    # 统计部件号数量
    df_db_2 = (
        df_db.groupby('物料描述')['部件编号']
        .apply(
            lambda x: ', '.join(
                x.value_counts().index
                + ' ('
                + x.value_counts().astype(str)
                + ')'
            )
        )
        .reset_index(name='部件号统计')
    )

    # 合并结果
    return pd.merge(df_db_1, df_db_2, on='物料描述', how='left')


# 格式化部件号为两位数
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 解析部件号统计字符串，并返回字典
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


# 计算数量差异并添加对比结果状态列
def calculate_differences(df_bom, df_db):
    # 合并两个文件的数据
    comparison_df = pd.merge(
        df_bom[['物料描述', '总数量']],
        df_db[['物料描述', '总数量', '部件号统计']],
        on='物料描述',
        how='outer',
        suffixes=('_df_bom', '_df_db'),
    )

    # 用 NaN 值填充未匹配的数据
    comparison_df.fillna(0, inplace=True)

    # 计算数量差异
    comparison_df['数量差异'] = (
        comparison_df['总数量_df_bom'] - comparison_df['总数量_df_db']
    )

    # 添加对比结果状态列
    comparison_df['对比结果'] = comparison_df.apply(
        lambda row: 'df_bom数量更多'
        if row['数量差异'] > 0
        else 'df_db数量更多'
        if row['数量差异'] < 0
        else '数量相等',
        axis=1,
    )

    return comparison_df


# 生成汇总行并合并到结果DataFrame
def add_summary_row(comparison_df):
    total_bom = comparison_df['总数量_df_bom'].sum()
    total_db = comparison_df['总数量_df_db'].sum()
    total_diff = comparison_df['数量差异'].sum()

    summary_row = pd.DataFrame(
        {
            '物料描述': ['总数'],
            '总数量_df_bom': [total_bom],
            '总数量_df_db': [total_db],
            '数量差异': [total_diff],
            '对比结果': ['汇总'],
        }
    )

    return pd.concat([comparison_df, summary_row], ignore_index=True)


# 高亮差异较大的物料描述
def highlight_differences(s):
    if s['物料描述'] == '总数':
        return ['background-color: lightblue'] * len(s)  # 对汇总行进行不同的高亮
    elif abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


# 主流程
def main(ERP):
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(f'tests/{ERP}投料BOOM.xlsx')
    df_bom = summarize_bom_data(df_bom)

    # 读取并处理第二个文件
    df_db = read_and_clean_db_file(f'tests/{ERP}-机电沟通表.xlsx')
    df_db_2 = df_db[['物料描述', '物料描述备注']]
    df_db = summarize_db_data(df_db)

    drives_need_to_add = []
    drives_in_bom = set(df_bom['物料描述'])
    for i in df_db_2['物料描述备注'].unique():
        drives_in_db = df_db_2[df_db_2['物料描述备注'] == i]['物料描述'].unique()
        if pd.isna(drives_in_db).all():
            continue
        is_in_bom = False
        for j in drives_in_db:
            if j in drives_in_bom:
                is_in_bom = True
                break
        if not is_in_bom:
            drives_need_to_add.extend(list(drives_in_db))

    regex_pattern = '|'.join(df_bom['物料描述'])
    df_db = df_db[
        df_db['物料描述'].str.contains(regex_pattern, regex=True)
        | df_db['物料描述'].isin(drives_need_to_add)
    ]

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_bom, df_db)

    # 添加汇总行
    comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        f'tests/{ERP}_comparison_result_of_drive_motor.xlsx', index=False
    )


def run(bom_file, db_file, mapping_df, output_path):
    # 读取并处理第一个文件
    df_bom = read_and_clean_bom_file(bom_file)
    df_bom = summarize_bom_data(df_bom)

    # 读取并处理第二个文件
    df_db = read_and_clean_db_file(db_file, mapping_df, output_path)
    df_db_2 = df_db[['物料描述', '物料描述备注']]
    df_db = summarize_db_data(df_db)

    drives_need_to_add = []
    drives_in_bom = set(df_bom['物料描述'])
    for i in df_db_2['物料描述备注'].unique():
        drives_in_db = df_db_2[df_db_2['物料描述备注'] == i]['物料描述'].unique()
        if pd.isna(drives_in_db).all():
            continue
        is_in_bom = False
        for j in drives_in_db:
            if j in drives_in_bom:
                is_in_bom = True
                break
        if not is_in_bom:
            drives_need_to_add.extend(list(drives_in_db))

    regex_pattern = '|'.join(df_bom['物料描述'])
    df_db = df_db[
        df_db['物料描述'].str.contains(regex_pattern, regex=True)
        | df_db['物料描述'].isin(drives_need_to_add)
    ]

    # 比较两个文件的数据
    comparison_df = calculate_differences(df_bom, df_db)

    # 添加汇总行
    comparison_df = add_summary_row(comparison_df)

    # 高亮差异
    comparison_df_styled = comparison_df.style.apply(
        highlight_differences, axis=1
    )

    # 输出结果到Excel
    comparison_df_styled.to_excel(
        f'{output_path}/驱动器对比结果_图纸_机电沟通表.xlsx', index=False
    )

    return (
        {
            'type': 'electric_drive',
            'path': f'{output_path}/驱动器对比结果_图纸_机电沟通表.xlsx',
        },
    )


if __name__ == '__main__':
    try:
        os.remove('tests/drive_motor_missing.txt')
    except FileNotFoundError:
        print('file not found')
    for ERP in ERPS:
        main(ERP)

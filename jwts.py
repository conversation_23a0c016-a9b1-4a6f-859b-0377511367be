import jwt
from jwt.exceptions import PyJWTError

# JWT加密盐
JWT_SALT = 'ds()udsjo@jlsdosjf)wjd_#(#)$'


def parse_userid(token):
    """
    解析JWT token并提取用户ID

    :param token: JWT token字符串
    :return: 用户ID，如果解析失败则返回None
    """
    try:
        # 解码JWT token
        payload = jwt.decode(token, JWT_SALT, algorithms=['HS256'])

        # 从payload中获取用户ID
        user_id = payload.get('userId')

        return user_id
    except PyJWTError:
        # 捕获所有JWT相关的异常
        return None

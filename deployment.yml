apiVersion: apps/v1
kind: Deployment
metadata:
  name: bom-analysis
  labels:
    app: bom-analysis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bom-analysis
  template:
    metadata:
      labels:
        app: bom-analysis
    spec:
      containers:
      - name: bom-analysis
        image: *************:5000/bom-analysis:v1 # localhost:5000/bom-analysis:v1
        imagePullPolicy: Always  # Always pull the latest image
        ports:
        - containerPort: 8000
---
apiVersion: v1
kind: Service
metadata:
  name: bom-analysis-service
spec:
  type: NodePort
  selector:
    app: bom-analysis
  ports:
  - protocol: TCP
    port: 8000
    targetPort: 8000
    nodePort: 30088  # 可选，指定 NodePort 端口号，范围通常为 30000-32767

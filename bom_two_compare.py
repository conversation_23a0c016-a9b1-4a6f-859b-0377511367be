import re
import pandas as pd
from typing import Dict, Set
from models import BomReverseMapping
from core.database import get_db


# Add styling
def highlight_differences(row):
    if row['差异类型'] == 'BOM1中不存在':
        return ['background-color: yellow'] * len(row)
    elif row['差异类型'] == 'BOM2中不存在':
        return ['background-color: red'] * len(row)
    elif row['差异类型'] == '数量不同':
        return ['background-color: orange'] * len(row)
    return [''] * len(row)


def get_reverse_mapping_dict() -> tuple[Dict[str, str], Set[str]]:
    """
    获取正反机映射字典和需要过滤的物料编码集合

    Returns:
        tuple: (映射字典 {反机编码: 正机编码}, 需要过滤的物料编码集合)
    """
    db = get_db()
    mappings = db.query(BomReverseMapping).all()

    reverse_to_forward_map = {}  # 反机编码 -> 正机编码
    codes_to_filter = set()  # 需要过滤的编码（没有反机映射的）
    text_to_filter = set()

    for mapping in mappings:
        if mapping.reverse_inv_code and mapping.reverse_inv_code.strip():
            # 有反机编码，建立映射关系
            reverse_to_forward_map[mapping.reverse_inv_code] = mapping.inv_code
        elif mapping.inv_code == 'remove':
            text_to_filter.add(mapping.memo_cn)
        else:
            # 没有反机编码，加入过滤集合
            codes_to_filter.add(mapping.inv_code)

    return reverse_to_forward_map, codes_to_filter, text_to_filter


def apply_reverse_mapping(
    df: pd.DataFrame,
    reverse_map: Dict[str, str],
    codes_to_filter: Set[str],
    text_to_filter: Set[str],
) -> pd.DataFrame:
    """
    应用正反机映射和过滤逻辑

    Args:
        df: BOM数据框
        reverse_map: 反机到正机的映射字典
        codes_to_filter: 需要过滤掉的物料编码集合

    Returns:
        处理后的数据框
    """
    # 先过滤掉不需要比对的物料编码
    df = df[~df['物料编码'].isin(codes_to_filter)].copy()

    escaped_patterns = [re.escape(item) for item in text_to_filter]
    is_contains = df['物料描述'].str.contains(
        '|'.join(escaped_patterns), na=False, regex=True
    )
    df = df[~is_contains].copy()

    # 应用反机到正机的映射
    df['物料编码'] = df['物料编码'].map(lambda x: reverse_map.get(x, x))

    return df


def compare_two_boms(bom1_df, bom2_df, part_number, is_ev=False):
    """
    Compare two BOM tables for a specific part number with reverse mapping logic.

    Args:
        bom1_df (pd.DataFrame): First BOM table
        bom2_df (pd.DataFrame): Second BOM table
        part_number (str): Part number to filter

    Returns:
        tuple: (result DataFrame, number of differences, total rows)
    """

    def clean_bom_data(df, part_number, is_ev):
        # Extract part number from '所属部件'
        df['部件编号'] = df['所属部件'].apply(
            lambda x: str(x).split('-')[1]
            if isinstance(x, str) and '-' in str(x)
            else ''
        )

        # Filter for specific part number and required columns
        if str(part_number) not in ['17', '18'] or not is_ev:
            df = df[df['部件编号'] == str(part_number).zfill(2)][
                ['物料描述', '总数量', '部件编号', '物料编码']
            ].copy()
        else:
            df = df[(df['部件编号'] == '17') | (df['部件编号'] == '18')][
                ['物料描述', '总数量', '部件编号', '物料编码']
            ].copy()

        # Convert '总数量' to numeric
        df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')

        # Group by to handle duplicates
        df = df.groupby(['物料编码', '部件编号'], as_index=False).agg(
            {'总数量': 'sum', '物料描述': 'first'}
        )

        return df.sort_values(['物料描述']).reset_index(drop=True)

    # Clean and prepare both BOMs
    bom1_cleaned = clean_bom_data(bom1_df, part_number, is_ev)
    bom2_cleaned = clean_bom_data(bom2_df, part_number, is_ev)

    # 获取正反机映射关系
    reverse_map, codes_to_filter, text_to_filter = get_reverse_mapping_dict()

    # 应用正反机映射和过滤逻辑
    bom1_mapped = apply_reverse_mapping(
        bom1_cleaned, reverse_map, codes_to_filter, text_to_filter
    )
    bom2_mapped = apply_reverse_mapping(
        bom2_cleaned, reverse_map, codes_to_filter, text_to_filter
    )

    # 重新按物料编码分组，因为映射后可能有重复
    bom1_mapped = (
        bom1_mapped.groupby(['物料编码', '部件编号'], as_index=False)
        .agg({'总数量': 'sum', '物料描述': 'first'})
        .sort_values(['物料描述'])
        .reset_index(drop=True)
    )

    bom2_mapped = (
        bom2_mapped.groupby(['物料编码', '部件编号'], as_index=False)
        .agg({'总数量': 'sum', '物料描述': 'first'})
        .sort_values(['物料描述'])
        .reset_index(drop=True)
    )

    # Create comparison DataFrame
    result_df = pd.DataFrame(
        columns=['物料编码', '物料描述', '总数量', '部件编号', 'BOM2物料描述', 'BOM2总数量', '差异类型']
    )

    # Compare rows
    for idx, row1 in bom1_mapped.iterrows():
        # Look for matching material code and description in BOM2
        matches = bom2_mapped[(bom2_mapped['物料编码'] == row1['物料编码'])]

        if len(matches) == 0:
            # No match found in BOM2
            new_row = {
                '物料编码': row1['物料编码'],
                '物料描述': row1['物料描述'],
                '总数量': row1['总数量'],
                '部件编号': row1['部件编号'],
                'BOM2物料描述': None,
                'BOM2总数量': None,
                '差异类型': 'BOM2中不存在',
            }
            result_df = pd.concat(
                [result_df, pd.DataFrame([new_row])], ignore_index=True
            )
        else:
            # Match found, compare quantities
            for _, match in matches.iterrows():
                new_row = {
                    '物料编码': row1['物料编码'],
                    '物料描述': row1['物料描述'],
                    '总数量': row1['总数量'],
                    '部件编号': row1['部件编号'],
                    'BOM2物料描述': match['物料描述'],
                    'BOM2总数量': match['总数量'],
                    '差异类型': '数量不同' if row1['总数量'] != match['总数量'] else '完全相同',
                }
                result_df = pd.concat(
                    [result_df, pd.DataFrame([new_row])], ignore_index=True
                )

    # Find items in BOM2 that don't exist in BOM1
    for idx, row2 in bom2_mapped.iterrows():
        if not any((bom1_mapped['物料编码'] == row2['物料编码'])):
            new_row = {
                '物料编码': row2['物料编码'],
                '物料描述': None,
                '总数量': None,
                '部件编号': row2['部件编号'],
                'BOM2物料描述': row2['物料描述'],
                'BOM2总数量': row2['总数量'],
                '差异类型': 'BOM1中不存在',
            }
            result_df = pd.concat(
                [result_df, pd.DataFrame([new_row])], ignore_index=True
            )

    # Calculate statistics
    total_rows = len(result_df)
    diff_count = len(result_df[result_df['差异类型'] != '完全相同'])

    result_df['异常原因'] = ''
    result_df['负责人'] = ''
    styled_df = result_df.style.apply(highlight_differences, axis=1)

    return styled_df, diff_count, total_rows


def run_bom_comparison(
    bom1_df, bom2_df, part_number, output_path, is_ev=False
):
    """
    Run the BOM comparison and save results to Excel.

    Args:
        bom1_df (pd.DataFrame): First BOM table
        bom2_df (pd.DataFrame): Second BOM table
        part_number (str): Part number to filter
        output_path (str): Path to save the output Excel file

    Returns:
        tuple: (output file path, number of differences, total rows)
    """
    styled_df, diff_count, total_rows = compare_two_boms(
        bom1_df, bom2_df, part_number, is_ev
    )

    output_file = f'{output_path}/正反机物料一致性对比.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
        styled_df.to_excel(writer, index=False, encoding='utf-8')

    return output_file, diff_count, total_rows


# 使用示例
if __name__ == '__main__':

    # 创建数据库连接（根据你的实际配置修改）
    # engine = create_engine("your_database_url")
    # SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    # db = SessionLocal()

    # 加载两个BOM表
    bom1_df = pd.read_excel(
        '/home/<USER>/code/bom-analysis/tests/37564/37564投料BOM20250207.xlsx'
    )
    bom2_df = pd.read_excel(
        '/home/<USER>/code/bom-analysis/tests/37564/37565投料BOM20250207.xlsx'
    )

    # 运行比对（需要传入数据库session）
    # output_file, diff_count, total_rows = run_bom_comparison(
    #     bom1_df, bom2_df, part_number='112', output_path='./tests', db=db
    # )

    # print(f'总行数: {total_rows}')
    # print(f'差异行数: {diff_count}')
    # print(f'结果保存在: {output_file}')

    # db.close()

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''=================================================
@Project -> File   ：pc_to_pi.py -> bom_cp_db
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2024/9/4 16:07
@Desc   ：
=================================================='''
import re
import os
import pandas as pd

ERPS = [37130, 37100]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'
CABLES_OR = []
POWERKEYS = []
ENCODERKEYS = []
BREAKKEYS = []


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    if match:
        return value.split('-')[1]  # 返回第二个部分
    return ''  # 如果不匹配，返回空字符串


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')

    # 第一层筛选 - 只处理伺服电缆
    servo_cable_mask = df['物料描述'].str.contains(
        r'伺服(动力|编码器|制动|刹车)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)?(电缆|线缆)_',
        na=False,
        regex=True,
    ) | df['物料描述'].str.contains(
        r'(步进伺服|直线电机)(动力|编码器|电机)(电缆|线缆|延长线)_', na=False, regex=True
    )

    # 只保留伺服电缆的行并处理
    servo_df = df[servo_cable_mask].copy()

    # 为伺服电缆添加必要的列
    servo_df['电缆物料描述'] = servo_df['物料描述']
    servo_df['提取结果'] = None
    servo_df['已处理'] = False  # 标记是否已被某个模式处理

    # S6 模式处理
    s6_mask = servo_df['物料描述'].str.contains(
        r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_S6-',
        na=False,
        regex=True,
    )
    if s6_mask.any():
        pattern = r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_([A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+)'
        servo_df.loc[s6_mask, '提取结果'] = servo_df.loc[s6_mask, '物料描述'].apply(
            lambda x: re.search(pattern, x)
        )
        servo_df.loc[s6_mask, '物料描述'] = servo_df.loc[s6_mask, '提取结果'].apply(
            lambda x: x.group(4) if x else None
        )
        servo_df.loc[s6_mask, '已处理'] = True

    # HCXD, SVCAB, CAB 模式处理
    hcxd_svcab_cab_mask = (
        servo_df['物料描述'].str.contains(
            r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_HCXD-',
            na=False,
            regex=True,
        )
        | servo_df['物料描述'].str.contains(
            r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_SVCAB-',
            na=False,
            regex=True,
        )
        | servo_df['物料描述'].str.contains(
            r'伺服(动力|编码器|制动|刹车)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)?(电缆|线缆)_CAB-',
            na=False,
            regex=True,
        )
    ) & ~servo_df[
        '已处理'
    ]  # 只处理尚未处理的行

    if hcxd_svcab_cab_mask.any():
        # 修复正则表达式以正确匹配SVCAB线缆格式
        # 支持格式如: SVCAB-PWR075CA-085L-10(...) 和 SVCAB-ENC075CA-ABS-085L-10(...)
        def extract_svcab_cable_code(description):
            # 动力线模式: SVCAB-PWR075CA 和 SVCAB-PWB075CA
            power_pattern = r'伺服动力(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)?(电缆|线缆)_(SVCAB-PW[RB][A-Za-z0-9]+)'
            power_match = re.search(power_pattern, description)
            if power_match:
                return power_match.group(3)

            # 编码器线模式: SVCAB-ENC075CA-ABS 或 SVCAB-ENC75A-ABS
            encoder_pattern = r'伺服编码器(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)?(电缆|线缆)_(SVCAB-ENC[A-Za-z0-9]+-[A-Z]+)'
            encoder_match = re.search(encoder_pattern, description)
            if encoder_match:
                return encoder_match.group(3)

            # 通用HCXD和CAB模式（保持原有逻辑）
            general_pattern = r'伺服(动力|编码器|制动|刹车)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)?(电缆|线缆)_([A-Za-z0-9.-]+?)-\d+(\.\d+)?M'
            general_match = re.search(general_pattern, description)
            if general_match:
                return general_match.group(4)

            return None

        servo_df.loc[hcxd_svcab_cab_mask, '物料描述'] = servo_df.loc[
            hcxd_svcab_cab_mask, '物料描述'
        ].apply(extract_svcab_cable_code)
        servo_df.loc[hcxd_svcab_cab_mask, '已处理'] = True

    # MR 模式处理
    mr_mask = (
        servo_df['物料描述'].str.contains(
            r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_MR-',
            na=False,
            regex=True,
        )
        & ~servo_df['已处理']
    )  # 只处理尚未处理的行

    if mr_mask.any():
        pattern = r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_(MR-[A-Za-z0-9.+-]+.*)'

        def replace_outside_brackets(text):
            # 先去掉括号内容（技术规格信息）
            text = re.sub(r'\(.*?\)', '', text)

            # 将数字+M替换为%M (如 8M -> %M, 10.5M -> %M)
            text = re.sub(r'(\d+(?:\.\d+)?)M-', '%M-', text)

            return text

        servo_df.loc[mr_mask, '提取结果'] = servo_df.loc[mr_mask, '物料描述'].apply(
            lambda x: re.search(pattern, x)
        )
        servo_df.loc[mr_mask, '物料描述'] = servo_df.loc[mr_mask, '提取结果'].apply(
            lambda x: replace_outside_brackets(x.group(4)) if x else None
        )
        servo_df.loc[mr_mask, '已处理'] = True

    # 步进伺服电机延长线_T2109-1000-P-IGUS   步进伺服编码器延长线_T2118-1000-P-IGUS 直线电机动力线缆_TCD-S0.34-TICBEL-PV-P16-JEC02-6M  直线电机编码器线缆_TCX-CDHD-RG15-DB19-FRF-TICBEL-B-6M 模式处理
    new_mask = (
        servo_df['物料描述'].str.contains(
            r'(步进伺服|直线电机)(动力|编码器|电机)(电缆|线缆|延长线)_',
            na=False,
            regex=True,
        )
        & ~servo_df['已处理']
    )  # 只处理尚未处理的行

    if new_mask.any():
        pattern = r'(步进伺服|直线电机)(动力|编码器|电机)(电缆|线缆|延长线)_([A-Za-z0-9.+-].*)'

        def replace_outside_brackets_new(text):
            parts = re.split(r'(\(.*?\))', text)
            replaced_parts = [
                re.sub(r'(\d+(\.\d+)?)M', '%M', part)
                if not part.startswith('(')
                else part
                for part in parts
            ]
            return ''.join(replaced_parts)

        servo_df.loc[new_mask, '提取结果'] = servo_df.loc[new_mask, '物料描述'].apply(
            lambda x: re.search(pattern, x)
        )
        servo_df.loc[new_mask, '物料描述'] = servo_df.loc[new_mask, '提取结果'].apply(
            lambda x: replace_outside_brackets_new(x.group(4)) if x else None
        )
        servo_df.loc[new_mask, '已处理'] = True

    # 伺服动力标准电缆 Cp-N1A08-10-06-00   伺服动力标准电缆 CP-N1A08-10-06-00 伺服编码器标准电缆 CF-E0FB17-02-06-00  伺服编码器标准电缆 CF-E0FB17-02-06-00 模式处理
    new1_mask = (
        servo_df['物料描述'].str.contains(
            r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_(CP|CF)',
            na=False,
            regex=True,
        )
        & ~servo_df['已处理']
    )  # 只处理尚未处理的行
    if new1_mask.any():
        # 修改后的pattern：匹配完整编号，允许空格或下划线分隔
        pattern = (
            r'伺服(动力|编码器|制动)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)(电缆|线缆)_([A-Za-z0-9.+-].*)'
        )

        def extract_code_prefix(full_code):
            """从完整编号中提取前缀部分，如 CF-E0FB17-02-06-00 -> CF-E0FB17-02"""
            if not full_code:
                return None
            # 分割并取前3部分（去掉最后的-XX-XX）
            parts = full_code.split('-')
            if len(parts) >= 3:
                return '-'.join(parts[:3])
            return full_code

        servo_df.loc[new1_mask, '提取结果'] = servo_df.loc[
            new1_mask, '物料描述'
        ].apply(lambda x: re.search(pattern, x))
        servo_df.loc[new1_mask, '物料描述'] = servo_df.loc[
            new1_mask, '提取结果'
        ].apply(lambda x: extract_code_prefix(x.group(4)) if x else None)
        servo_df.loc[new1_mask, '已处理'] = True
    # 过滤掉未处理的伺服电缆行
    # servo_df = servo_df[servo_df['已处理']]

    # 去除无效的处理结果
    servo_df = servo_df[~servo_df['物料描述'].isna()]

    # 只处理外购件
    df_bom = servo_df[servo_df['物料类型'] == '外购件']

    # 提取部件编号
    df_bom['部件编号'] = df_bom['所属部件'].apply(extract_second_part)

    # 整理最终结果
    df_bom = df_bom.sort_values(by='部件编号').reset_index(drop=True)[
        ['物料描述', '部件编号', '总数量', '电缆物料描述']
    ]

    # 处理柔性电缆加上 "-H" 后缀
    df_bom['物料描述'] = df_bom.apply(
        lambda x: f"{x['物料描述']}-H" if '柔' in x['电缆物料描述'] else x['物料描述'], axis=1
    )

    # 合并重复的物料描述和部件编号
    df_bom_grouped = df_bom.groupby(['物料描述', '部件编号'], as_index=False).agg(
        {'总数量': 'sum'}
    )

    return df_bom_grouped


def merge_db_and_motor_detail(df_db, df_motor_detail, erp, output_path):
    # 通过物料编码合并两个表
    df = pd.merge(df_db, df_motor_detail, on='物料描述', how='left')

    # 新列：将处理后的物料描述字段整合在一起
    df['动力线物料描述'] = df.apply(
        lambda x: f"{x['动力线-柔性物料描述']}-H"
        if x['是否过拖链'] and x['动力线-柔性物料描述'] != '自制'
        else x['动力线-普通物料描述'],
        axis=1,
    )
    df['编码器线物料描述'] = df.apply(
        lambda x: f"{x['编码器线-柔性物料描述']}-H"
        if x['是否过拖链'] and x['动力线-柔性物料描述'] != '自制'
        else x['编码器线-普通物料描述'],
        axis=1,
    )
    df['刹车线物料描述'] = df.apply(
        lambda x: (
            x['刹车线-柔性物料描述'] + '-H'
            if x['刹车线-柔性物料描述'] and x['刹车线-柔性物料描述'] != '自制'
            else None
        )
        if x['是否过拖链']
        else x['刹车线-普通物料描述'],
        axis=1,
    )

    with open(os.path.join(output_path, 'cable_motor_missing.txt'), 'w') as f:
        f.write(f'{erp}: ')
        na_list = df[df['动力线物料描述'].isna() | df['编码器线物料描述'].isna()][
            '物料描述'
        ].unique()
        f.write(','.join(na_list))
        f.write('\n')
    return df


# 读取并物料信息Csv文件
def read_and_clean_motor_detail(file_path, df=None):
    if df is None:
        df = pd.read_csv(file_path)
    # 清理物料描述并提取部件编号
    df['物料描述'] = (
        df['motorModel']
        .str.replace(f'{JIDIAN_PREFIX}_', '', regex=False)
        .str.replace(r'-XD.*', '', regex=True)
    )

    # 要处理的列
    columns_to_process = [
        'powerLineStandard',
        'powerLineFlexible',
        'encoderLineStandard',
        'encoderLineFlexible',
        'brakeLineStandard',
        'brakeLineFlexible',
    ]
    # 处理每一列
    for col in columns_to_process:

        # 首先按/分割取第一部分
        df[col] = df[col].apply(
            lambda x: x.split('/')[0] if pd.notna(x) and '/' in x else x
        )

        # 处理原字段，提取字符串中的字母数字三段组合（如 S6-L-B107）
        df[col] = df[col].apply(
            lambda x: x.split('-？')[0] if pd.notna(x) and '-？' in x else x
        )

        df[col] = df[col].apply(
            lambda x: x.split('-?')[0] if pd.notna(x) and '-?' in x else x
        )

        if col in ['powerLineFlexible', 'encoderLineFlexible']:
            df_or = df[df[col].str.contains('正向出线：', na=False)][col]
            for i in df_or.values:
                CABLES_OR.append(
                    [
                        j.replace('正向出线：', '').replace('向出线：', '')
                        for j in i.split('/反')
                    ]
                )
            df[col] = df[col].apply(
                lambda x: [
                    i.replace('正向出线：', '').replace('向出线：', '')
                    for i in x.split('/反')
                ]
                if pd.notna(x) and '/反' in x
                else x
            )

    df = df.explode('powerLineFlexible').explode('encoderLineFlexible')
    for col in columns_to_process:
        df[col] = df[col].str.strip()

    df['brakeLineStandard'] = df['brakeLineStandard'].apply(
        lambda x: x if x != '/' else None
    )
    df['brakeLineFlexible'] = df['brakeLineFlexible'].apply(
        lambda x: x if x != '/' else None
    )

    # 重命名列
    renamed_columns = {
        'powerLineStandard': '动力线-普通物料描述',
        'powerLineFlexible': '动力线-柔性物料描述',
        'encoderLineStandard': '编码器线-普通物料描述',
        'encoderLineFlexible': '编码器线-柔性物料描述',
        'brakeLineStandard': '刹车线-普通物料描述',
        'brakeLineFlexible': '刹车线-柔性物料描述',
    }

    # 应用列重命名
    df.rename(columns=renamed_columns, inplace=True)

    global POWERKEYS
    global ENCODERKEYS
    global BREAKKEYS

    POWERKEYS = list(pd.concat([df['动力线-柔性物料描述'], df['动力线-普通物料描述']]).unique())

    ENCODERKEYS = list(
        pd.concat([df['编码器线-柔性物料描述'], df['编码器线-普通物料描述']]).unique()
    )

    BREAKKEYS = list(pd.concat([df['刹车线-柔性物料描述'], df['刹车线-普通物料描述']]).unique())

    # 根据加号生成的列名一起重命名
    for original_col in renamed_columns:
        brake_line_col = original_col + '（刹车线）'
        if brake_line_col in df.columns:
            df.rename(
                columns={
                    brake_line_col: renamed_columns[original_col] + '（刹车线）'
                },
                inplace=True,
            )
    # 移除全是 None 的列
    df = df.dropna(axis=1, how='all')

    # 确保物料描述是唯一的，如果不是就保留第一个出现的记录
    df = df.drop_duplicates(subset=['物料描述'], keep='first')

    # 构建要保留的列列表，只包含存在的列
    columns_to_keep = [
        '物料描述',
        '动力线-普通物料描述',
        '动力线-柔性物料描述',
        '编码器线-普通物料描述',
        '编码器线-柔性物料描述',
    ]

    # 只有当刹车线列存在时才添加
    if '刹车线-普通物料描述' in df.columns:
        columns_to_keep.append('刹车线-普通物料描述')
    if '刹车线-柔性物料描述' in df.columns:
        columns_to_keep.append('刹车线-柔性物料描述')

    df = df[columns_to_keep]

    return df


# 读取机电沟通Excel文件
def read_and_clean_db_file(
    file_path, mapping_df=None, output_path='tests', code_mapping=None
):
    df = pd.read_excel(file_path)
    df['部件编号'] = df['部件编号'].fillna('')
    df2 = read_and_clean_motor_detail(
        'tests/mech_motor_details.csv', mapping_df
    )

    df = df[~df['EM中文'].str.contains('蛇形', na=False)]
    df_db = df[df['类别'] == '伺服电机'][
        ['型号', '部件编号', '是否过拖链', 'EM中文', '功能描述']
    ].rename(columns={'型号': '物料描述'})

    # 转换数据类型并排序
    df_db['部件编号'] = df_db['部件编号'].apply(
        lambda x: str(int(x)) if isinstance(x, float) else str(x)
    )

    df_db['部件编号'] = df_db['部件编号'].apply(lambda x: x.zfill(2))

    # 使用映射替换两位编码为三位编码
    if code_mapping is not None:
        # 只替换存在于映射中的编码
        df_db['部件编号'] = df_db['部件编号'].apply(
            lambda x: code_mapping.get(x, x)  # 如果不存在，返回原值
        )

    # 清理物料描述
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .str.replace(r'-XD.*', '', regex=True)
    )
    df_db['是否过拖链'] = df_db['是否过拖链'].apply(
        lambda x: True if x == '是' else False
    )
    df_db['伺服电机物料描述'] = df_db['物料描述']
    df_db = merge_db_and_motor_detail(df_db, df2, file_path[6:11], output_path)
    df_db['刹车线物料描述'] = df_db['刹车线物料描述'].apply(
        lambda x: None if x == '/' or x == '否' or x == '无' or x == '自制' else x
    )
    return df_db[
        ['伺服电机物料描述', '动力线物料描述', '编码器线物料描述', '刹车线物料描述', '部件编号', 'EM中文', '功能描述']
    ]


# 格式化部件号
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 比较两者的部件号和数量差异
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


def highlight_row(row):
    # 如果原表无值，标记为红色；BOM表无值，标记为黄色
    if row['异常'] == 1:
        return ['background-color: red'] * len(row)
    return [''] * len(row)


def merge_df(df_bom, df_db):
    result_df = pd.DataFrame(
        columns=[
            '伺服电机物料描述',
            '部件编号',
            'EM中文',
            '功能描述',
            '动力线物料描述',
            '编码器线物料描述',
            '刹车线物料描述',
            'BOM表动力线物料描述',
            'BOM表编码器线物料描述',
            'BOM表刹车线物料描述',
        ]
    )

    # Step 1: 创建唯一键“物料描述-部件编号”用于匹配
    df_bom['物料描述-部件编号'] = df_bom['物料描述'] + '-' + df_bom['部件编号'].astype(str)
    df_db['编码器线物料描述-部件编号'] = (
        df_db['编码器线物料描述'] + '-' + df_db['部件编号'].astype(str)
    )
    df_db['动力线物料描述-部件编号'] = df_db['动力线物料描述'] + '-' + df_db['部件编号'].astype(str)
    df_db['刹车线物料描述-部件编号'] = (
        df_db['刹车线物料描述'].fillna('') + '-' + df_db['部件编号'].astype(str)
    )

    # Step 2: 遍历数据库数据，根据动力线和编码器线唯一键检查 BOM 表
    for _, db_row in df_db.iterrows():
        power_key = db_row['动力线物料描述-部件编号']
        encoder_key = db_row['编码器线物料描述-部件编号']
        break_key = db_row['刹车线物料描述-部件编号']

        bom_power_match = df_bom[df_bom['物料描述-部件编号'] == power_key]
        bom_encoder_match = df_bom[df_bom['物料描述-部件编号'] == encoder_key]
        bom_break_match = None
        if db_row['刹车线物料描述']:
            bom_break_match = df_bom[df_bom['物料描述-部件编号'] == break_key]

        if (
            not bom_power_match.empty
            or not bom_encoder_match.empty
            or (db_row['刹车线物料描述'] and not bom_break_match.empty)
        ):
            # 初始化动力线和编码器线BOM描述
            bom_power_description = None
            bom_encoder_description = None
            bom_break_description = None

            # 根据动力线匹配
            if not bom_power_match.empty:
                for _, bom_row in bom_power_match.iterrows():
                    if bom_row['总数量'] > 0:
                        bom_power_description = bom_row['物料描述']
                        # 减少 BOM 中的总数量
                        df_bom.loc[
                            df_bom['物料描述-部件编号'] == power_key, '总数量'
                        ] -= 1
                        break

            # 根据编码器线匹配
            if not bom_encoder_match.empty:
                for _, bom_row in bom_encoder_match.iterrows():
                    if bom_row['总数量'] > 0:
                        bom_encoder_description = bom_row['物料描述']
                        # 减少 BOM 中的总数量
                        df_bom.loc[
                            df_bom['物料描述-部件编号'] == encoder_key, '总数量'
                        ] -= 1
                        break

            if db_row['刹车线物料描述'] and not bom_break_match.empty:
                for _, bom_row in bom_break_match.iterrows():
                    if bom_row['总数量'] > 0:
                        bom_break_description = bom_row['物料描述']
                        # 减少 BOM 中的总数量
                        df_bom.loc[
                            df_bom['物料描述-部件编号'] == break_key, '总数量'
                        ] -= 1
                        break

            # 将匹配的动力线和编码器线写入同一行
            result_df = pd.concat(
                [
                    result_df,
                    pd.DataFrame(
                        [
                            {
                                '伺服电机物料描述': db_row['伺服电机物料描述'],
                                '部件编号': db_row['部件编号'],
                                'EM中文': db_row['EM中文'],
                                '功能描述': db_row['功能描述'],
                                '动力线物料描述': db_row['动力线物料描述'],
                                '编码器线物料描述': db_row['编码器线物料描述'],
                                '刹车线物料描述': db_row['刹车线物料描述'],
                                'BOM表动力线物料描述': bom_power_description,
                                'BOM表编码器线物料描述': bom_encoder_description,
                                'BOM表刹车线物料描述': bom_break_description,
                            }
                        ]
                    ),
                ]
            )
        else:
            # 如果 BOM 中没有对应记录，直接将 DB 中的记录插入结果
            result_df = pd.concat(
                [
                    result_df,
                    pd.DataFrame(
                        [
                            {
                                '伺服电机物料描述': db_row['伺服电机物料描述'],
                                '部件编号': db_row['部件编号'],
                                'EM中文': db_row['EM中文'],
                                '功能描述': db_row['功能描述'],
                                '动力线物料描述': db_row['动力线物料描述'],
                                '编码器线物料描述': db_row['编码器线物料描述'],
                                '刹车线物料描述': db_row['刹车线物料描述'],
                                'BOM表动力线物料描述': None,
                                'BOM表编码器线物料描述': None,
                                'BOM表刹车线物料描述': None,
                            }
                        ]
                    ),
                ]
            )
    # Step 3: 遍历 BOM 表，添加 DB 表中不存在的 BOM 记录
    for _, bom_row in df_bom.iterrows():
        row_data = bom_row['物料描述']
        if bom_row['物料描述'].endswith('-H'):
            row_data = bom_row['物料描述'][:-2]
        if bom_row['总数量'] > 0:
            for _ in range(bom_row['总数量']):

                if row_data in POWERKEYS:
                    bom_power_description = bom_row['物料描述']
                    bom_encoder_description = None
                    bom_break_description = None
                elif row_data in BREAKKEYS:
                    bom_power_description = None
                    bom_encoder_description = None
                    bom_break_description = bom_row['物料描述']
                elif row_data in ENCODERKEYS:
                    bom_power_description = None
                    bom_encoder_description = bom_row['物料描述']
                    bom_break_description = None
                else:
                    bom_power_description = None
                    bom_encoder_description = bom_row['物料描述']  # 默认分配到编码器线
                    bom_break_description = None
                result_df = pd.concat(
                    [
                        result_df,
                        pd.DataFrame(
                            [
                                {
                                    '伺服电机物料描述': None,
                                    '部件编号': bom_row['部件编号'],
                                    'EM中文': None,
                                    '功能描述': None,
                                    '动力线物料描述': None,
                                    '编码器线物料描述': None,
                                    '刹车线物料描述': None,
                                    'BOM表动力线物料描述': bom_power_description,
                                    'BOM表编码器线物料描述': bom_encoder_description,
                                    'BOM表刹车线物料描述': bom_break_description,
                                }
                            ]
                        ),
                    ]
                )

    # Reset index after concatenation
    result_df.reset_index(drop=True, inplace=True)
    result_df = result_df.replace({None: ''})

    # 生成"异常"列：如果任一物料描述或BOM表物料描述为空，则标记为1，否则为0
    result_df['异常'] = result_df.apply(
        lambda row: 1
        if row['动力线物料描述'] == ''
        or row['编码器线物料描述'] == ''
        or row['BOM表动力线物料描述'] == ''
        or row['BOM表编码器线物料描述'] == ''
        else 0,
        axis=1,
    )

    # 刹车线异常检测：都没有或都有就正常，一个有一个没有就异常
    result_df['异常'] = result_df.apply(
        lambda row: 1
        if (
            # 刹车线异常：一个有一个没有的情况
            (
                # 情况1：机电沟通表有刹车线，但BOM表没有
                (row['刹车线物料描述'] != '' and not pd.isna(row['刹车线物料描述']))
                and (pd.isna(row['BOM表刹车线物料描述']) or row['BOM表刹车线物料描述'] == '')
            )
            or (
                # 情况2：机电沟通表没有刹车线，但BOM表有
                (row['刹车线物料描述'] == '' or pd.isna(row['刹车线物料描述']))
                and (
                    not pd.isna(row['BOM表刹车线物料描述'])
                    and row['BOM表刹车线物料描述'] != ''
                )
            )
        )
        or row['异常'] == 1
        else 0,
        axis=1,
    )

    # 应用样式并返回
    styled_df = result_df.style.apply(highlight_row, axis=1)
    return styled_df


# 主函数，执行所有流程
def main(ERP):
    # 读取和处理 BOM 文件
    df_bom = read_and_clean_bom_file(
        '/home/<USER>/code/bom-analysis/tests/38147/38147-B0723.xlsx'
    )

    # 读取和处理机电沟通表
    df_db = read_and_clean_db_file(
        '/home/<USER>/code/bom-analysis/tests/38147/erp_mech_info_202507240932.xlsx'
    )

    styled_df = merge_df(df_bom, df_db)

    styled_df.to_excel(f'tests/{ERP}电缆投料信息汇总.xlsx', index=False)


def run(bom_file, db_file, mapping_df, output_path, code_mapping=None):
    # 读取和处理 BOM 文件
    df_bom = read_and_clean_bom_file(bom_file)

    # 读取和处理机电沟通表
    df_db = read_and_clean_db_file(
        db_file, mapping_df, output_path, code_mapping
    )

    styled_df = merge_df(df_bom, df_db)

    styled_df.to_excel(f'{output_path}/3电缆比对_BOM_机电沟通表.xlsx', index=False)
    return ({'type': 'cable', 'path': f'{output_path}/3电缆比对_BOM_机电沟通表.xlsx'},)


if __name__ == '__main__':
    # for ERP in ERPS:
    #     main(ERP)
    main('38150')

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''=================================================
@Project -> File   ：pc_to_pi.py -> bom_cp_db
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2024/9/4 16:07
@Desc   ：
=================================================='''
import re
import os
import pandas as pd

ERPS = [37130, 37100]
JIDIAN_PREFIX = '伺服电机'
TOULIAO_PREFIX = '伺服驱动器'
CABLES_OR = []
POWERKEYS = []
ENCODERKEYS = []
BREAKKEYS = []


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    if match:
        return value.split('-')[1]  # 返回第二个部分
    return ''  # 如果不匹配，返回空字符串


# 读取并清理投料信息Excel文件
def read_and_clean_bom_file(file_path):
    df = pd.read_excel(file_path)
    df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')

    # 第一层筛选 - 只处理伺服电缆
    servo_cable_mask = df['物料描述'].str.contains(
        r'伺服(动力|编码器|制动|刹车)(柔性|标准|普通屏蔽|普通|柔性屏蔽|柔性普通)?(电缆|线缆)_',
        na=False,
        regex=True,
    ) | df['物料描述'].str.contains(
        r'(步进伺服|直线电机)(动力|编码器|电机)(电缆|线缆|延长线)_', na=False, regex=True
    )

    # 只保留伺服电缆的行并处理
    servo_df = df[servo_cable_mask].copy()

    # 为伺服电缆添加必要的列
    servo_df['电缆物料描述'] = servo_df['物料描述']
    servo_df['提取结果'] = None
    servo_df['已处理'] = False  # 标记是否已被某个模式处理

    # 新的简化处理逻辑：直接提取"中文_"之后括号之前的型号
    def extract_cable_model_simple(description):
        """
        提取电缆型号，格式：中文_型号(规格信息)
        返回型号部分，如：S6-L-M022-7.0
        """
        if pd.isna(description):
            return None

        # 匹配模式：中文_型号(...)
        pattern = r'.*?_([A-Za-z0-9.-]+)(?:\(|$)'
        match = re.search(pattern, str(description))
        if match:
            return match.group(1)
        return None

    # 应用简化提取逻辑
    servo_df['简化型号'] = servo_df['物料描述'].apply(extract_cable_model_simple)

    # 过滤掉无法提取型号的记录
    servo_df = servo_df[servo_df['简化型号'].notna()]

    # 使用简化型号作为物料描述
    servo_df['物料描述'] = servo_df['简化型号']

    # 标记所有记录为已处理（因为我们使用了简化的提取逻辑）
    servo_df['已处理'] = True

    # 只处理外购件
    df_bom = servo_df[servo_df['物料类型'] == '外购件']

    # 提取部件编号
    df_bom['部件编号'] = df_bom['所属部件'].apply(extract_second_part)

    # 整理最终结果
    df_bom = df_bom.sort_values(by='部件编号').reset_index(drop=True)[
        ['物料描述', '部件编号', '总数量', '电缆物料描述']
    ]

    # 处理柔性电缆加上 "-H" 后缀
    df_bom['物料描述'] = df_bom.apply(
        lambda x: f"{x['物料描述']}-H" if '柔' in x['电缆物料描述'] else x['物料描述'], axis=1
    )

    # 合并重复的物料描述和部件编号
    df_bom_grouped = df_bom.groupby(['物料描述', '部件编号'], as_index=False).agg(
        {'总数量': 'sum'}
    )

    return df_bom_grouped


def merge_db_and_cable_devices(df_db, cable_devices_df, output_path):
    """
    将机电沟通表与电缆设备数据进行匹配
    根据EM编号进行匹配
    """
    # 使用EM名称进行匹配
    # 与电缆设备数据进行左连接
    df = pd.merge(df_db, cable_devices_df, left_on='EM名称', right_on='EM编号', how='left', suffixes=('', '_cable'))

    # 处理柔性电缆（根据是否过拖链决定是否添加-H后缀）
    def add_flexible_suffix(cable_desc, is_flexible):
        if pd.isna(cable_desc) or cable_desc == '自制':
            return cable_desc
        if is_flexible and str(cable_desc) != 'nan':
            return f"{cable_desc}-H"
        return cable_desc

    df['动力线物料描述'] = df.apply(
        lambda x: add_flexible_suffix(x['动力线物料描述'], x['是否过拖链']),
        axis=1,
    )

    df['编码器线物料描述'] = df.apply(
        lambda x: add_flexible_suffix(x['编码器线物料描述'], x['是否过拖链']),
        axis=1,
    )

    # 刹车线处理（新数据中暂无刹车线信息）
    df['刹车线物料描述'] = df.apply(
        lambda x: add_flexible_suffix(x['刹车线物料描述'], x['是否过拖链']),
        axis=1,
    )

    # 记录缺失的电缆信息
    with open(os.path.join(output_path, 'cable_motor_missing.txt'), 'w') as f:
        f.write(f'Missing cables: ')
        na_list = df[df['动力线物料描述'].isna() | df['编码器线物料描述'].isna()][
            '伺服电机物料描述'
        ].unique()
        f.write(','.join(na_list))
        f.write('\n')

    # 删除临时列
    df = df.drop(['EM编号'], axis=1, errors='ignore')

    return df


# 读取并处理电缆设备CSV文件
def read_and_clean_cable_devices(file_path, df=None):
    """
    读取电缆设备CSV文件，根据EM和功能描述整理电缆数据
    每个伺服对应动力线和编码器线
    """
    if df is None:
        df = pd.read_csv(file_path)

    # 提取EM编号（从device_identifier中提取）
    df['EM编号'] = df['device_identifier'].str.extract(r'(EM\w+)')

    # 从model_number中提取型号（去掉长度信息）
    def extract_cable_model(model_number):
        if pd.isna(model_number):
            return None
        # 提取型号，去掉长度部分（如 S6-L-M022-7.0 -> S6-L-M022）
        match = re.match(r'([A-Za-z0-9-]+)-[\d.]+', str(model_number))
        if match:
            return match.group(1)
        return model_number

    df['电缆型号'] = df['model_number'].apply(extract_cable_model)

    # 判断电缆类型（动力线或编码器线）
    df['电缆类型'] = df['function_text'].apply(
        lambda x: '动力线' if '动力电缆' in str(x) else ('编码器线' if '编码器电缆' in str(x) else '其他')
    )

    # 过滤掉非动力线和编码器线的记录
    df = df[df['电缆类型'].isin(['动力线', '编码器线'])]

    # 按EM编号分组，为每个EM创建动力线和编码器线的映射
    result_data = []

    for em_num, group in df.groupby('EM编号'):
        if pd.isna(em_num):
            continue

        # 获取功能描述（取第一个记录的功能描述）
        function_desc = group['function_text'].iloc[0]

        # 分别获取动力线和编码器线
        power_cables = group[group['电缆类型'] == '动力线']
        encoder_cables = group[group['电缆类型'] == '编码器线']

        # 创建记录
        record = {
            'EM编号': em_num,
            '功能描述': function_desc,
            '动力线物料描述': power_cables['电缆型号'].iloc[0] if not power_cables.empty else None,
            '编码器线物料描述': encoder_cables['电缆型号'].iloc[0] if not encoder_cables.empty else None,
            '刹车线物料描述': None  # 新数据中暂无刹车线信息
        }
        result_data.append(record)

    result_df = pd.DataFrame(result_data)

    # 更新全局变量
    global POWERKEYS
    global ENCODERKEYS
    global BREAKKEYS

    POWERKEYS = list(result_df['动力线物料描述'].dropna().unique())
    ENCODERKEYS = list(result_df['编码器线物料描述'].dropna().unique())
    BREAKKEYS = []  # 新数据中暂无刹车线

    return result_df


# 读取机电沟通Excel文件
def read_and_clean_db_file(
    file_path, cable_devices_df=None, output_path='tests', code_mapping=None
):
    """
    读取机电沟通Excel文件，并与电缆设备数据进行匹配
    """
    df = pd.read_excel(file_path)
    df['部件编号'] = df['部件编号'].fillna('')

    # 过滤掉蛇形相关的记录
    df = df[~df['EM中文'].str.contains('蛇形', na=False)]

    # 只处理伺服电机
    df_db = df[df['类别'] == '伺服电机'][
        ['型号', '部件编号', '是否过拖链', 'EM中文', '功能描述', 'EM名称']
    ].rename(columns={'型号': '物料描述'})

    # 转换数据类型并排序
    df_db['部件编号'] = df_db['部件编号'].apply(
        lambda x: str(int(x)) if isinstance(x, float) else str(x)
    )
    df_db['部件编号'] = df_db['部件编号'].apply(lambda x: x.zfill(2))

    # 使用映射替换两位编码为三位编码
    if code_mapping is not None:
        df_db['部件编号'] = df_db['部件编号'].apply(
            lambda x: code_mapping.get(x, x)
        )

    # 清理物料描述
    df_db['物料描述'] = (
        df_db['物料描述']
        .str.replace(f'{JIDIAN_PREFIX}_?', '', regex=True)
        .str.replace(r'-XD.*', '', regex=True)
    )

    df_db['是否过拖链'] = df_db['是否过拖链'].apply(
        lambda x: True if x == '是' else False
    )
    df_db['伺服电机物料描述'] = df_db['物料描述']

    # 与电缆设备数据进行匹配
    if cable_devices_df is not None:
        df_db = merge_db_and_cable_devices(df_db, cable_devices_df, output_path)
    else:
        # 如果没有电缆设备数据，设置默认值
        df_db['动力线物料描述'] = None
        df_db['编码器线物料描述'] = None
        df_db['刹车线物料描述'] = None

    return df_db[
        ['伺服电机物料描述', '动力线物料描述', '编码器线物料描述', '刹车线物料描述', '部件编号', 'EM中文', '功能描述']
    ]


# 格式化部件号
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


# 比较两者的部件号和数量差异
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


def highlight_row(row):
    # 如果原表无值，标记为红色；BOM表无值，标记为黄色
    if row['异常'] == 1:
        return ['background-color: red'] * len(row)
    return [''] * len(row)


def merge_df(df_bom, df_db):
    result_df = pd.DataFrame(
        columns=[
            '伺服电机物料描述',
            '部件编号',
            'EM中文',
            '功能描述',
            '动力线物料描述',
            '编码器线物料描述',
            '刹车线物料描述',
            'BOM表动力线物料描述',
            'BOM表编码器线物料描述',
            'BOM表刹车线物料描述',
        ]
    )

    # Step 1: 创建唯一键“物料描述-部件编号”用于匹配
    df_bom['物料描述-部件编号'] = df_bom['物料描述'] + '-' + df_bom['部件编号'].astype(str)
    df_db['编码器线物料描述-部件编号'] = (
        df_db['编码器线物料描述'] + '-' + df_db['部件编号'].astype(str)
    )
    df_db['动力线物料描述-部件编号'] = df_db['动力线物料描述'] + '-' + df_db['部件编号'].astype(str)
    df_db['刹车线物料描述-部件编号'] = (
        df_db['刹车线物料描述'].fillna('') + '-' + df_db['部件编号'].astype(str)
    )

    # Step 2: 遍历数据库数据，根据动力线和编码器线唯一键检查 BOM 表
    for _, db_row in df_db.iterrows():
        power_key = db_row['动力线物料描述-部件编号']
        encoder_key = db_row['编码器线物料描述-部件编号']
        break_key = db_row['刹车线物料描述-部件编号']

        bom_power_match = df_bom[df_bom['物料描述-部件编号'] == power_key]
        bom_encoder_match = df_bom[df_bom['物料描述-部件编号'] == encoder_key]
        bom_break_match = None
        if db_row['刹车线物料描述']:
            bom_break_match = df_bom[df_bom['物料描述-部件编号'] == break_key]

        if (
            not bom_power_match.empty
            or not bom_encoder_match.empty
            or (db_row['刹车线物料描述'] and not bom_break_match.empty)
        ):
            # 初始化动力线和编码器线BOM描述
            bom_power_description = None
            bom_encoder_description = None
            bom_break_description = None

            # 根据动力线匹配
            if not bom_power_match.empty:
                for _, bom_row in bom_power_match.iterrows():
                    if bom_row['总数量'] > 0:
                        bom_power_description = bom_row['物料描述']
                        # 减少 BOM 中的总数量
                        df_bom.loc[
                            df_bom['物料描述-部件编号'] == power_key, '总数量'
                        ] -= 1
                        break

            # 根据编码器线匹配
            if not bom_encoder_match.empty:
                for _, bom_row in bom_encoder_match.iterrows():
                    if bom_row['总数量'] > 0:
                        bom_encoder_description = bom_row['物料描述']
                        # 减少 BOM 中的总数量
                        df_bom.loc[
                            df_bom['物料描述-部件编号'] == encoder_key, '总数量'
                        ] -= 1
                        break

            if db_row['刹车线物料描述'] and not bom_break_match.empty:
                for _, bom_row in bom_break_match.iterrows():
                    if bom_row['总数量'] > 0:
                        bom_break_description = bom_row['物料描述']
                        # 减少 BOM 中的总数量
                        df_bom.loc[
                            df_bom['物料描述-部件编号'] == break_key, '总数量'
                        ] -= 1
                        break

            # 将匹配的动力线和编码器线写入同一行
            result_df = pd.concat(
                [
                    result_df,
                    pd.DataFrame(
                        [
                            {
                                '伺服电机物料描述': db_row['伺服电机物料描述'],
                                '部件编号': db_row['部件编号'],
                                'EM中文': db_row['EM中文'],
                                '功能描述': db_row['功能描述'],
                                '动力线物料描述': db_row['动力线物料描述'],
                                '编码器线物料描述': db_row['编码器线物料描述'],
                                '刹车线物料描述': db_row['刹车线物料描述'],
                                'BOM表动力线物料描述': bom_power_description,
                                'BOM表编码器线物料描述': bom_encoder_description,
                                'BOM表刹车线物料描述': bom_break_description,
                            }
                        ]
                    ),
                ]
            )
        else:
            # 如果 BOM 中没有对应记录，直接将 DB 中的记录插入结果
            result_df = pd.concat(
                [
                    result_df,
                    pd.DataFrame(
                        [
                            {
                                '伺服电机物料描述': db_row['伺服电机物料描述'],
                                '部件编号': db_row['部件编号'],
                                'EM中文': db_row['EM中文'],
                                '功能描述': db_row['功能描述'],
                                '动力线物料描述': db_row['动力线物料描述'],
                                '编码器线物料描述': db_row['编码器线物料描述'],
                                '刹车线物料描述': db_row['刹车线物料描述'],
                                'BOM表动力线物料描述': None,
                                'BOM表编码器线物料描述': None,
                                'BOM表刹车线物料描述': None,
                            }
                        ]
                    ),
                ]
            )
    # Step 3: 遍历 BOM 表，添加 DB 表中不存在的 BOM 记录
    for _, bom_row in df_bom.iterrows():
        row_data = bom_row['物料描述']
        if bom_row['物料描述'].endswith('-H'):
            row_data = bom_row['物料描述'][:-2]
        if bom_row['总数量'] > 0:
            for _ in range(bom_row['总数量']):

                if row_data in POWERKEYS:
                    bom_power_description = bom_row['物料描述']
                    bom_encoder_description = None
                    bom_break_description = None
                elif row_data in BREAKKEYS:
                    bom_power_description = None
                    bom_encoder_description = None
                    bom_break_description = bom_row['物料描述']
                elif row_data in ENCODERKEYS:
                    bom_power_description = None
                    bom_encoder_description = bom_row['物料描述']
                    bom_break_description = None
                else:
                    bom_power_description = None
                    bom_encoder_description = bom_row['物料描述']  # 默认分配到编码器线
                    bom_break_description = None
                result_df = pd.concat(
                    [
                        result_df,
                        pd.DataFrame(
                            [
                                {
                                    '伺服电机物料描述': None,
                                    '部件编号': bom_row['部件编号'],
                                    'EM中文': None,
                                    '功能描述': None,
                                    '动力线物料描述': None,
                                    '编码器线物料描述': None,
                                    '刹车线物料描述': None,
                                    'BOM表动力线物料描述': bom_power_description,
                                    'BOM表编码器线物料描述': bom_encoder_description,
                                    'BOM表刹车线物料描述': bom_break_description,
                                }
                            ]
                        ),
                    ]
                )

    # Reset index after concatenation
    result_df.reset_index(drop=True, inplace=True)
    result_df = result_df.replace({None: ''})

    # 生成"异常"列：如果任一物料描述或BOM表物料描述为空，则标记为1，否则为0
    result_df['异常'] = result_df.apply(
        lambda row: 1
        if row['动力线物料描述'] == ''
        or row['编码器线物料描述'] == ''
        or row['BOM表动力线物料描述'] == ''
        or row['BOM表编码器线物料描述'] == ''
        else 0,
        axis=1,
    )

    # 刹车线异常检测：都没有或都有就正常，一个有一个没有就异常
    result_df['异常'] = result_df.apply(
        lambda row: 1
        if (
            # 刹车线异常：一个有一个没有的情况
            (
                # 情况1：机电沟通表有刹车线，但BOM表没有
                (row['刹车线物料描述'] != '' and not pd.isna(row['刹车线物料描述']))
                and (pd.isna(row['BOM表刹车线物料描述']) or row['BOM表刹车线物料描述'] == '')
            )
            or (
                # 情况2：机电沟通表没有刹车线，但BOM表有
                (row['刹车线物料描述'] == '' or pd.isna(row['刹车线物料描述']))
                and (
                    not pd.isna(row['BOM表刹车线物料描述'])
                    and row['BOM表刹车线物料描述'] != ''
                )
            )
        )
        or row['异常'] == 1
        else 0,
        axis=1,
    )

    # 应用样式并返回
    styled_df = result_df.style.apply(highlight_row, axis=1)
    return styled_df


# 主函数，执行所有流程
def main(ERP):
    # 读取和处理 BOM 文件
    df_bom = read_and_clean_bom_file(
        '/home/<USER>/code/bom-analysis/tests/38147/38147-B0723.xlsx'
    )

    # 读取和处理机电沟通表
    df_db = read_and_clean_db_file(
        '/home/<USER>/code/bom-analysis/tests/38147/erp_mech_info_202507240932.xlsx'
    )

    styled_df = merge_df(df_bom, df_db)

    styled_df.to_excel(f'tests/{ERP}电缆投料信息汇总.xlsx', index=False)


def run(bom_file, db_file, cable_devices_df, output_path, code_mapping=None):
    """
    主运行函数
    参数:
    - bom_file: BOM文件路径
    - db_file: 机电沟通表文件路径
    - cable_devices_df: 电缆设备数据DataFrame
    - output_path: 输出路径
    - code_mapping: 编码映射
    """
    # 读取和处理 BOM 文件
    df_bom = read_and_clean_bom_file(bom_file)

    # 读取和处理机电沟通表
    df_db = read_and_clean_db_file(
        db_file, cable_devices_df, output_path, code_mapping
    )

    styled_df = merge_df(df_bom, df_db)

    styled_df.to_excel(f'{output_path}/3电缆比对_BOM_机电沟通表.xlsx', index=False)
    return ({'type': 'cable', 'path': f'{output_path}/3电缆比对_BOM_机电沟通表.xlsx'},)


def test_new_data_structure():
    """
    测试新的数据结构和比对方法
    """
    # 文件路径
    bom_file = '/home/<USER>/code/bom-analysis/tests/37979/2_37979-b0710.xlsx'
    db_file = '/home/<USER>/code/bom-analysis/tests/37979/mech_info_37979.xlsx'
    cable_devices_file = '/home/<USER>/code/bom-analysis/tests/37979/erp_cable_devices_202507241658.csv'
    output_path = '/home/<USER>/code/bom-analysis/tests/37979'

    print("开始测试新的数据结构...")

    # 读取电缆设备数据
    print("1. 读取电缆设备数据...")
    cable_devices_df = read_and_clean_cable_devices(cable_devices_file)
    print(f"电缆设备数据行数: {len(cable_devices_df)}")
    print("电缆设备数据示例:")
    print(cable_devices_df.head())

    # 运行比对
    print("\n2. 运行比对...")
    result = run(bom_file, db_file, cable_devices_df, output_path)

    print(f"\n3. 比对完成，结果保存到: {result[0]['path']}")
    return result

if __name__ == '__main__':
    test_new_data_structure()

#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import logging
from logging.handlers import RotatingFileHandler
import requests
from datetime import datetime, timedelta
import json
from typing import List, Dict, Optional
from sqlalchemy import (
    create_engine,
    Column,
    Integer,
    String,
    Text,
    DateTime,
    Float,
    ForeignKey,
    text,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler(
            'ecr_sync.log',
            maxBytes=1024 * 1024,  # 1MB 大小限制
            backupCount=5,  # 保留5个备份文件
        ),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger('ecr_sync')

# 数据库配置
DATABASE_URL = 'mysql+pymysql://root:leadchina@*************:3306/IO'

# API配置
API_BASE_URL = (
    'https://esb-new.leadchina.cn/api/ipeas/leadchina/api_chn_0000_lead_0002'
)

# 创建SQLAlchemy Base类
Base = declarative_base()


# 定义数据模型
class ECRHistoryMain(Base):
    """ECR历史主表"""

    __tablename__ = 'ecr_history_main'

    id = Column(Integer, primary_key=True, autoincrement=True)
    erp_code = Column(String(20), nullable=False, index=True, comment='ERP编号')
    ecr_no = Column(String(50), nullable=False, index=True, comment='ECR申请单号')
    creator_id = Column(String(50), comment='创建者ID')
    creator_name = Column(String(50), comment='创建者姓名')
    create_time = Column(DateTime, comment='创建时间')
    reviewer_id = Column(String(50), comment='审核用户ID')
    reviewer_name = Column(String(50), comment='审核用户姓名')
    review_status = Column(String(20), comment='审核状态')
    change_reason = Column(Text, comment='变更原因')
    sync_time = Column(DateTime, comment='同步时间')

    # 定义与明细表的关系
    details = relationship(
        'ECRHistoryDetail', back_populates='main', cascade='all, delete-orphan'
    )

    def __repr__(self):
        return f"<ECRHistoryMain(ecr_no='{self.ecr_no}', erp_code='{self.erp_code}')>"


class ECRHistoryDetail(Base):
    """ECR历史明细表"""

    __tablename__ = 'ecr_history_detail'

    id = Column(Integer, primary_key=True, autoincrement=True)
    main_id = Column(
        Integer,
        ForeignKey('ecr_history_main.id', ondelete='CASCADE'),
        nullable=False,
        index=True,
        comment='主表ID',
    )
    material_code = Column(String(50), index=True, comment='物料编码')
    drawing_code = Column(String(50), comment='图号')
    material_desc = Column(String(200), comment='物料名称')
    diff_quantity = Column(Float, comment='差异数量')
    implement_content = Column(Text, comment='实施内容')
    diff_category = Column(String(50), comment='差异类别')
    whole_device_code = Column(String(50), comment='整机编码')
    whole_device_drawing = Column(String(50), comment='整机图号')
    top_level_code = Column(String(50), comment='顶层编码')
    parent_material_code = Column(String(50), comment='父级物料编码')

    # 定义与主表的关系
    main = relationship('ECRHistoryMain', back_populates='details')

    def __repr__(self):
        return f"<ECRHistoryDetail(material_code='{self.material_code}')>"


class DBOperation:
    """数据库操作类"""

    def __init__(self, db_url=DATABASE_URL):
        self.engine = create_engine(db_url)
        self.SessionLocal = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine
        )
        self.session = None

    def create_tables(self):
        """创建数据表（如果不存在）"""
        Base.metadata.create_all(self.engine)
        logger.info('已确保所有必要的数据表存在')

    def get_session(self):
        """获取数据库会话"""
        if self.session is None:
            self.session = self.SessionLocal()
        return self.session

    def close_session(self):
        """关闭数据库会话"""
        if self.session:
            self.session.close()
            self.session = None

    def get_active_erp_projects(self) -> List[str]:
        """
        获取所有需要同步的活跃ERP项目

        Returns:
            ERP编号列表
        """
        session = self.get_session()
        try:
            query = text(
                """
                SELECT
                    DISTINCT ERP
                FROM erp_base_info
                WHERE mech_time_end IS NOT NULL
                  AND DATEDIFF(NOW(), mech_time_end) <= 180
                """
            )

            result = session.execute(query)
            erp_numbers = [row[0] for row in result.fetchall()]

            logger.info(f'找到{len(erp_numbers)}个活跃ERP项目')
            return erp_numbers

        except Exception as e:
            logger.error(f'获取活跃ERP项目时出错: {str(e)}')
            return []

    def check_ecr_record_exists(self, ecr_no: str) -> Optional[ECRHistoryMain]:
        """
        检查ECR记录是否已存在

        Args:
            ecr_no: ECR申请单号

        Returns:
            如果存在返回记录对象，否则返回None
        """
        session = self.get_session()
        try:
            return (
                session.query(ECRHistoryMain)
                .filter(ECRHistoryMain.ecr_no == ecr_no)
                .first()
            )
        except Exception as e:
            logger.error(f'检查ECR记录时出错: {str(e)}')
            return None

    def save_ecr_record(
        self, erp_code: str, record_data: Dict
    ) -> Optional[ECRHistoryMain]:
        """
        保存ECR主记录

        Args:
            erp_code: ERP编号
            record_data: ECR记录数据

        Returns:
            成功时返回ECR记录对象，失败时返回None
        """
        session = self.get_session()
        try:
            # 创建新记录
            create_time = record_data.get('createTime')
            if create_time and isinstance(create_time, str):
                # 尝试解析日期时间字符串
                try:
                    create_time = datetime.strptime(
                        create_time, '%Y-%m-%d %H:%M:%S'
                    )
                except ValueError:
                    try:
                        create_time = datetime.strptime(
                            create_time, '%Y-%m-%d'
                        )
                    except ValueError:
                        create_time = None

            main_record = ECRHistoryMain(
                erp_code=erp_code,
                ecr_no=record_data.get('ecrNo', ''),
                creator_id=record_data.get('creatorId', ''),
                creator_name=record_data.get('creatorName', ''),
                create_time=create_time,
                reviewer_id=record_data.get('reviewerId', ''),
                reviewer_name=record_data.get('reviewerName', ''),
                review_status=record_data.get('reviewStatus', ''),
                change_reason=record_data.get('changeReason', ''),
                sync_time=datetime.now(),
            )

            session.add(main_record)
            session.flush()  # 获取ID但不提交

            # 添加明细记录
            self.save_ecr_details(
                main_record.id, record_data.get('details', [])
            )

            session.commit()
            return main_record

        except Exception as e:
            logger.error(f'保存ECR记录时出错: {str(e)}')
            session.rollback()
            return None

    def update_ecr_record(
        self, record: ECRHistoryMain, record_data: Dict
    ) -> bool:
        """
        更新ECR主记录和明细

        Args:
            record: 现有ECR记录对象
            record_data: 新的ECR记录数据

        Returns:
            更新成功返回True，否则返回False
        """
        session = self.get_session()
        try:
            # 解析创建时间
            create_time = record_data.get('createTime')
            if create_time and isinstance(create_time, str):
                try:
                    create_time = datetime.strptime(
                        create_time, '%Y-%m-%d %H:%M:%S'
                    )
                except ValueError:
                    try:
                        create_time = datetime.strptime(
                            create_time, '%Y-%m-%d'
                        )
                    except ValueError:
                        create_time = None

            # 更新主记录
            record.creator_id = record_data.get('creatorId', record.creator_id)
            record.creator_name = record_data.get(
                'creatorName', record.creator_name
            )
            if create_time:
                record.create_time = create_time
            record.reviewer_id = record_data.get(
                'reviewerId', record.reviewer_id
            )
            record.reviewer_name = record_data.get(
                'reviewerName', record.reviewer_name
            )
            record.review_status = record_data.get(
                'reviewStatus', record.review_status
            )
            record.change_reason = record_data.get(
                'changeReason', record.change_reason
            )
            record.sync_time = datetime.now()

            # 删除旧明细记录
            session.query(ECRHistoryDetail).filter(
                ECRHistoryDetail.main_id == record.id
            ).delete()

            # 添加新明细记录
            self.save_ecr_details(record.id, record_data.get('details', []))

            session.commit()
            return True

        except Exception as e:
            logger.error(f'更新ECR记录时出错: {str(e)}')
            session.rollback()
            return False

    def save_ecr_details(self, main_id: int, details: List[Dict]) -> bool:
        """
        保存ECR明细记录

        Args:
            main_id: ECR主记录ID
            details: ECR明细数据列表

        Returns:
            保存成功返回True，否则返回False
        """
        session = self.get_session()
        try:
            if not details:
                return True

            for detail in details:
                diff_quantity = detail.get('diffQuantity')
                # 确保数值类型正确
                if diff_quantity is not None:
                    try:
                        diff_quantity = float(diff_quantity)
                    except (ValueError, TypeError):
                        diff_quantity = 0.0

                detail_record = ECRHistoryDetail(
                    main_id=main_id,
                    material_code=detail.get('materialCode', ''),
                    drawing_code=detail.get('drawingCode', ''),
                    material_desc=detail.get('materialDesc', ''),
                    diff_quantity=diff_quantity,
                    implement_content=detail.get('implementContent', ''),
                    diff_category=detail.get('diffCategory', ''),
                    whole_device_code=detail.get('wholeDeviceCode', ''),
                    whole_device_drawing=detail.get('wholeDeviceDrawing', ''),
                    top_level_code=detail.get('topLevelCode', ''),
                    parent_material_code=detail.get('parentMaterialCode', ''),
                )

                session.add(detail_record)

            return True

        except Exception as e:
            logger.error(f'保存ECR明细记录时出错: {str(e)}')
            return False


class ECRApiClient:
    """ECR API客户端"""

    def __init__(self, api_url=API_BASE_URL):
        self.api_url = api_url

    def fetch_ecr_history(
        self,
        erp_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        page_size: int = 100,
        page_num: int = 1,
    ) -> Optional[Dict]:
        """
        获取指定ERP的ECR历史变更数据

        Args:
            erp_code: ERP编号
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            page_size: 每页记录数
            page_num: 页码

        Returns:
            API响应数据，失败时返回None
        """
        try:
            # 准备请求参数
            payload = {
                'PageID': '查询ECR申核信息',
                'Guid': '34ef9b88-987e-4eb6-83fd-040ff752b0d7',
                'Method': 'ECRHistoryQuery',
                'data': {
                    'erpCode': erp_code,
                    'pageSize': page_size,
                    'pageNum': page_num,
                },
            }

            # 添加可选参数
            if start_date:
                payload['data']['startDate'] = start_date
            if end_date:
                payload['data']['endDate'] = end_date

            # 发送API请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(
                self.api_url, json=payload, headers=headers
            )

            # 调试输出
            logger.debug(f'API请求: {json.dumps(payload)}')

            # 检查响应
            if response.status_code == 200:
                result = response.json()
                logger.debug(f'API响应: {json.dumps(result)}')

                if result.get('code') == 0:  # 成功状态码
                    logger.info(f'成功获取ERP {erp_code}的ECR历史数据')
                    return result
                else:
                    logger.warning(f'API返回错误: {result.get("message")}')
            else:
                logger.error(f'API请求失败，状态码: {response.status_code}')

            return None
        except Exception as e:
            logger.error(f'获取ECR历史数据时出错: {str(e)}')
            return None


class ECRSyncService:
    """ECR同步服务"""

    def __init__(self, db_op: DBOperation, api_client: ECRApiClient):
        self.db_op = db_op
        self.api_client = api_client

    def sync_all_erps(self):
        """同步所有活跃ERP项目的ECR历史数据"""
        logger.info('开始同步ECR历史数据')

        try:
            # 确保数据表存在
            self.db_op.create_tables()

            # 获取活跃ERP项目
            erp_projects = self.db_op.get_active_erp_projects()

            if not erp_projects:
                logger.warning('未找到需要同步的活跃ERP项目')
                return

            success_count = 0
            # 同步每个ERP项目的ECR历史
            for erp_code in erp_projects:
                try:
                    # 获取当前日期前180天的数据
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    start_date = (
                        datetime.now() - timedelta(days=180)
                    ).strftime('%Y-%m-%d')

                    # 分页获取数据，每页100条
                    page_num = 1
                    total_pages = 1

                    while page_num <= total_pages:
                        ecr_data = self.api_client.fetch_ecr_history(
                            erp_code, start_date, end_date, 100, page_num
                        )

                        if ecr_data and 'data' in ecr_data:
                            # 更新总页数
                            total_records = ecr_data['data'].get('total', 0)
                            total_pages = (total_records + 99) // 100  # 向上取整

                            # 保存数据
                            if self.save_ecr_data(erp_code, ecr_data):
                                success_count += 1

                        page_num += 1

                    logger.info(f'完成ERP {erp_code}的ECR历史同步')

                except Exception as e:
                    logger.error(f'同步ERP {erp_code}的ECR历史时出错: {str(e)}')

            logger.info(
                f'ECR历史同步完成，成功同步{success_count}/{len(erp_projects)}个ERP项目'
            )

        except Exception as e:
            logger.error(f'ECR历史同步过程中出错: {str(e)}')

    def save_ecr_data(self, erp_code: str, ecr_data: Dict) -> bool:
        """
        保存ECR历史数据

        Args:
            erp_code: ERP编号
            ecr_data: ECR历史数据

        Returns:
            保存成功返回True，否则返回False
        """
        try:
            if (
                not ecr_data
                or 'data' not in ecr_data
                or 'list' not in ecr_data['data']
            ):
                logger.warning('ECR数据格式不正确，无法保存')
                return False

            ecr_records = ecr_data['data']['list']
            if not ecr_records:
                logger.info(f'ERP {erp_code}没有ECR历史记录')
                return True

            # 保存记录
            saved_count = 0
            for record in ecr_records:
                # 检查记录是否已存在
                existing_record = self.db_op.check_ecr_record_exists(
                    record.get('ecrNo', '')
                )

                if existing_record:
                    # 更新现有记录
                    if self.db_op.update_ecr_record(existing_record, record):
                        logger.debug(f'更新了ECR记录: {record.get("ecrNo")}')
                        saved_count += 1
                else:
                    # 插入新记录
                    if self.db_op.save_ecr_record(erp_code, record):
                        saved_count += 1

            logger.info(
                f'为ERP {erp_code}保存了{saved_count}/{len(ecr_records)}条ECR记录'
            )
            return saved_count > 0

        except Exception as e:
            logger.error(f'保存ECR历史数据时出错: {str(e)}')
            return False


def run_ecr_sync():
    """运行ECR同步主函数"""
    try:
        # 创建数据库操作对象
        db_op = DBOperation()

        # 创建API客户端
        api_client = ECRApiClient()

        # 创建同步服务
        sync_service = ECRSyncService(db_op, api_client)

        # 执行同步
        sync_service.sync_all_erps()

    except Exception as e:
        logger.error(f'ECR同步过程中出错: {str(e)}')
    finally:
        # 关闭数据库连接
        if 'db_op' in locals():
            db_op.close_session()


if __name__ == '__main__':
    logger.info('======== 开始ECR历史数据同步 ========')
    run_ecr_sync()
    logger.info('======== ECR历史数据同步完成 ========')

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL单次备份脚本 - 简化版
备份逻辑（考虑8小时时差，延后一天逻辑）：
1. 检查erp_base_info表的bom_time，判断是否需要备份
2. 如果需要，则备份那些关联到特定bom_time的ERP对应的BOM数据
3. 延后一天逻辑：需要的是当前的后一天 + -7 天的数据
适用于cronjob调度，每天早上6点运行
"""

import pymysql
import logging
import sys
import traceback
from datetime import datetime, timedelta
from typing import Optional

# ===========================================
# 配置区域 - 请根据实际情况修改以下配置
# ===========================================

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'root',  # 请修改为实际用户名
    'password': 'leadchina',  # 请修改为实际密码
    'database': 'IO',  # 请修改为实际数据库名
    'charset': 'utf8mb4',
}

# 表名配置
TABLES = {
    'erp_base_info': 'erp_base_info',
    'bom_table': 'bom_analysis_daily_parts_error_counts',
    'backup_table': 'bom_analysis_daily_parts_error_counts_backup',
}

# 其他配置
AVOID_DUPLICATES = True  # 是否避免重复备份
LOG_LEVEL = 'INFO'  # 日志级别：DEBUG, INFO, WARNING, ERROR
LOG_FILE = 'backup.log'  # 日志文件名

# ===========================================
# 备份脚本主体代码
# ===========================================


class MySQLBackupManager:
    def __init__(self):
        """初始化备份管理器"""
        self.setup_logging()
        self.connection = None

    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, LOG_LEVEL)

        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # 文件处理器
        file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
        file_handler.setFormatter(formatter)

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)

        # 配置logger
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # 避免重复日志
        self.logger.propagate = False

    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=DB_CONFIG['host'],
                port=DB_CONFIG['port'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                database=DB_CONFIG['database'],
                charset=DB_CONFIG['charset'],
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=False,
            )
            self.logger.info('数据库连接成功')
            return True

        except Exception as e:
            self.logger.error(f'数据库连接失败: {e}')
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.logger.info('数据库连接已关闭')

    def create_backup_table(self) -> bool:
        """创建备份表（如果不存在）"""
        try:
            source_table = TABLES['bom_table']
            backup_table = TABLES['backup_table']

            with self.connection.cursor() as cursor:
                # 检查备份表是否存在
                cursor.execute(
                    f"""
                    SELECT COUNT(*) as count FROM information_schema.tables
                    WHERE table_schema = '{DB_CONFIG['database']}'
                    AND table_name = '{backup_table}'
                """
                )

                result = cursor.fetchone()
                if result['count'] == 0:
                    # 创建备份表
                    cursor.execute(
                        f'CREATE TABLE {backup_table} LIKE {source_table}'
                    )
                    self.connection.commit()
                    self.logger.info(f'备份表 {backup_table} 创建成功')
                else:
                    self.logger.info(f'备份表 {backup_table} 已存在')

            return True

        except Exception as e:
            self.logger.error(f'创建备份表失败: {e}')
            if self.connection:
                self.connection.rollback()
            return False

    def backup_data(self) -> bool:
        """执行数据备份"""
        try:
            erp_table = TABLES['erp_base_info']
            bom_table = TABLES['bom_table']
            backup_table = TABLES['backup_table']

            current_date = datetime.now().date()
            yesterday_date = current_date - timedelta(
                days=1
            )  # 因为8小时时差，要备份前一天的数据

            self.logger.info(f'当前日期: {current_date}')
            self.logger.info(f'检查是否需要备份与 {yesterday_date} 相关的ERP对应的BOM数据')

            with self.connection.cursor() as cursor:
                # 首先检查是否需要备份：基于erp表的bom_time判断（延后一天逻辑）
                check_sql = f"""
                    SELECT COUNT(*) as need_backup
                    FROM {erp_table} erp
                    WHERE (
                        DATE_ADD(erp.bom_time, INTERVAL 1 DAY) = '{yesterday_date}'
                        OR
                        DATE_SUB(DATE_ADD(erp.bom_time, INTERVAL 1 DAY), INTERVAL 7 DAY) = '{yesterday_date}'
                    )
                """

                cursor.execute(check_sql)
                check_result = cursor.fetchone()

                if check_result['need_backup'] == 0:
                    self.logger.info(
                        f'根据erp表bom_time判断，{yesterday_date} 不需要执行备份'
                    )
                    self.log_backup_result(
                        0, 'SUCCESS', f'{yesterday_date} 无需备份'
                    )
                    return True

                self.logger.info(
                    f'检测到需要备份，开始备份与 {yesterday_date} 相关的ERP对应的BOM数据'
                )

                # 备份与指定日期相关的ERP对应的BOM数据（延后一天逻辑）
                backup_sql = f"""
                    SELECT bom.*
                    FROM {bom_table} bom
                    INNER JOIN {erp_table} erp ON bom.erp_number = erp.ERP
                    WHERE (
                        DATE_ADD(erp.bom_time, INTERVAL 1 DAY) = '{yesterday_date}'
                        OR
                        DATE_SUB(DATE_ADD(erp.bom_time, INTERVAL 1 DAY), INTERVAL 7 DAY) = '{yesterday_date}'
                    ) and bom.create_date = '{yesterday_date}'
                """

                # 如果需要避免重复备份
                if AVOID_DUPLICATES:
                    backup_sql += f"""
                        AND bom.id NOT IN (
                            SELECT id FROM {backup_table}
                        )
                    """

                self.logger.info(f'执行备份SQL: {backup_sql}')

                # 先查询要备份的数据
                cursor.execute(backup_sql)
                data_to_backup = cursor.fetchall()

                if not data_to_backup:
                    self.logger.info(
                        f'没有找到与 {yesterday_date} 相关的ERP对应的BOM数据需要备份'
                    )
                    self.log_backup_result(
                        0, 'SUCCESS', f'{yesterday_date} 无相关BOM数据需要备份'
                    )
                    return True

                self.logger.info(
                    f'找到 {len(data_to_backup)} 条与 {yesterday_date} 相关的BOM记录需要备份'
                )

                # 准备插入语句
                if data_to_backup:
                    # 获取字段名
                    columns = list(data_to_backup[0].keys())
                    columns_str = ', '.join(columns)
                    placeholders = ', '.join(['%s'] * len(columns))

                    insert_sql = f"""
                        INSERT INTO {backup_table} ({columns_str})
                        VALUES ({placeholders})
                    """

                    # 准备数据
                    insert_data = []
                    for row in data_to_backup:
                        insert_data.append([row[col] for col in columns])

                    # 批量插入
                    cursor.executemany(insert_sql, insert_data)
                    affected_rows = cursor.rowcount

                    self.connection.commit()

                    self.logger.info(
                        f'备份完成，成功备份 {affected_rows} 条与 {yesterday_date} 相关的BOM记录'
                    )

                    # 记录备份日志到数据库
                    self.log_backup_result(
                        affected_rows,
                        'SUCCESS',
                        f'备份{yesterday_date}相关BOM数据成功',
                    )

                    return True

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f'数据备份失败: {error_msg}')
            self.logger.error(f'错误详情: {traceback.format_exc()}')

            if self.connection:
                self.connection.rollback()

            # 记录失败日志
            self.log_backup_result(0, 'FAILED', error_msg)
            return False

    def log_backup_result(
        self, rows_backed_up: int, status: str, error_message: Optional[str]
    ):
        """记录备份结果到数据库"""
        try:
            backup_table = TABLES['backup_table']

            with self.connection.cursor() as cursor:
                # 创建日志表（如果不存在）
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS backup_log (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        backup_date DATETIME,
                        table_name VARCHAR(100),
                        rows_backed_up INT,
                        status VARCHAR(20),
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # 插入日志记录
                cursor.execute(
                    """
                    INSERT INTO backup_log
                    (backup_date, table_name, rows_backed_up, status, error_message)
                    VALUES (%s, %s, %s, %s, %s)
                """,
                    (
                        datetime.now(),
                        backup_table,
                        rows_backed_up,
                        status,
                        error_message,
                    ),
                )

                self.connection.commit()
                self.logger.info('备份日志已记录到数据库')

        except Exception as e:
            self.logger.error(f'记录备份日志失败: {e}')

    def run_backup(self) -> bool:
        """运行备份任务"""
        self.logger.info('=' * 50)
        self.logger.info('开始执行备份任务')

        if not self.connect_database():
            return False

        try:
            # 创建备份表
            if not self.create_backup_table():
                return False

            # 执行备份
            success = self.backup_data()

            if success:
                self.logger.info('备份任务执行成功')
            else:
                self.logger.error('备份任务执行失败')

            return success

        finally:
            self.close_connection()
            self.logger.info('备份任务结束')
            self.logger.info('=' * 50)


def main():
    """主函数"""
    try:
        # 执行备份
        backup_manager = MySQLBackupManager()
        success = backup_manager.run_backup()

        # 设置退出码
        sys.exit(0 if success else 1)

    except Exception as e:
        print(f'脚本执行出错: {e}')
        print(f'错误详情: {traceback.format_exc()}')
        sys.exit(1)


if __name__ == '__main__':
    main()

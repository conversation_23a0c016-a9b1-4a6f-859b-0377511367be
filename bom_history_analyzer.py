import pandas as pd
import re


def analyze_bom(
    bom_file: str,
    history_file: str,
    standards_df: pd.DataFrame,
    output_file: str,
    current_erp,
    history_erp,
) -> None:
    """
    Analyze BOM data against standard material requirements and export results to Excel.

    Args:
        bom_data: Raw BOM data string
        standard_data: Raw standard requirements data string
        machine_type: Type of machine to analyze
        output_file: Path to save the Excel output file
    """
    # Convert BOM string to DataFrame
    bom_df = pd.read_excel(bom_file)

    bom_df['总数量'] = pd.to_numeric(bom_df['总数量'], errors='coerce')
    bom_df['物料描述'] = bom_df['物料描述'].astype(str)
    history_bom_df = pd.read_excel(history_file)

    history_bom_df['总数量'] = pd.to_numeric(
        history_bom_df['总数量'], errors='coerce'
    )
    history_bom_df['物料描述'] = history_bom_df['物料描述'].astype(str)

    results = []
    # Check each material type in standards
    for matierial_type in standards_df['machine_type'].unique():
        standards_df_filtered = standards_df[
            standards_df['machine_type'] == matierial_type
        ]['material_detail'].tolist()
        escaped_patterns = [re.escape(item) for item in standards_df_filtered]
        is_contains = history_bom_df['物料描述'].str.contains(
            '|'.join(escaped_patterns), na=False, regex=True
        )
        history_bom_df_filtered = history_bom_df[is_contains]
        history_matierial_count = history_bom_df_filtered['总数量'].sum()
        bom_matierial_count = bom_df[
            bom_df['物料描述'].str.contains(
                '|'.join(escaped_patterns), na=False, regex=True
            )
        ]['总数量'].sum()
        results.append(
            {
                '物料种类': matierial_type,
                f'{current_erp}物料数量(当前)': bom_matierial_count,
                f'{history_erp}物料数量(历史)': history_matierial_count,
                '物料差异': bom_matierial_count - history_matierial_count,
            }
        )

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)

    # Define highlight function
    def highlight_row(row):
        if row['物料差异'] > 0:
            return ['background-color: red'] * len(row)
        if row['物料差异'] < 0:
            return ['background-color: yellow'] * len(row)
        return [''] * len(row)

    # Apply styling and save to Excel
    styled_df = results_df.style.apply(highlight_row, axis=1)
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        styled_df.to_excel(writer, index=False)


def run(
    bom_file, history_file, standards_df, output_path, current_erp, history_erp
):
    analyze_bom(
        bom_file,
        history_file,
        standards_df,
        f'{output_path}/4电气必备物料对比历史BOM.xlsx',
        current_erp,
        history_erp,
    )

    return (
        {
            'type': 'bomhistorycheck',
            'path': f'{output_path}/4电气必备物料对比历史BOM.xlsx',
        },
    )


if __name__ == '__main__':
    # Example usage
    machine_type = 'EV普通卷绕机'
    output_file = 'BOM分析结果.xlsx'
    analyze_bom_file = 'tests/37074投料信息.xlsx'
    history_bom_file = 'tests/37130投料信息.xlsx'
    materials_file = 'tests/bom_machine_type_material_info.csv'
    materials_df = pd.read_csv(materials_file)
    # materials_df['数量'] = pd.to_numeric(materials_df['数量'], errors='coerce')
    # materials_df['物料编码'] = materials_df['物料编码'].astype(str)

    analyze_bom(
        analyze_bom_file,
        history_bom_file,
        materials_df,
        output_file,
        '37074',
        '37130',
    )

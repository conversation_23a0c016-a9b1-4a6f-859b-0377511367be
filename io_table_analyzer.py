import pandas as pd


def merge_df(io_df, eplan_df):
    result_df = pd.DataFrame(columns=['IO点位', '中文名称', 'EPLAN中文名称', '异常'])

    # 按IO点位分组处理
    io_grouped = io_df.groupby('IO点位')

    for io_point, io_group in io_grouped:
        eplan_row = eplan_df[eplan_df['IO点位'] == io_point]

        if eplan_row.empty:
            # IO点位在eplan中不存在，所有行都标记为异常
            for index, row in io_group.iterrows():
                result_df = pd.concat(
                    [
                        result_df,
                        pd.DataFrame(
                            {
                                'IO点位': [row['IO点位']],
                                '中文名称': [row['中文名称']],
                                '异常': [1],
                            }
                        ),
                    ]
                )
        else:
            # 检查是否包含"开门请求按钮"
            has_door_request = any(
                '开门请求按钮' in str(desc)
                for desc in io_group['中文名称']
                if pd.notna(desc)
            )

            if has_door_request:
                # 特殊处理：只要有一个能匹配上就行，其他多余行不写入结果表
                matched = False
                matched_row = None

                # 先找是否有匹配的行
                for index, row in io_group.iterrows():
                    if row['中文名称'] in eplan_row['EPLAN中文名称'].values:
                        matched = True
                        matched_row = row
                        break

                if matched:
                    # 找到匹配的，只添加这一行到结果，标记为正常
                    result_df = pd.concat(
                        [
                            result_df,
                            pd.DataFrame(
                                {
                                    'IO点位': [matched_row['IO点位']],
                                    '中文名称': [matched_row['中文名称']],
                                    'EPLAN中文名称': [matched_row['中文名称']],
                                    '异常': [0],
                                }
                            ),
                        ]
                    )
                else:
                    # 没有匹配的，所有行都添加到异常行中
                    for index, row in io_group.iterrows():
                        result_df = pd.concat(
                            [
                                result_df,
                                pd.DataFrame(
                                    {
                                        'IO点位': [row['IO点位']],
                                        '中文名称': [row['中文名称']],
                                        'EPLAN中文名称': [
                                            eplan_row.iloc[0]['EPLAN中文名称']
                                        ],
                                        '异常': [1],
                                    }
                                ),
                            ]
                        )
            else:
                # 不包含"开门请求按钮"的字符串按正常处理
                for index, row in io_group.iterrows():
                    if row['中文名称'] in eplan_row['EPLAN中文名称'].values:
                        if len(eplan_row) == 1:
                            result_df = pd.concat(
                                [
                                    result_df,
                                    pd.DataFrame(
                                        {
                                            'IO点位': [row['IO点位']],
                                            '中文名称': [row['中文名称']],
                                            'EPLAN中文名称': [row['中文名称']],
                                            '异常': [0],
                                        }
                                    ),
                                ]
                            )
                        else:
                            result_df = pd.concat(
                                [
                                    result_df,
                                    pd.DataFrame(
                                        {
                                            'IO点位': [row['IO点位']],
                                            '中文名称': [row['中文名称']],
                                            'EPLAN中文名称': [row['中文名称']],
                                            '异常': [0],
                                        }
                                    ),
                                ]
                            )
                            eplan_row_without_same_name = eplan_row[
                                eplan_row['EPLAN中文名称'] != row['中文名称']
                            ]
                            if not eplan_row_without_same_name.empty:
                                result_df = pd.concat(
                                    [
                                        result_df,
                                        pd.DataFrame(
                                            {
                                                'IO点位': eplan_row_without_same_name[
                                                    'IO点位'
                                                ],
                                                'EPLAN中文名称': eplan_row_without_same_name[
                                                    'EPLAN中文名称'
                                                ],
                                                '异常': [1]
                                                * len(
                                                    eplan_row_without_same_name
                                                ),
                                            }
                                        ),
                                    ]
                                )
                            if (
                                len(eplan_row)
                                - len(eplan_row_without_same_name)
                                > 1
                            ):
                                count = (
                                    len(eplan_row)
                                    - len(eplan_row_without_same_name)
                                    - 1
                                )
                                result_df = pd.concat(
                                    [
                                        result_df,
                                        pd.DataFrame(
                                            {
                                                'IO点位': [row['IO点位']] * count,
                                                'EPLAN中文名称': [row['中文名称']]
                                                * count,
                                                '异常': [1] * count,
                                            }
                                        ),
                                    ]
                                )
                    else:
                        result_df = pd.concat(
                            [
                                result_df,
                                pd.DataFrame(
                                    {
                                        'IO点位': [row['IO点位']],
                                        '中文名称': [row['中文名称']],
                                        'EPLAN中文名称': [
                                            eplan_row.iloc[0]['EPLAN中文名称']
                                        ],
                                        '异常': [1],
                                    }
                                ),
                            ]
                        )

    # 处理eplan中存在但io表中不存在的IO点位
    processed_io_points = set(io_df['IO点位'].values)
    io_not_in_table = set(eplan_df['IO点位'].values) - processed_io_points

    if io_not_in_table:
        eplan_io = eplan_df[eplan_df['IO点位'].isin(io_not_in_table)][
            'IO点位'
        ].values
        eplan_descs = eplan_df[eplan_df['IO点位'].isin(io_not_in_table)][
            'EPLAN中文名称'
        ].values
        result_df = pd.concat(
            [
                result_df,
                pd.DataFrame(
                    {
                        'IO点位': eplan_io,
                        'EPLAN中文名称': eplan_descs,
                        '异常': [1] * len(eplan_io),
                    }
                ),
            ]
        )

    return result_df


def compare_io_points(eplan_df, io_df):
    # Drop the first 6 rows of eplan data as they are headers
    io_df['中文名称'] = io_df['中文名称'].apply(lambda x: x if pd.notna(x) else '')
    io_df['中文名称'] = io_df['中文名称'].apply(
        lambda x: x.replace('阴极', '正极').replace('阳极', '负极')
        if pd.notna(x)
        else ''
    )

    # Remove rows where the Chinese description contains "模块"
    eplan_df = eplan_df[~eplan_df['功能文本'].str.contains('模块', na=False)]
    eplan_df = eplan_df[eplan_df['功能文本'] != '文本说明']
    eplan_df = eplan_df.dropna(subset=['PLC地址'])

    # Create a new DataFrame with all IO points from eplan
    processed_eplan_df = pd.DataFrame()
    processed_eplan_df['IO点位'] = eplan_df['PLC地址']
    processed_eplan_df['EPLAN中文名称'] = eplan_df['功能文本'].apply(
        lambda x: x.replace('阴极', '正极').replace('阳极', '负极')
        if pd.notna(x)
        else ''
    )

    # Create a dictionary from manual file for easier lookup
    result_df = merge_df(io_df, processed_eplan_df).reset_index(drop=True)
    result_df['异常'] = result_df.apply(
        lambda x: 0
        if (x['中文名称'] == '' or pd.isna(x['中文名称']))
        and (x['EPLAN中文名称'] == '' or pd.isna(x['EPLAN中文名称']))
        else x['异常'],
        axis=1,
    )

    # Apply formatting
    def color_status(s):
        if s['异常'] == 1 and (s['中文名称'] == '' or pd.isna(s['中文名称'])):
            return ['background-color: red'] * len(s)
        elif s['异常'] == 1:
            return ['background-color: yellow'] * len(s)
        return [''] * len(s)

    # Style the DataFrame
    styled_df = result_df.style.apply(color_status, axis=1)

    return styled_df


def save_comparison(eplan_file, manual_file, output_file):
    """
    Compare IO points between EPLAN and manual files and save results to Excel

    Args:
        eplan_file: Path to EPLAN exported Excel file
        manual_file: Path to manually entered Excel file
        output_file: Path to save the comparison results
    """
    styled_df = compare_io_points(eplan_file, manual_file)

    # Save to Excel
    styled_df.to_excel(output_file, index=False)


def run(eplan_df, io_df, output_path):
    save_comparison(eplan_df, io_df, f'{output_path}/8Elpan图纸IO点位表校验.xlsx')

    return (
        {'type': 'iotable', 'path': f'{output_path}/8Elpan图纸IO点位表校验.xlsx'},
    )


if __name__ == '__main__':
    eplan_file = '/home/<USER>/code/bom-analysis/tests/38116/eplan-io.xlsx'
    output_file = 'tests/comparison.xlsx'
    from sqlalchemy import create_engine
    from core.config import settings

    engine = create_engine(settings.DATABASE_URL)

    # Read the eplan Excel file
    eplan_df = pd.read_excel(eplan_file)
    # Read the manual Excel file
    io_df = pd.read_sql_query(
        "SELECT * FROM io_version_info WHERE ERP='38116'",
        engine,
    )

    save_comparison(eplan_df, io_df, output_file)

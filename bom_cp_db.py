#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''=================================================
@Project -> File   ：pc_to_pi.py -> bom_cp_db
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2024/9/4 16:07
@Desc   ：
=================================================='''
# !/usr/bin/env python
# -*- coding: UTF-8 -*-
import re

import pandas as pd

ERP = 36615


# 提取部件号
def extract_second_part(value):
    match = re.search(r'^\d+-\d{2}', value)  # 匹配前面的数字-两个数字的格式
    if match:
        return value.split('-')[1]  # 返回第二个部分
    return ''  # 如果不匹配，返回空字符串


# 读取第一个Excel文件并处理
df = pd.read_excel(f'tests/{ERP}投料信息.xlsx')
df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
df_bom = df.loc[
    (df['物料类型'] == '外购件') & (df['物料描述'].str.contains('伺服电机', na=False))
]

# 使用 .loc 修改 '物料描述' 列和提取 '部件号'
df_bom.loc[:, '物料描述'] = (
    df_bom['物料描述']
    .str.replace('伺服电机_', '', regex=False)
    .str.replace(r'-XD.*', '', regex=True)
)
df_bom.loc[:, '部件编号'] = df_bom['父级'].apply(extract_second_part)
df_bom = df_bom.sort_values(by='部件编号', ascending=True).reset_index(drop=True)


# df_bom = df_bom.groupby('物料描述')['总数量'].sum().reset_index()
def summarize_bom(group):
    bom_summary = group.groupby('部件编号')['总数量'].sum().reset_index()
    return ', '.join(
        [f'{row["部件编号"]} ({row["总数量"]})' for _, row in bom_summary.iterrows()]
    )


df_bom = (
    df_bom.groupby('物料描述')
    .apply(
        lambda x: pd.Series({'总数量': x['总数量'].sum(), '部件号统计': summarize_bom(x)})
    )
    .reset_index()
)
# 查看结果

# 读取第二个Excel文件并处理
df1 = pd.read_excel(f'tests/{ERP}-机电沟通表.xlsx')
df1 = df1[~df1['EM中文'].str.contains('蛇形', na=False)]
df_db = df1[df1['类别'] == '伺服电机'][['型号', '部件编号']].rename(columns={'型号': '物料描述'})
df_db = (
    df_db.astype({'部件编号': 'int'})
    .sort_values(by='部件编号')
    .reset_index(drop=True)
    .astype({'部件编号': 'str'})
)
# df_db = df_db[['型号']].rename(columns={'型号': '物料描述'})
df_db['物料描述'] = (
    df_db['物料描述']
    .str.replace('伺服电机_', '', regex=False)
    .str.replace(r'-XD.*', '', regex=True)
)
df_db_1 = df_db.groupby('物料描述').size().reset_index(name='总数量')
# 统计每个物料描述对应的部件号数量
df_db_2 = (
    df_db.groupby('物料描述')['部件编号']
    .apply(
        lambda x: ', '.join(
            x.value_counts().index + ' (' + x.value_counts().astype(str) + ')'
        )
    )
    .reset_index(name='部件号统计')
)

# 合并两者
df_db = pd.merge(df_db_1, df_db_2, on='物料描述', how='left')
# 合并 df_bom 和 df_db
comparison_df = pd.merge(
    df_bom[['物料描述', '总数量', '部件号统计']],
    df_db[['物料描述', '总数量', '部件号统计']],
    on='物料描述',
    how='outer',
    suffixes=('_df_bom', '_df_db'),
)

# 用 NaN 值填充未匹配的数据
comparison_df.fillna(0, inplace=True)

# 计算数量差异
comparison_df['数量差异'] = (
    comparison_df['总数量_df_bom'] - comparison_df['总数量_df_db']
)


# 添加对比结果状态列
def compare_status(row):
    if row['数量差异'] > 0:
        return 'df_bom数量更多'
    elif row['数量差异'] < 0:
        return 'df_db数量更多'
    else:
        return '数量相等'


comparison_df['对比结果'] = comparison_df.apply(compare_status, axis=1)


# 修改格式化函数，只补齐一位数为两位数
def format_part_numbers(part_number_str):
    if isinstance(part_number_str, (int, float)):
        part_number_str = str(part_number_str)
    # 匹配1位或2位数的部件号，忽略三位数或更长的部件号
    return re.sub(
        r'\b(\d{1,2})\b', lambda m: m.group(1).zfill(2), part_number_str
    )


comparison_df['部件号统计_df_bom'] = comparison_df['部件号统计_df_bom'].apply(
    format_part_numbers
)
comparison_df['部件号统计_df_db'] = comparison_df['部件号统计_df_db'].apply(
    format_part_numbers
)


# 定义函数解析部件号统计字符串，并返回字典
def parse_part_numbers(part_number_str):
    if part_number_str == 0:
        return {}
    # 改进正则表达式，捕捉两位或三位的部件号，并确保数量解析正确
    parts = re.findall(r'(\d{2,3})\s?\((\d+)\)', part_number_str)
    return {part: int(count) for part, count in parts}


# 对比部件号及其数量差异
def compare_part_numbers(row):
    bom_parts = parse_part_numbers(row['部件号统计_df_bom'])
    db_parts = parse_part_numbers(row['部件号统计_df_db'])

    all_parts = sorted(
        set(bom_parts.keys()).union(db_parts.keys())
    )  # 获取所有部件号并排序
    diff_results = []

    for part in all_parts:
        bom_count = bom_parts.get(part, 0)
        db_count = db_parts.get(part, 0)
        diff = bom_count - db_count
        if diff != 0:
            diff_results.append(f'{part} (差异: {diff})')

    if diff_results:
        return ', '.join(diff_results)
    else:
        return '数量相等'


# 应用函数并生成比较结果
comparison_df['部件号对比结果'] = comparison_df.apply(compare_part_numbers, axis=1)

# 计算 df_bom 和 df_db 的数量总和，以及数量差异的总和
total_bom = comparison_df['总数量_df_bom'].sum()
total_db = comparison_df['总数量_df_db'].sum()
total_diff = comparison_df['数量差异'].sum()

# 将汇总统计添加到 DataFrame 中
summary_row = pd.DataFrame(
    {
        '物料描述': ['总数'],
        '总数量_df_bom': [total_bom],
        '总数量_df_db': [total_db],
        '数量差异': [total_diff],
        '对比结果': ['汇总'],
    }
)

# 将汇总行附加到 comparison_df
comparison_df = pd.concat([comparison_df, summary_row], ignore_index=True)


# Step 7: 高亮差异较大的物料描述
def highlight_differences(s):
    if s['物料描述'] == '总数':
        return ['background-color: lightblue'] * len(s)  # 对汇总行进行不同的高亮
    elif abs(s['数量差异']) > 0:
        return ['background-color: yellow'] * len(s)
    else:
        return [''] * len(s)


comparison_df_styled = comparison_df.style.apply(highlight_differences, axis=1)

# 导出到 Excel
comparison_df_styled.to_excel(
    f'tests/{ERP}comparison_result_with_summary_new.xlsx',
    index=False,
)

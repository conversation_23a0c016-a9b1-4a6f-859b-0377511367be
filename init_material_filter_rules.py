#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
初始化物料过滤规则表
从 bom_parts_count_by_line_daily.py 中的 read_and_clean_bom_motor 函数提取过滤规则
"""

from core.database import SessionLocal, init_db
from models import MaterialFilterRule
from db_operations import create_material_filter_rule


def extract_filter_rules_from_code():
    """
    从代码中提取过滤规则
    基于 bom_parts_count_by_line_daily.py 中的 read_and_clean_bom_motor 函数
    """

    # 排除规则（is_included=False）
    exclude_rules = [
        '虚拟件',
        '读码器传感器',
        # '光纤放大',  # 注释掉的规则
        # '微动开关传感器_',  # 注释掉的规则
    ]

    # 包含规则（is_included=True）
    include_rules = [
        '伺服电机',
        '气缸_',
        '传感器_',
        '磁性开关_',
        '导轨制动器_',
        '接近开关_',
        '电气比例阀_',
        '光纤放大器',
        '放大器_',
    ]

    # 特殊排除的物料编码（这些可以作为描述规则）
    special_exclude_codes = ['801010500049', '801010200018']

    return exclude_rules, include_rules, special_exclude_codes


def init_material_filter_rules():
    """初始化物料过滤规则表"""

    # 确保数据库表已创建
    init_db()

    # 获取数据库会话
    db = SessionLocal()

    try:
        # 检查是否已有数据
        existing_count = (
            db.query(MaterialFilterRule)
            .filter(MaterialFilterRule.is_active == True)
            .count()
        )

        if existing_count > 0:
            print(f'数据库中已存在 {existing_count} 条物料过滤规则，跳过初始化')
            return

        (
            exclude_rules,
            include_rules,
            special_exclude_codes,
        ) = extract_filter_rules_from_code()

        created_count = 0

        # 添加排除规则
        print('添加排除规则...')
        for rule in exclude_rules:
            try:
                create_material_filter_rule(
                    db=db, description=rule, is_included=False
                )
                print(f'  ✅ 添加排除规则: {rule}')
                created_count += 1
            except ValueError as e:
                print(f'  ❌ 跳过重复规则: {rule} - {e}')
            except Exception as e:
                print(f'  ❌ 添加规则失败: {rule} - {e}')

        # 添加包含规则
        print('添加包含规则...')
        for rule in include_rules:
            try:
                create_material_filter_rule(
                    db=db, description=rule, is_included=True
                )
                print(f'  ✅ 添加包含规则: {rule}')
                created_count += 1
            except ValueError as e:
                print(f'  ❌ 跳过重复规则: {rule} - {e}')
            except Exception as e:
                print(f'  ❌ 添加规则失败: {rule} - {e}')

        # 添加特殊排除的物料编码
        print('添加特殊排除的物料编码...')
        for code in special_exclude_codes:
            try:
                create_material_filter_rule(
                    db=db, description=f'物料编码:{code}', is_included=False
                )
                print(f'  ✅ 添加排除物料编码: {code}')
                created_count += 1
            except ValueError as e:
                print(f'  ❌ 跳过重复规则: {code} - {e}')
            except Exception as e:
                print(f'  ❌ 添加规则失败: {code} - {e}')

        print(f'\n🎉 初始化完成！共创建了 {created_count} 条物料过滤规则')

        # 显示创建的规则统计
        include_count = (
            db.query(MaterialFilterRule)
            .filter(
                MaterialFilterRule.is_included == True,
                MaterialFilterRule.is_active == True,
            )
            .count()
        )

        exclude_count = (
            db.query(MaterialFilterRule)
            .filter(
                MaterialFilterRule.is_included == False,
                MaterialFilterRule.is_active == True,
            )
            .count()
        )

        print(f'📊 统计信息:')
        print(f'  - 包含规则: {include_count} 条')
        print(f'  - 排除规则: {exclude_count} 条')
        print(f'  - 总计: {include_count + exclude_count} 条')

    except Exception as e:
        print(f'❌ 初始化失败: {e}')
        db.rollback()
        raise
    finally:
        db.close()


def show_current_rules():
    """显示当前的所有过滤规则"""

    # 获取数据库会话
    db = SessionLocal()

    try:
        # 获取所有活跃规则
        all_rules = (
            db.query(MaterialFilterRule)
            .filter(MaterialFilterRule.is_active == True)
            .order_by(
                MaterialFilterRule.is_included.desc(),
                MaterialFilterRule.description,
            )
            .all()
        )

        if not all_rules:
            print('❌ 数据库中没有找到任何物料过滤规则')
            return

        print('📋 当前物料过滤规则:')
        print('=' * 80)

        # 分组显示
        include_rules = [rule for rule in all_rules if rule.is_included]
        exclude_rules = [rule for rule in all_rules if not rule.is_included]

        print(f'✅ 包含规则 ({len(include_rules)} 条):')
        for rule in include_rules:
            print(f'  {rule.id:3d}. {rule.description}')

        print(f'\n❌ 排除规则 ({len(exclude_rules)} 条):')
        for rule in exclude_rules:
            print(f'  {rule.id:3d}. {rule.description}')

        print('=' * 80)
        print(f'总计: {len(all_rules)} 条规则')

    except Exception as e:
        print(f'❌ 查询失败: {e}')
    finally:
        db.close()


if __name__ == '__main__':
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'show':
        show_current_rules()
    else:
        print('🚀 开始初始化物料过滤规则表...')
        init_material_filter_rules()
        print('\n📋 显示当前规则:')
        show_current_rules()

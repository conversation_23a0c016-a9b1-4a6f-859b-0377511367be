# 首先在顶部添加计算类型说明的常量字典
CALCULATION_TYPE_DESCRIPTIONS = {
    'notFed': {
        # 'drive': {
        #     'title': '驱动器数量统计',
        #     'description': '''通过比对投料BOM表和机电沟通表中的驱动器数量是否一致。主要步骤：
        # 1. 从BOM表提取名称为"伺服驱动器"或"伺服控制器"的记录
        # 2. 从机电沟通表中提取伺服电机对应的驱动器型号（通过mech_motor_details映射关系）
        # 3. 清理型号中的后缀（如-XD）并统计各型号数量
        # 4. 对比两表中相同型号的数量差异''',
        #     'error_definition': '驱动器在两个表中的总数量差异的绝对值（|BOM表总数 - 机电沟通表总数|）',
        #     'total_definition': 'BOM表中的驱动器总数量（汇总所有型号数量）',
        #     'key_fields': [
        #         '物料描述 - 驱动器型号',
        #         '总数量_df_bom - BOM表中的数量',
        #         '总数量_df_db - 机电沟通表中的数量',
        #         '数量差异 - BOM表数量减去机电沟通表数量',
        #         '部件号统计 - 机电沟通表中的部件编号及对应数量',
        #         '对比结果 - 标识哪个表的数量更多',
        #     ],
        #     'additional_info': {
        #         'output_files': [
        #             '驱动器对比结果_图纸_机电沟通表.xlsx - 主要对比结果文件',
        #             'drive_motor_missing.txt - 记录找不到对应驱动器的电机型号',
        #         ],
        #         'special_handling': [
        #             '排除包含"蛇形"的记录',
        #             '通过映射表获取电机对应的驱动器型号',
        #             '支持一个电机对应多个驱动器型号（通过/分隔）',
        #             '高亮显示有数量差异的记录',
        #         ],
        #     },
        # }
    },
    'alreadyFed': {
        'motor': {
            'title': '1电机比对_BOM_机电沟通表',
            'description': '''通过逐行比对投料信息和机电沟通表中的伺服电机数量是否一致。主要步骤：
        1. 从BOM表提取物料类型为"外购件"且物料描述包含"伺服电机"的记录
        2. 从机电沟通表提取类别为"伺服电机"的记录
        3. 按照"物料描述-部件编号"进行配对比较
        4. 逐行核对，并标记异常情况：
           - 机电沟通表有而BOM表没有（标红）
           - BOM表有而机电沟通表没有（标黄）
           - 同型号电机在两表中数量不一致''',
            'error_definition': '''异常数量的计算标准：
        1. 物料描述为空（BOM表缺失）计为一个异常
        2. BOM表物料描述为空（机电沟通表缺失）计为一个异常
        每行缺失都单独计算，最终异常数为所有异常行数之和''',
            'total_definition': '所有比对的电机条目总数（包括机电沟通表和BOM表的所有记录）',
            'key_fields': [
                '物料描述 - 机电沟通表中的电机型号',
                '部件编号 - 电机所属的部件编号（两位数字格式）',
                'EM中文 - 机电沟通表中的中文描述',
                '功能描述 - 机电沟通表中的功能说明',
                'BOM表物料描述 - BOM表中的电机型号',
                '异常 - 标记是否存在不匹配（1表示异常，0表示正常）',
            ],
            'additional_info': {
                'output_files': [
                    '1电机比对_BOM_机电沟通表.xlsx - 包含所有比对结果的详细表格，使用颜色标记异常'
                ],
                'special_handling': [
                    '排除包含"蛇形"的记录',
                    '清理型号中的后缀（如-XD）',
                    '将部件编号统一格式化为两位数字（补零）',
                    '红色高亮表示机电沟通表独有记录',
                    '黄色高亮表示BOM表独有记录',
                ],
                'data_cleaning': [
                    '移除物料描述中的"伺服电机_"前缀',
                    '部件编号从"所属部件"列提取',
                    '对重复记录按部件编号和物料描述合并并汇总数量',
                ],
            },
        },
        'drive': {
            'title': '2驱动器对比_BOM_机电沟通表',
            'description': '''通过对比驱动器在BOM表和机电沟通表中的数量差异进行核对。主要步骤：
1. 从BOM表提取物料类型为"外购件"且物料描述包含"伺服驱动器"的记录
2. 从机电沟通表通过以下步骤获取驱动器信息：
   - 提取伺服电机记录
   - 通过mech_motor_details映射表获取对应的驱动器型号
   - 支持一个电机对应多个驱动器(通过/分隔)
3. 汇总两表中各型号的数量并比较差异
4. 特别处理了电机与驱动器的对应关系缺失问题''',
            'error_definition': '''差异计算方式：
1. 计算BOM表数量与机电沟通表数量的差值
2. 总差异 = BOM表总数 - 机电沟通表总数
3. 异常行用黄色高亮标记
4. 汇总行用浅蓝色标记''',
            'total_definition': 'BOM表中的驱动器总数量（按物料描述汇总）',
            'key_fields': [
                '物料描述 - 驱动器型号',
                '总数量_df_bom - BOM表中的数量',
                '总数量_df_db - 机电沟通表中的数量',
                '数量差异 - BOM表数量减去机电沟通表数量',
                '部件号统计 - 机电沟通表中包含该驱动器的部件编号及数量',
                '对比结果 - 标识数量差异的状态(df_bom数量更多/df_db数量更多/数量相等)',
            ],
            'additional_info': {
                'output_files': [
                    '2驱动器对比_BOM_机电沟通表.xlsx - 包含所有型号的数量对比结果',
                    'drive_motor_missing.txt - 记录在映射表中找不到对应驱动器的电机型号',
                ],
                'special_handling': [
                    '排除包含"蛇形"的记录',
                    '清理型号中的后缀（如-XD）',
                    '清理驱动器型号中的MIT.前缀',
                    '特别处理了电机型号与多个驱动器型号的映射关系',
                    '对于机电沟通表中出现但BOM表中没有的驱动器型号也进行统计',
                ],
                'data_processing': [
                    '按物料描述分组汇总BOM表中的数量',
                    '通过映射关系将电机转换为对应的驱动器型号',
                    '统计各驱动器型号在不同部件中的分布情况',
                    '计算并标记差异，包括添加汇总行',
                ],
            },
        },
        'cable': {
            'title': '3电缆比对_BOM_机电沟通表',
            'description': '''通过比对伺服电机电缆在BOM表和机电沟通表中的一致性。主要步骤：
1. 分别对比动力线和编码器线：
   - 从BOM表提取物料类型为"外购件"的电缆记录
   - 通过mech_motor_details映射获取每个电机对应的标准/柔性电缆型号
   - 根据是否过拖链选择对应的电缆类型
2. 电缆型号提取规则处理三种格式：
   - S6-xx-xx 格式
   - HCXD/SVCAB/CAB前缀格式
   - MR前缀格式（含括号）
3. 按部件编号和物料描述进行一一对应配对''',
            'error_definition': '''异常判定标准：
1. 机电沟通表有而BOM表没有的电缆（标红）
2. BOM表有而机电沟通表没有的电缆（标黄）
3. 缺少动力线或编码器线的记录
4. 异常值为1表示存在不匹配，0表示完全匹配''',
            'total_definition': '所有比对的电缆记录总数（包括动力线和编码器线）',
            'key_fields': [
                '伺服电机物料描述 - 电机型号',
                '部件编号 - 电机所属的部件编号',
                'EM中文 - 电机的中文描述',
                '功能描述 - 电机的功能说明',
                '动力线物料描述 - 机电沟通表中的动力线型号',
                '编码器线物料描述 - 机电沟通表中的编码器线型号',
                'BOM表动力线物料描述 - BOM表中的动力线型号',
                'BOM表编码器线物料描述 - BOM表中的编码器线型号',
            ],
            'additional_info': {
                'output_files': [
                    '3电缆比对_BOM_机电沟通表.xlsx - 详细的比对结果，包含每个电机的动力线和编码器线信息',
                    'cable_motor_missing.txt - 记录找不到对应电缆型号的电机列表',
                ],
                'special_handling': [
                    '支持电缆正反向出线的处理',
                    '处理刹车线（通过+分隔）',
                    '自动识别电缆型号格式并提取',
                    '根据是否过拖链选择柔性/普通电缆',
                    '支持一个电机对应多种电缆规格的情况',
                ],
                'data_cleaning': [
                    '清理型号中的长度信息（如 -5M）',
                    '处理特殊格式的型号（如括号内容）',
                    '规范化电缆型号的格式',
                    '去除前缀（如MIT.）',
                    '分别维护动力线和编码器线的型号列表',
                ],
            },
        },
        'bomhistorycheck': {
            'title': '4电气必备物料对比历史BOM',
            'description': '''通过比对当前BOM表与历史BOM表中的电气必备物料配置情况。主要步骤：
1. 数据预处理：
   - 清理并转换数量字段为数值类型
   - 确保物料描述字段为字符串类型
2. 按物料种类分组统计：
   - 根据标准物料要求进行分类
   - 分别统计当前和历史BOM中各类物料的总数量
3. 差异分析：
   - 计算每类物料的数量差异
   - 生成对比结果报告''',
            'error_definition': '''差异计算标准：
1. 物料差异 = 当前BOM数量 - 历史BOM数量
2. 差异标记：
   - 红色高亮表示当前数量多于历史数量
   - 黄色高亮表示当前数量少于历史数量
   - 无颜色表示数量相等''',
            'total_definition': '所有对比物料的总数量（包括当前BOM和历史BOM的总和）',
            'key_fields': [
                '物料种类 - 标准物料分类',
                '当前物料数量 - 当前BOM中的数量',
                '历史物料数量 - 历史BOM中的数量',
                '物料差异 - 当前与历史数量的差值',
            ],
            'additional_info': {
                'output_files': ['4电气必备物料对比历史BOM.xlsx - 包含所有物料种类的对比结果'],
                'special_handling': [
                    '支持不同ERP编号的对比',
                    '自动处理数值类型转换',
                    '使用颜色标记差异情况',
                ],
                'data_processing': [
                    '按物料种类分组统计数量',
                    '计算并标记数量差异',
                    '生成差异分析报告',
                    '支持批量数据处理',
                ],
            },
        },
        'electricalMaterials': {
            'title': '5电气投料对比结果_图纸_BOM',
            'description': '''通过比对预投料表(BOOM)和正式投料表中的电气物料情况。主要步骤：
1. 预投料表(BOOM)处理：
   - 清理物料编码前导单引号
   - 清理型号和数量的前导单引号
   - 按物料编码汇总，保留第一个型号和名称
2. 正式投料表处理：
   - 仅处理特定区域的物料：
     * 父级包含"-999"的记录
     * 父级包含"-800-"的记录
     * 父级包含"-801-"的记录
   - 按物料编码汇总数量和描述
3. 数据对比：
   - 基于物料编码进行匹配
   - 计算每个物料的数量差异''',
            'error_definition': '''差异计算标准：
1. 数量差异 = 预投料数量 - 投料总数量
2. 差异标记：
   - 黄色高亮表示数量不一致
   - 浅蓝色标记表示汇总行
3. 对比结果分类：
   - 预投料数量更多
   - 投料数量更多
   - 数量相等''',
            'total_definition': '所有对比物料的总数量（包括预投料和正式投料的总和）',
            'key_fields': [
                '物料编码 - 唯一标识符',
                '预投料名称 - BOOM表中的物料名称',
                '预投料型号 - BOOM表中的物料型号',
                '预投料数量 - BOOM表中的数量',
                '投料物料描述 - 正式投料表中的物料描述',
                '投料总数量 - 正式投料表中的数量',
                '数量差异 - 预投料与正式投料的数量差值',
                '对比结果 - 数量差异的状态说明',
            ],
            'additional_info': {
                'output_files': ['5电气投料对比结果_图纸_BOM.xlsx - 包含所有物料的对比结果'],
                'special_handling': [
                    '自动清理数据中的前导单引号',
                    '支持数值类型的自动转换',
                    '按特定父级代码筛选电气物料',
                    '保留物料的首次出现的描述信息',
                ],
                'data_processing': [
                    '对预投料和正式投料分别按物料编码汇总',
                    '自动填充缺失数据为0',
                    '计算并标记数量差异',
                    '生成汇总统计信息',
                ],
                'key_areas': {
                    'material_selection': ['999区域物料', '800区域物料', '801区域物料']
                },
            },
        },
        #         'mechcheck': {
        #             'title': '6机械投料校验_BOM_机电沟通表',
        #             'description': '''通过比对BOM表和机电沟通表中的机械安全类物料配置情况。主要步骤：
        # 1. BOM表处理：
        #    - 提取物料描述和部件编号信息
        #    - 使用正则提取部件编号的二位数字格式
        #    - 按物料描述和部件编号分组汇总数量
        # 2. 机电沟通表处理：
        #    - 筛选指定类别的记录（安全门/按钮/按钮盒/仪器/传感器/阀岛等）
        #    - 标准化部件编号格式（补零处理）
        #    - 提取EM中文和功能描述信息
        # 3. 数据对比：
        #    - 基于"物料描述-部件编号"进行配对比较
        #    - 处理一对多和多对一的映射关系
        #    - 标记异常情况并生成汇总报告''',
        #             'error_definition': '''异常判定标准：
        # 1. 机电沟通表有而BOM表没有的物料（标红）
        # 2. BOM表有而机电沟通表没有的物料（标黄）
        # 3. 相同物料在两表中的数量不一致
        # 4. 异常值为1表示存在不匹配，0表示完全匹配''',
        #             'total_definition': '所有比对的机械物料记录总数（包括两个表的所有记录）',
        #             'key_fields': [
        #                 '类别 - 物料类别（安全门/按钮等）',
        #                 '物料描述 - 机电沟通表中的物料型号',
        #                 '部件编号 - 统一格式的两位数字编号',
        #                 'EM中文 - 机电沟通表中的中文描述',
        #                 '功能描述 - 机电沟通表中的功能说明',
        #                 'BOM表物料描述 - BOM表中的物料型号',
        #                 '异常 - 标记是否存在不匹配（1表示异常，0表示正常）',
        #             ],
        #             'additional_info': {
        #                 'output_files': ['6机械投料校验_BOM_机电沟通表.xlsx - 包含所有机械物料的详细对比结果'],
        #                 'special_handling': [
        #                     '部件编号统一格式化为两位数字（补零）',
        #                     '支持部件号从"所属部件"列提取',
        #                     '红色高亮表示机电沟通表独有记录',
        #                     '黄色高亮表示BOM表独有记录',
        #                 ],
        #                 'data_processing': [
        #                     '使用正则表达式提取标准格式的部件号',
        #                     '合并重复记录并汇总数量',
        #                     '逐行比对并标记异常情况',
        #                     '处理数量不一致的情况',
        #                 ],
        #                 'validation_rules': [
        #                     '物料类别必须在预定义列表中',
        #                     '部件编号必须为两位数字格式',
        #                     '相同物料描述的记录需要合并',
        #                     '异常情况需要用颜色标记',
        #                 ],
        #             },
        #         },
        'externalMaterials': {
            'title': '7柜外物料对比_BOM_柜外清单',
            'description': '''通过比对柜外物料清单和BOM投料表中的物料配置情况。主要步骤：
1. 柜外物料清单处理：
   - 提取存货编码、存货名称和总数量信息
   - 清理空值记录
   - 按物料编码汇总数量，保留首条物料描述
2. BOM投料表处理：
   - 获取物料描述、总数量和物料编码
   - 按物料编码汇总投料数量
   - 保留每个物料的首条描述信息
3. 数据对比：
   - 基于物料编码进行匹配
   - 计算每个物料的数量差异''',
            'error_definition': '''差异计算标准：
1. 数量差异 = 物料清单数量 - 投料总数量
2. 差异标记：
   - 黄色高亮表示数量不一致
   - 浅蓝色标记表示汇总行
3. 对比结果分类：
   - 物料清单数量更多
   - 投料数量更多
   - 数量相等''',
            'total_definition': '所有对比物料的总数量（包括物料清单和BOM投料的总和）',
            'key_fields': [
                '物料编码 - 存货编码，作为匹配键',
                '物料描述 - 柜外物料清单中的存货名称',
                '物料清单数量 - 柜外物料清单中的数量',
                '投料物料描述 - BOM表中的物料描述',
                '投料总数量 - BOM表中的投料数量',
                '数量差异 - 物料清单与BOM投料的数量差值',
                '对比结果 - 数量差异的状态说明',
            ],
            'additional_info': {
                'output_files': ['7柜外物料对比_BOM_柜外清单.xlsx - 包含所有物料的对比结果，带有差异标记'],
                'special_handling': [
                    '自动处理物料编码数据类型转换',
                    '清理数据中的空值记录',
                    '保留物料的首次出现的描述信息',
                    '自动汇总相同编码的物料数量',
                ],
                'data_processing': [
                    '对物料清单和BOM投料分别按物料编码汇总',
                    '自动填充缺失数据为0',
                    '计算并标记数量差异',
                    '生成汇总统计信息',
                ],
                'validation_rules': [
                    '物料编码不能为空',
                    '数量必须为数值类型',
                    '相同物料编码的数量需要汇总',
                    '保留首次出现的物料描述作为标准描述',
                ],
            },
        },
        'ioTable': {
            'title': '8Elpan图纸IO点位表校验',
            'description': '''通过比对EPLAN图纸和IO点位表中的IO点位信息是否一致。主要步骤：
1. EPLAN数据处理：
   - 移除前6行表头信息
   - 过滤掉包含"模块"的中文描述行
   - 移除"文本说明"行
   - 确保IO点位不为空
2. IO表数据处理：
   - 提取IO点位和中文名称
   - 与EPLAN数据进行匹配比对
3. 对比规则：
   - 检查IO点位是否存在于两个表中
   - 验证中文描述的一致性
   - 标记重复和异常情况''',
            'error_definition': '''异常判定标准：
1. IO点位在EPLAN中存在但IO表中不存在（标红）
2. IO点位存在但中文描述不一致（标黄）
3. IO点位出现重复
4. 异常值为1表示存在不匹配，0表示完全匹配''',
            'total_definition': '所有比对的IO点位总数（包括EPLAN和IO表中的所有记录）',
            'key_fields': [
                'IO点位 - IO点位编号',
                '中文名称 - IO表中的点位描述',
                'EPLAN中文名称 - EPLAN图纸中的点位描述',
                '异常 - 标记是否存在不匹配（1表示异常，0表示正常）',
            ],
            'additional_info': {
                'output_files': ['8Elpan图纸IO点位表校验.xlsx - 包含所有IO点位的比对结果'],
                'special_handling': [
                    '排除包含"模块"的记录',
                    '自动处理空值情况',
                    '支持多重匹配处理',
                    '使用颜色标记不同类型的异常',
                ],
                'data_processing': [
                    '清理EPLAN数据中的无效行',
                    '处理重复IO点位的情况',
                    '标记并突出显示异常记录',
                    '生成汇总统计信息',
                ],
            },
        },
        'airline': {
            'title': '9航插公母头匹配检查表',
            'description': '''通过比较BOM表与航插数据库表，检查公母头数量是否匹配。主要步骤：
        1. 数据库连接：
        - 从航插数据库获取所有连接器配对关系
        - 包含公头编码、公头名称、母头编码、母头名称信息
        2. BOM数据处理：
        - 提取部件编号（从"所属部件"字段分割获取）
        - 将总数量转换为数值类型
        - 按物料描述排序
        3. 配对匹配：
        - 根据数据库中的公母头配对关系
        - 在BOM表中查找对应的公头和母头数量
        - 计算每对连接器的数量差异
        4. 异常检测：
        - 识别公母头数量不匹配的情况
        - 过滤掉在BOM中完全不存在的连接器对''',
            'error_definition': '''异常判定标准：
        1. 数量差异 = 公头数量 - 母头数量
        2. 异常条件：数量差异不等于0
        3. 异常标记：
        - 红色高亮表示公母头数量不匹配
        - 正值表示公头数量多于母头
        - 负值表示母头数量多于公头
        4. 统计维度：不匹配的连接器对数量''',
            'total_definition': '所有在BOM表中存在的连接器对总数（排除BOM中完全不存在的配对）',
            'key_fields': [
                '公头编码 - 公头连接器的物料编码',
                '公头名称 - 公头连接器的物料名称',
                '公头数量 - BOM表中公头的总数量',
                '母头编码 - 母头连接器的物料编码',
                '母头名称 - 母头连接器的物料名称',
                '母头数量 - BOM表中母头的总数量',
                '数量差异 - 公头数量减去母头数量的差值',
            ],
            'additional_info': {
                'output_files': ['9航插公母头匹配检查表.xlsx - 包含所有连接器配对的数量对比结果'],
                'special_handling': [
                    '从数据库动态获取最新的航插配对关系',
                    '自动提取部件编号（通过"-"分割所属部件字段）',
                    '过滤掉BOM中完全不存在的连接器对',
                    '红色高亮标记数量不匹配的记录',
                    '支持大批量数据处理（limit=10000）',
                ],
                'data_processing': [
                    '将BOM表总数量字段转换为数值类型',
                    '按物料编码在BOM中查找对应的公母头',
                    '汇总相同物料编码的数量',
                    '计算每对连接器的数量差异',
                    '应用条件格式突出显示异常记录',
                ],
                'database_operations': [
                    '连接MySQL数据库获取航插配对数据',
                    '使用SQLAlchemy ORM进行数据库操作',
                    '自动管理数据库会话的创建和关闭',
                    '支持可配置的数据库连接字符串',
                ],
                'validation_rules': [
                    '连接器配对关系必须来自数据库',
                    'BOM中的数量必须为有效数值',
                    '公母头编码不能为空',
                    '只处理在BOM中至少有一方存在的连接器对',
                ],
            },
        },
    },
}

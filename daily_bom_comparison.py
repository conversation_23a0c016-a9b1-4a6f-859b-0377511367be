#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import os
import shutil
import logging
from logging.handlers import RotatingFileHandler
import requests
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import re
from openpyxl import load_workbook
from typing import List, Dict, Optional, Tuple
from rdm_api import RDMBomTracker

from routers.results import copy_first_sheet_with_formatting

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler(
            'daily_bom_comparison.log',
            maxBytes=1024 * 1024,  # 1MB 大小限制
            backupCount=5,  # 保留5个备份文件
        ),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger('daily_bom_comparison')

from models import (
    Base,
    DailyPartsErrorCount,
    DailyPartsCalculationResult,
    DailyPartsUploadedFile,
)
from bom_parts_count_by_line_daily import (
    run as run_bom_parts_count_by_line,
)
from bom_two_compare import run_bom_comparison

# 数据库配置
DATABASE_URL = 'mysql+pymysql://root:leadchina@10.30.200.215:3306/IO'


def cleanup_old_data(db_session, days_to_keep: int = 3) -> Dict:
    """
    清理数据库和文件系统中的旧数据
    根据指定的保留天数，清理旧数据库记录和结果文件夹，提高系统性能并节省存储空间。
    直接基于日期清理文件夹，而非逐个文件删除，提高效率。

    Args:
        db_session: SQLAlchemy 数据库会话
        days_to_keep: 数据保留天数（默认：3天）

    Returns:
        包含清理统计信息的字典
    """
    logger = logging.getLogger('daily_bom_comparison')
    logger.info(f'开始清理{days_to_keep}天前的数据')

    # 计算截止日期
    cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
    cutoff_date_str = cutoff_date.strftime('%Y%m%d')

    # 初始化统计信息
    stats = {
        'db_records_deleted': 0,
        'folders_deleted': 0,
        'error_count': 0,
    }

    try:
        # 第1步：清理数据库记录
        stats = cleanup_database_records(db_session, cutoff_date, stats)

        # 第2步：直接清理日期文件夹（基于日期而非单个文件）
        stats = cleanup_date_folders(cutoff_date_str, stats)

        logger.info(
            f"清理完成: {stats['db_records_deleted']}条数据库记录已删除, "
            f"{stats['folders_deleted']}个文件夹已删除, "
            f"{stats['error_count']}个错误发生"
        )

    except Exception as e:
        logger.error(f'清理过程中出错: {str(e)}')
        db_session.rollback()
        stats['error_count'] += 1

    return stats


def cleanup_database_records(db_session, cutoff_date, stats):
    """
    删除数据库中的旧记录
    Args:
        db_session: SQLAlchemy 数据库会话
        cutoff_date: 删除此日期之前的记录
        stats: 统计信息字典

    Returns:
        更新后的统计信息字典
    """
    logger = logging.getLogger('daily_bom_comparison')

    # 清理 DailyPartsErrorCount
    deleted_count = (
        db_session.query(DailyPartsErrorCount)
        .filter(DailyPartsErrorCount.create_date <= cutoff_date)
        .delete(synchronize_session=False)
    )
    stats['db_records_deleted'] += deleted_count
    logger.info(f'已删除 DailyPartsErrorCount 中的 {deleted_count} 条旧记录')

    # 清理 DailyPartsCalculationResult
    deleted_count = (
        db_session.query(DailyPartsCalculationResult)
        .filter(DailyPartsCalculationResult.calculation_date <= cutoff_date)
        .delete(synchronize_session=False)
    )
    stats['db_records_deleted'] += deleted_count
    logger.info(f'已删除 DailyPartsCalculationResult 中的 {deleted_count} 条旧记录')

    # 清理 DailyPartsUploadedFile
    deleted_count = (
        db_session.query(DailyPartsUploadedFile)
        .filter(DailyPartsUploadedFile.upload_date <= cutoff_date)
        .delete(synchronize_session=False)
    )
    stats['db_records_deleted'] += deleted_count
    logger.info(f'已删除 DailyPartsUploadedFile 中的 {deleted_count} 条旧记录')

    # 提交事务
    db_session.commit()

    return stats


def cleanup_date_folders(cutoff_date_str, stats):
    """
    直接清理截止日期之前的日期文件夹
    Args:
        cutoff_date_str: 截止日期字符串，格式为 YYYYMMDD
        stats: 统计信息字典

    Returns:
        更新后的统计信息字典
    """
    logger = logging.getLogger('daily_bom_comparison')
    base_dir = 'daily_results'

    if not os.path.exists(base_dir):
        return stats

    # 遍历 daily_results 下的所有 ERP 文件夹
    for erp_dir in os.listdir(base_dir):
        erp_path = os.path.join(base_dir, erp_dir)
        if not os.path.isdir(erp_path):
            continue

        # 遍历 ERP 下的所有部件文件夹
        for part_dir in os.listdir(erp_path):
            part_path = os.path.join(erp_path, part_dir)
            if not os.path.isdir(part_path):
                continue

            # 遍历部件下的所有日期文件夹
            date_dirs_to_remove = []
            for date_dir in os.listdir(part_path):
                # 检查是否是日期格式的文件夹（YYYYMMDD）
                if (
                    len(date_dir) == 8
                    and date_dir.isdigit()
                    and date_dir <= cutoff_date_str
                ):
                    date_dirs_to_remove.append(
                        os.path.join(part_path, date_dir)
                    )

            # 删除旧的日期文件夹
            for date_path in date_dirs_to_remove:
                try:
                    shutil.rmtree(date_path)
                    stats['folders_deleted'] += 1
                    logger.info(f'已删除日期文件夹: {date_path}')
                except Exception as e:
                    logger.error(f'删除文件夹 {date_path} 时出错: {str(e)}')
                    stats['error_count'] += 1

            # 如果部件文件夹为空，删除它
            if not os.listdir(part_path):
                try:
                    os.rmdir(part_path)
                    logger.info(f'已删除空的部件文件夹: {part_path}')
                except Exception as e:
                    logger.error(f'删除文件夹 {part_path} 时出错: {str(e)}')

        # 如果 ERP 文件夹为空，删除它
        if not os.listdir(erp_path):
            try:
                os.rmdir(erp_path)
                logger.info(f'已删除空的 ERP 文件夹: {erp_path}')
            except Exception as e:
                logger.error(f'删除文件夹 {erp_path} 时出错: {str(e)}')

    return stats


def create_engine_and_session():
    """创建数据库引擎和会话"""
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # 如果表不存在，创建表
    Base.metadata.create_all(bind=engine)

    return engine, SessionLocal()


def extract_second_part(value):
    """从字符串中提取部件号"""
    if pd.isna(value) or not isinstance(value, str):
        return ''

    match = re.search(r'^\d+-\d{2}', value)
    if match:
        return value.split('-')[1]  # 返回第二部分
    return ''  # 如果不匹配，返回空字符串


def get_active_erp_projects(db_session) -> List[Dict]:
    """从数据库中获取所有需要比对的活跃ERP项目

    返回包含ERP编号及其对应反机ERP（如果有）的字典列表
    """
    try:
        query = text(
            """
            SELECT
                ERP,
                second_erp,
                pcode,
                ncode,
                `类别` as category,
                mech_time_end
            FROM erp_base_info
            """
        )

        result = db_session.execute(query)
        projects = []
        current_time = datetime.now()

        for row in result:
            # 如果mech_time_end未设置或超过180天，则跳过
            if not row.mech_time_end:
                continue

            mech_end_time = row.mech_time_end
            try:
                days_since_mech_end = (current_time - mech_end_time).days
            except:
                days_since_mech_end = (
                    current_time.date() - mech_end_time
                ).days

            if days_since_mech_end > 180 or days_since_mech_end < 0:
                continue

            # 处理second_erp值
            second_erp = row.second_erp
            reverse_erp = None

            if second_erp and isinstance(second_erp, str):
                second_erp = second_erp.strip()
                if second_erp and second_erp != '/' and second_erp != '无':
                    # 如果有多个值，取第一个
                    if '/' in second_erp:
                        reverse_erp = second_erp.split('/')[0].strip()
                    else:
                        reverse_erp = second_erp

            # 处理second_erp值
            ncode = row.ncode
            reverse_material_code = None

            if ncode and isinstance(ncode, str):
                ncode = ncode.strip()
                if ncode and ncode != '/' and ncode != '无':
                    # 如果有多个值，取第一个
                    if '/' in ncode:
                        reverse_material_code = ncode.split('/')[0].strip()
                    else:
                        reverse_material_code = ncode

            projects.append(
                {
                    'erp_number': row.ERP,
                    'reverse_erp': reverse_erp,
                    'material_code': row.pcode,
                    'reverse_material_code': reverse_material_code,
                    'category': row.category,
                }
            )

        return projects
    except Exception as e:
        logger.error(f'获取ERP项目时出错: {str(e)}')
        return []


def extract_part_numbers_from_bom(df_bom: pd.DataFrame) -> List[str]:
    """从BOM数据框中提取所有唯一的部件号"""
    try:
        # 确保'所属部件'列存在
        if '所属部件' not in df_bom.columns:
            logger.warning("BOM数据中找不到'所属部件'列")
            return []

        # 提取部件号
        df_bom['部件编号'] = df_bom['所属部件'].apply(extract_second_part)

        # 获取唯一的非空部件号
        part_numbers = df_bom['部件编号'].dropna().unique().tolist()
        part_numbers = [part for part in part_numbers if part]

        return part_numbers
    except Exception as e:
        logger.error(f'从BOM提取部件号时出错: {str(e)}')
        return []


def get_part_numbers(db_session, erp_number) -> List[str]:
    """从BOM数据框中提取所有唯一的部件号"""
    try:
        query = text(
            """
            SELECT
                partNumber
            FROM inner_erp_framework_list
            WHERE erp = :erp_number AND is_delete = 0
        """
        )

        result = db_session.execute(query, {'erp_number': erp_number})
        data = result.fetchall()

        return [part for part, in data if part.isdigit()]
    except Exception as e:
        logger.error(f'从BOM提取部件号时出错: {str(e)}')
        return []


def get_db_data_for_erp(db_session, erp_number: str) -> pd.DataFrame:
    """获取特定ERP的所有机电沟通表数据"""
    try:
        query = text(
            """
            SELECT
                id AS `序号`,
                componentEngineer AS `部件工程师`,
                revisionDate AS `修订日期`,
                CASE
                    WHEN realPart IS NOT NULL AND realPart <> '' AND realPart REGEXP '^[0-9]+$' THEN realPart
                    ELSE partNumber
                END AS `部件编号`,
                amplifier AS `放大器`,
                emCN AS `EM中文`,
                emName AS `EM名称`,
                deviceIdentifier AS `设备标识符`,
                functionDescription AS `功能描述`,
                category AS `类别`,
                brand AS `品牌`,
                model AS `型号`,
                airInterface AS `气接口`,
                initialPosition AS `初始位`,
                valveModel AS `阀型号`,
                valveIslandOrSingleValve AS `阀岛/单片阀`,
                singleOrDoubleControl AS `单/双控`,
                extensionDetection AS `伸位检测`,
                retractionDetection AS `缩位检测`,
                note1 AS `备注1`,
                note2 AS `备注2`,
                positiveLimit AS `正.极限`,
                negativeLimit AS `负.极限`,
                origin AS `原点`,
                passDragChain AS `是否过拖链`,
                reductionRatio AS `减速比`,
                `lead` AS `导程`,
                rollerDiameter AS `辊径`,
                motorRotationDirection AS `电机旋转方向`,
                inertiaRatio AS `惯量比`
            FROM inner_erp_mech_info
            WHERE ERP = :erp_number AND is_delete = 0 AND is_reserve != 1
        """
        )

        result = db_session.execute(query, {'erp_number': erp_number})
        data = result.fetchall()

        # 将查询结果转换为DataFrame
        df = pd.DataFrame(data)

        if df.empty:
            # 如果inner_erp_mech_info中没有数据，尝试erp_mech_info
            query = text(
                """
                SELECT
                    id AS `序号`,
                    componentEngineer AS `部件工程师`,
                    revisionDate AS `修订日期`,
                    CASE
                        WHEN realPart IS NOT NULL AND realPart <> '' AND realPart REGEXP '^[0-9]+$' THEN realPart
                        ELSE partNumber
                    END AS `部件编号`,
                    amplifier AS `放大器`,
                    emCN AS `EM中文`,
                    emName AS `EM名称`,
                    deviceIdentifier AS `设备标识符`,
                    functionDescription AS `功能描述`,
                    category AS `类别`,
                    brand AS `品牌`,
                    model AS `型号`,
                    airInterface AS `气接口`,
                    initialPosition AS `初始位`,
                    valveModel AS `阀型号`,
                    valveIslandOrSingleValve AS `阀岛/单片阀`,
                    singleOrDoubleControl AS `单/双控`,
                    extensionDetection AS `伸位检测`,
                    retractionDetection AS `缩位检测`,
                    note1 AS `备注1`,
                    note2 AS `备注2`,
                    positiveLimit AS `正.极限`,
                    negativeLimit AS `负.极限`,
                    origin AS `原点`,
                    passDragChain AS `是否过拖链`,
                    reductionRatio AS `减速比`,
                    `lead` AS `导程`,
                    rollerDiameter AS `辊径`,
                    motorRotationDirection AS `电机旋转方向`,
                    inertiaRatio AS `惯量比`
                FROM erp_mech_info
                WHERE ERP = :erp_number
            """
            )

            result = db_session.execute(query, {'erp_number': erp_number})
            data = result.fetchall()
            df = pd.DataFrame(data)

        # 标准化部件编号格式
        if not df.empty and '部件编号' in df.columns:
            df['部件编号'] = df['部件编号'].apply(
                lambda x: str(int(x)).zfill(2)
                if isinstance(x, (int, float))
                else str(x).zfill(2)
                if x
                else ''
            )

        return df
    except Exception as e:
        logger.error(f'获取ERP {erp_number}的机电沟通表数据时出错: {str(e)}')
        return pd.DataFrame()


def get_bom_data_for_erp(
    erp_number: str, material_code: str
) -> Optional[pd.DataFrame]:
    """通过API获取特定ERP号的BOM数据"""
    if material_code == '131100300042':
        return pd.read_excel(
            '/home/<USER>/code/bom-analysis/tests/37807/37807投料BOM20250409.xlsx'
        )

    if material_code == '131100300041':
        return pd.read_excel(
            '/home/<USER>/code/bom-analysis/tests/37807/37807投料BOM20250409.xlsx'
        )

    try:
        # 创建RDM BOM查询工具实例
        tracker = RDMBomTracker()

        # 输入要查询的物料编码
        root_inv_code = material_code
        factory_code = '1020'  # 固定使用1020工厂编码
        wbs_project_number = ''  # 固定使用空WBS项目号

        # 开始查询BOM（限制为两层查询）
        results = tracker.query_bom(
            root_inv_code, factory_code, wbs_project_number
        )

        if results:
            return tracker.get_bom_dataframe()

    except requests.RequestException as e:
        logger.error(f'API请求ERP {erp_number}的BOM数据时出错: {str(e)}')
        return None
    except Exception as e:
        logger.error(f'处理ERP {erp_number}的BOM数据时出错: {str(e)}')
        return None


def add_daily_parts_calculation_result(
    db_session,
    erp_number: str,
    part_id: str,
    result_file_path: str,
) -> DailyPartsCalculationResult:
    """添加每日部件计算结果记录"""
    try:
        today = datetime.now().date()

        # 检查今天是否已存在记录
        existing_result = (
            db_session.query(DailyPartsCalculationResult)
            .filter(
                DailyPartsCalculationResult.erp_number == erp_number,
                DailyPartsCalculationResult.part_id == part_id,
                DailyPartsCalculationResult.calculation_date == today,
            )
            .first()
        )

        if existing_result:
            # 更新现有记录
            existing_result.result_file_path = result_file_path
            existing_result.calculation_time = datetime.now()
            db_session.commit()
            return existing_result
        else:
            # 创建新记录
            new_result = DailyPartsCalculationResult(
                erp_number=erp_number,
                part_id=part_id,
                result_file_path=result_file_path,
                calculation_date=today,
            )
            db_session.add(new_result)
            db_session.commit()
            return new_result
    except Exception as e:
        db_session.rollback()
        logger.error(f'添加每日计算结果时出错: {str(e)}')
        raise


def add_daily_parts_error_count(
    db_session,
    erp_number: str,
    part_id: str,
    error_count: int,
    total_count: int,
    cal_type: str = '',
) -> DailyPartsErrorCount:
    """添加每日部件错误计数记录"""
    try:
        today = datetime.now().date()

        # 检查今天是否已存在记录
        if cal_type:
            existing_count = (
                db_session.query(DailyPartsErrorCount)
                .filter(
                    DailyPartsErrorCount.erp_number == erp_number,
                    DailyPartsErrorCount.part_id == part_id,
                    DailyPartsErrorCount.cal_type == cal_type,
                    DailyPartsErrorCount.create_date == today,
                )
                .first()
            )
        else:
            existing_count = (
                db_session.query(DailyPartsErrorCount)
                .filter(
                    DailyPartsErrorCount.erp_number == erp_number,
                    DailyPartsErrorCount.part_id == part_id,
                    DailyPartsErrorCount.cal_type != 'reverse',
                    DailyPartsErrorCount.create_date == today,
                )
                .first()
            )

        if existing_count:
            # 更新现有记录
            existing_count.error_count = error_count
            existing_count.total_count = total_count
            existing_count.create_time = datetime.now()
            db_session.commit()
            return existing_count
        else:
            # 创建新记录
            new_count = DailyPartsErrorCount(
                erp_number=erp_number,
                part_id=part_id,
                error_count=error_count,
                total_count=total_count,
                cal_type=cal_type,
                create_date=today,
            )
            db_session.add(new_count)
            db_session.commit()
            return new_count
    except Exception as e:
        db_session.rollback()
        logger.error(f'添加每日错误计数时出错: {str(e)}')
        raise


def add_daily_parts_uploaded_file(
    db_session,
    erp_number: str,
    part_id: str,
    file_path: str,
    file_name: str,
    file_size: int,
) -> DailyPartsUploadedFile:
    """添加每日部件上传文件记录"""
    try:
        today = datetime.now().date()

        # 检查今天是否已存在记录
        existing_file = (
            db_session.query(DailyPartsUploadedFile)
            .filter(
                DailyPartsUploadedFile.erp_number == erp_number,
                DailyPartsUploadedFile.part_id == part_id,
                DailyPartsUploadedFile.upload_date == today,
            )
            .first()
        )

        if existing_file:
            # 更新现有记录
            existing_file.file_path = file_path
            existing_file.file_name = file_name
            existing_file.file_size = file_size
            existing_file.upload_time = datetime.now()
            db_session.commit()
            return existing_file
        else:
            # 创建新记录
            new_file = DailyPartsUploadedFile(
                erp_number=erp_number,
                part_id=part_id,
                file_path=file_path,
                file_name=file_name,
                file_size=file_size,
                upload_date=today,
            )
            db_session.add(new_file)
            db_session.commit()
            return new_file
    except Exception as e:
        db_session.rollback()
        logger.error(f'添加每日上传文件时出错: {str(e)}')
        raise


def process_erp_part_comparison(
    db_session,
    erp_number: str,
    part_number: str,
    df_bom: pd.DataFrame,
    df_db: pd.DataFrame,
    reverse_results: dict,
) -> bool:
    """处理特定ERP和部件号的比对"""
    try:
        part_number = str(part_number).zfill(2)
        # 创建结果目录
        date_str = datetime.now().strftime('%Y%m%d')
        result_dir = os.path.join(
            'daily_results', erp_number, part_number, date_str
        )
        os.makedirs(result_dir, exist_ok=True)

        # 过滤当前部件号的DB数据
        df_db_part = df_db[df_db['部件编号'] == part_number].copy()

        if df_db_part.empty:
            logger.warning(f'找不到ERP {erp_number}，部件 {part_number}的DB数据')
            return False

        # 处理BOM部件计数比对
        (
            bom_result_file,
            error_count,
            total_count,
        ) = run_bom_parts_count_by_line(
            df_bom, df_db_part, part_number, result_dir
        )

        # 添加错误计数到每日统计
        add_daily_parts_error_count(
            db_session, erp_number, part_number, error_count, total_count
        )

        # 创建输出文件
        output_path = os.path.join(result_dir, 'result.xlsx')

        results = [bom_result_file]
        if reverse_results.get(part_number):
            results.append(reverse_results[part_number])

        # 创建新的Excel文件
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for index, result_file_path in enumerate(results):
                if result_file_path and result_file_path.endswith('.xlsx'):
                    # 读取原始Excel文件，保留格式
                    original_wb = load_workbook(result_file_path)

                    # 使用文件名（不包括扩展名）作为sheet名
                    sheet_name = os.path.splitext(
                        os.path.basename(result_file_path)
                    )[0]

                    # 如果sheet名重复，添加序号
                    if sheet_name in writer.book.sheetnames:
                        sheet_name = f'{sheet_name}_{index}'

                    # 复制第一个sheet到新的workbook，保留格式
                    copy_first_sheet_with_formatting(
                        original_wb, writer.book, sheet_name
                    )

            # 删除默认创建的Sheet
            if 'Sheet' in writer.book.sheetnames:
                del writer.book['Sheet']

        # 添加计算结果
        add_daily_parts_calculation_result(
            db_session, erp_number, part_number, output_path
        )

        logger.info(f'成功处理ERP {erp_number}，部件 {part_number}')
        return True
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f'处理ERP {erp_number}，部件 {part_number}时出错: {str(e)}')
        return False


def process_bom_comparison(
    db_session,
    erp_number: str,
    reverse_erp_number: str,
    bom_df: pd.DataFrame,
    reverse_bom_df: pd.DataFrame,
    is_ev: bool = False,
) -> Tuple[int, int, dict]:
    """处理两个BOM对比，适用于所有部件号"""
    total_processed = 0
    successful_processed = 0
    result_files = {}
    date_str = datetime.now().strftime('%Y%m%d')

    try:
        # 从两个BOM中提取所有部件号
        part_numbers_main = extract_part_numbers_from_bom(bom_df)
        part_numbers_reverse = extract_part_numbers_from_bom(reverse_bom_df)

        # 合并唯一部件号
        part_numbers = list(set(part_numbers_main + part_numbers_reverse))

        logger.info(f'在两个BOM中共找到{len(part_numbers)}个唯一部件号')

        # 处理每个部件号
        for part_number in part_numbers:
            result_dir = os.path.join(
                'daily_results', erp_number, part_number, date_str
            )
            os.makedirs(result_dir, exist_ok=True)

            try:
                # 运行BOM比对
                (
                    reverse_result_file,
                    reverse_error_count,
                    reverse_total_count,
                ) = run_bom_comparison(
                    bom_df, reverse_bom_df, part_number, result_dir, is_ev
                )

                # 添加反机错误计数到每日统计
                add_daily_parts_error_count(
                    db_session,
                    erp_number,
                    part_number,
                    reverse_error_count,
                    reverse_total_count,
                    'reverse',
                )

                # # 添加计算结果
                # add_daily_parts_calculation_result(
                #     db_session, erp_number, part_number, reverse_result_file
                # )
                result_files[part_number] = reverse_result_file

                successful_processed += 1
                logger.info(f'成功处理部件{part_number}的BOM比对')
            except Exception as e:
                logger.error(f'处理部件{part_number}的BOM比对时出错: {str(e)}')

            total_processed += 1

        return successful_processed, total_processed, result_files
    except Exception as e:
        logger.error(f'BOM比对过程中出错: {str(e)}')
        return successful_processed, total_processed, result_files


def run_daily_comparison(target_erp=None):
    """
    运行每日比对的主函数

    Args:
        target_erp (str, optional): 指定要比对的ERP编号，如果不传则处理所有活跃ERP
    """
    engine, db_session = create_engine_and_session()

    try:
        # 获取所有需要比对的活跃ERP项目
        projects = get_active_erp_projects(db_session)
        logger.info(f'找到{len(projects)}个需要比对的活跃ERP项目')

        if not projects:
            logger.warning('未找到需要比对的活跃ERP项目')
            return

        # 如果指定了target_erp，过滤出对应的项目
        if target_erp:
            projects = [p for p in projects if p['erp_number'] == target_erp]
            if not projects:
                logger.warning(f'未找到指定的ERP项目: {target_erp}')
                return
            logger.info(f'指定处理ERP: {target_erp}')
        else:
            logger.info('处理所有活跃ERP项目')

        # 处理每个ERP项目
        for project in projects:
            erp_number = project['erp_number']
            # 如果没有指定target_erp，保留原有的过滤逻辑
            if not target_erp and erp_number != '37807':
                continue
            reverse_erp = project['reverse_erp']
            material_code = project['material_code']
            reverse_material_code = project['reverse_material_code']
            if not material_code:
                continue

            logger.info(f'处理ERP {erp_number} (反机ERP: {reverse_erp})')

            # 获取此ERP的BOM数据
            df_bom = get_bom_data_for_erp(erp_number, material_code)
            if df_bom is None:
                logger.warning(f'跳过ERP {erp_number}: 无BOM数据')
                continue

            # 从BOM提取所有部件号
            part_numbers = get_part_numbers(db_session, erp_number)
            logger.info(f'为ERP {erp_number}找到{len(part_numbers)}个唯一部件号')

            # 获取机电沟通表数据（一次性获取全部数据）
            df_db = get_db_data_for_erp(db_session, erp_number)
            if df_db.empty:
                logger.warning(f'跳过ERP {erp_number}: 无机电沟通表数据')
                continue

            # 获取反机BOM数据（如果有）
            df_reverse_bom = None
            if reverse_erp:
                df_reverse_bom = get_bom_data_for_erp(
                    erp_number, reverse_material_code
                )
                if df_reverse_bom is not None:
                    logger.info(f'成功加载ERP {reverse_erp}的反机BOM')
                else:
                    logger.warning(f'无法加载ERP {reverse_erp}的反机BOM')

            reverse_results = {}
            # 如果有反机BOM，处理所有部件号的BOM比对
            if df_reverse_bom is not None:
                successful, total, reverse_results = process_bom_comparison(
                    db_session,
                    erp_number,
                    reverse_erp,
                    df_bom,
                    df_reverse_bom,
                    'EV' in project['category'],
                )
                logger.info(
                    f'ERP {erp_number}与{reverse_erp}的BOM比对已完成: '
                    f'{successful}/{total}个部件成功处理'
                )

            # 处理机电沟通表与BOM的比对（对每个部件号）
            successful_parts = 0
            for part_number in part_numbers:
                success = process_erp_part_comparison(
                    db_session,
                    erp_number,
                    part_number,
                    df_bom,
                    df_db,
                    reverse_results,
                )
                if success:
                    successful_parts += 1

            for part_number in set(reverse_results.keys()) - set(
                [part_number.zfill(2) for part_number in part_numbers]
            ):
                add_daily_parts_calculation_result(
                    db_session,
                    erp_number,
                    part_number,
                    reverse_results[part_number],
                )

            logger.info(
                f'ERP {erp_number}的机电沟通表与BOM比对已完成: '
                f'{successful_parts}/{len(part_numbers)}个部件成功处理'
            )

        cleanup_old_data(db_session)

        logger.info('每日比对过程已完成')
    except Exception as e:
        logger.error(f'每日比对过程中出错: {str(e)}')
    finally:
        db_session.close()


if __name__ == '__main__':
    import sys

    # 检查是否有命令行参数指定ERP
    target_erp = None
    if len(sys.argv) > 1:
        target_erp = sys.argv[1]
        logger.info(f'开始指定ERP {target_erp} 的BOM比对过程')
    else:
        logger.info('开始每日BOM比对过程')

    run_daily_comparison(target_erp)
    logger.info('每日BOM比对过程已完成')

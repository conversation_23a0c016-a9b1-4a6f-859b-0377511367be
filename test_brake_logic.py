#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
测试刹车线异常检测逻辑
"""
import pandas as pd

def test_brake_logic():
    # 创建测试数据
    test_data = [
        # 情况1：都没有刹车线 - 正常
        {'刹车线物料描述': '', 'BOM表刹车线物料描述': '', '预期异常': 0, '描述': '都没有刹车线'},
        {'刹车线物料描述': None, 'BOM表刹车线物料描述': '', '预期异常': 0, '描述': '都没有刹车线(None)'},
        
        # 情况2：都有刹车线 - 正常
        {'刹车线物料描述': 'BRAKE-001', 'BOM表刹车线物料描述': 'BRAKE-001', '预期异常': 0, '描述': '都有刹车线'},
        
        # 情况3：机电沟通表有，BOM表没有 - 异常
        {'刹车线物料描述': 'BRAKE-001', 'BOM表刹车线物料描述': '', '预期异常': 1, '描述': '机电有，BOM没有'},
        {'刹车线物料描述': 'BRAKE-001', 'BOM表刹车线物料描述': None, '预期异常': 1, '描述': '机电有，BOM没有(None)'},
        
        # 情况4：机电沟通表没有，BOM表有 - 异常
        {'刹车线物料描述': '', 'BOM表刹车线物料描述': 'BRAKE-001', '预期异常': 1, '描述': '机电没有，BOM有'},
        {'刹车线物料描述': None, 'BOM表刹车线物料描述': 'BRAKE-001', '预期异常': 1, '描述': '机电没有(None)，BOM有'},
    ]
    
    df = pd.DataFrame(test_data)
    df['异常'] = 0  # 初始化异常列
    
    # 应用刹车线异常检测逻辑
    df['异常'] = df.apply(
        lambda row: 1
        if (
            # 刹车线异常：一个有一个没有的情况
            (
                # 情况1：机电沟通表有刹车线，但BOM表没有
                (row['刹车线物料描述'] != '' and not pd.isna(row['刹车线物料描述'])) 
                and (pd.isna(row['BOM表刹车线物料描述']) or row['BOM表刹车线物料描述'] == '')
            ) or (
                # 情况2：机电沟通表没有刹车线，但BOM表有
                (row['刹车线物料描述'] == '' or pd.isna(row['刹车线物料描述'])) 
                and (not pd.isna(row['BOM表刹车线物料描述']) and row['BOM表刹车线物料描述'] != '')
            )
        ) or row['异常'] == 1
        else 0,
        axis=1,
    )
    
    # 检查结果
    print("刹车线异常检测测试结果：")
    print("=" * 80)
    for idx, row in df.iterrows():
        result = "✓ 通过" if row['异常'] == row['预期异常'] else "✗ 失败"
        print(f"{result} | {row['描述']:<20} | 机电: {str(row['刹车线物料描述']):<10} | BOM: {str(row['BOM表刹车线物料描述']):<10} | 预期: {row['预期异常']} | 实际: {row['异常']}")
    
    # 统计结果
    passed = (df['异常'] == df['预期异常']).sum()
    total = len(df)
    print("=" * 80)
    print(f"测试结果：{passed}/{total} 通过")
    
    return passed == total

if __name__ == '__main__':
    test_brake_logic()
